// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RTSAIController.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeRTSAIController() {}

// ********** Begin Cross Module References ********************************************************
AIMODULE_API UClass* Z_Construct_UClass_AAIController();
ARMORWARS_API UClass* Z_Construct_UClass_ARTSAIController();
ARMORWARS_API UClass* Z_Construct_UClass_ARTSAIController_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_ARTSBaseActor_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSBehaviorNode_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSBehaviorTreeComponent_NoRegister();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSAIPriority();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSAIState();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSCombatBehavior();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSFormationType();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSAIController_OnAIStateChanged__DelegateSignature();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSAIController_OnEnemyDetected__DelegateSignature();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSAIController_OnTargetAcquired__DelegateSignature();
ARMORWARS_API UScriptStruct* Z_Construct_UScriptStruct_FRTSAITargetInfo();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
UPackage* Z_Construct_UPackage__Script_ArmorWars();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum ERTSAIState ***************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERTSAIState;
static UEnum* ERTSAIState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERTSAIState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERTSAIState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_ArmorWars_ERTSAIState, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("ERTSAIState"));
	}
	return Z_Registration_Info_UEnum_ERTSAIState.OuterSingleton;
}
template<> ARMORWARS_API UEnum* StaticEnum<ERTSAIState>()
{
	return ERTSAIState_StaticEnum();
}
struct Z_Construct_UEnum_ArmorWars_ERTSAIState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Attack.DisplayName", "Attack" },
		{ "Attack.Name", "ERTSAIState::Attack" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enum for AI states\n" },
#endif
		{ "Defend.DisplayName", "Defend" },
		{ "Defend.Name", "ERTSAIState::Defend" },
		{ "Follow.DisplayName", "Follow" },
		{ "Follow.Name", "ERTSAIState::Follow" },
		{ "FormationMove.DisplayName", "Formation Move" },
		{ "FormationMove.Name", "ERTSAIState::FormationMove" },
		{ "Idle.DisplayName", "Idle" },
		{ "Idle.Name", "ERTSAIState::Idle" },
		{ "Investigate.DisplayName", "Investigate" },
		{ "Investigate.Name", "ERTSAIState::Investigate" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
		{ "Patrol.DisplayName", "Patrol" },
		{ "Patrol.Name", "ERTSAIState::Patrol" },
		{ "Retreat.DisplayName", "Retreat" },
		{ "Retreat.Name", "ERTSAIState::Retreat" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enum for AI states" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERTSAIState::Idle", (int64)ERTSAIState::Idle },
		{ "ERTSAIState::Patrol", (int64)ERTSAIState::Patrol },
		{ "ERTSAIState::Attack", (int64)ERTSAIState::Attack },
		{ "ERTSAIState::Defend", (int64)ERTSAIState::Defend },
		{ "ERTSAIState::Follow", (int64)ERTSAIState::Follow },
		{ "ERTSAIState::Retreat", (int64)ERTSAIState::Retreat },
		{ "ERTSAIState::FormationMove", (int64)ERTSAIState::FormationMove },
		{ "ERTSAIState::Investigate", (int64)ERTSAIState::Investigate },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_ArmorWars_ERTSAIState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	"ERTSAIState",
	"ERTSAIState",
	Z_Construct_UEnum_ArmorWars_ERTSAIState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSAIState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSAIState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_ArmorWars_ERTSAIState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_ArmorWars_ERTSAIState()
{
	if (!Z_Registration_Info_UEnum_ERTSAIState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERTSAIState.InnerSingleton, Z_Construct_UEnum_ArmorWars_ERTSAIState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERTSAIState.InnerSingleton;
}
// ********** End Enum ERTSAIState *****************************************************************

// ********** Begin Enum ERTSAIPriority ************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERTSAIPriority;
static UEnum* ERTSAIPriority_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERTSAIPriority.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERTSAIPriority.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_ArmorWars_ERTSAIPriority, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("ERTSAIPriority"));
	}
	return Z_Registration_Info_UEnum_ERTSAIPriority.OuterSingleton;
}
template<> ARMORWARS_API UEnum* StaticEnum<ERTSAIPriority>()
{
	return ERTSAIPriority_StaticEnum();
}
struct Z_Construct_UEnum_ArmorWars_ERTSAIPriority_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enum for AI priorities\n" },
#endif
		{ "Critical.DisplayName", "Critical" },
		{ "Critical.Name", "ERTSAIPriority::Critical" },
		{ "High.DisplayName", "High" },
		{ "High.Name", "ERTSAIPriority::High" },
		{ "Low.DisplayName", "Low" },
		{ "Low.Name", "ERTSAIPriority::Low" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
		{ "Normal.DisplayName", "Normal" },
		{ "Normal.Name", "ERTSAIPriority::Normal" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enum for AI priorities" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERTSAIPriority::Low", (int64)ERTSAIPriority::Low },
		{ "ERTSAIPriority::Normal", (int64)ERTSAIPriority::Normal },
		{ "ERTSAIPriority::High", (int64)ERTSAIPriority::High },
		{ "ERTSAIPriority::Critical", (int64)ERTSAIPriority::Critical },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_ArmorWars_ERTSAIPriority_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	"ERTSAIPriority",
	"ERTSAIPriority",
	Z_Construct_UEnum_ArmorWars_ERTSAIPriority_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSAIPriority_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSAIPriority_Statics::Enum_MetaDataParams), Z_Construct_UEnum_ArmorWars_ERTSAIPriority_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_ArmorWars_ERTSAIPriority()
{
	if (!Z_Registration_Info_UEnum_ERTSAIPriority.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERTSAIPriority.InnerSingleton, Z_Construct_UEnum_ArmorWars_ERTSAIPriority_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERTSAIPriority.InnerSingleton;
}
// ********** End Enum ERTSAIPriority **************************************************************

// ********** Begin Enum ERTSCombatBehavior ********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERTSCombatBehavior;
static UEnum* ERTSCombatBehavior_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERTSCombatBehavior.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERTSCombatBehavior.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_ArmorWars_ERTSCombatBehavior, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("ERTSCombatBehavior"));
	}
	return Z_Registration_Info_UEnum_ERTSCombatBehavior.OuterSingleton;
}
template<> ARMORWARS_API UEnum* StaticEnum<ERTSCombatBehavior>()
{
	return ERTSCombatBehavior_StaticEnum();
}
struct Z_Construct_UEnum_ArmorWars_ERTSCombatBehavior_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Aggressive.DisplayName", "Aggressive" },
		{ "Aggressive.Name", "ERTSCombatBehavior::Aggressive" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enum for combat behaviors\n" },
#endif
		{ "CoverSeeking.DisplayName", "Cover Seeking" },
		{ "CoverSeeking.Name", "ERTSCombatBehavior::CoverSeeking" },
		{ "Defensive.DisplayName", "Defensive" },
		{ "Defensive.Name", "ERTSCombatBehavior::Defensive" },
		{ "Flanking.DisplayName", "Flanking" },
		{ "Flanking.Name", "ERTSCombatBehavior::Flanking" },
		{ "HitAndRun.DisplayName", "Hit and Run" },
		{ "HitAndRun.Name", "ERTSCombatBehavior::HitAndRun" },
		{ "Kiting.DisplayName", "Kiting" },
		{ "Kiting.Name", "ERTSCombatBehavior::Kiting" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
		{ "SupportFire.DisplayName", "Support Fire" },
		{ "SupportFire.Name", "ERTSCombatBehavior::SupportFire" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enum for combat behaviors" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERTSCombatBehavior::Aggressive", (int64)ERTSCombatBehavior::Aggressive },
		{ "ERTSCombatBehavior::Defensive", (int64)ERTSCombatBehavior::Defensive },
		{ "ERTSCombatBehavior::Kiting", (int64)ERTSCombatBehavior::Kiting },
		{ "ERTSCombatBehavior::Flanking", (int64)ERTSCombatBehavior::Flanking },
		{ "ERTSCombatBehavior::CoverSeeking", (int64)ERTSCombatBehavior::CoverSeeking },
		{ "ERTSCombatBehavior::SupportFire", (int64)ERTSCombatBehavior::SupportFire },
		{ "ERTSCombatBehavior::HitAndRun", (int64)ERTSCombatBehavior::HitAndRun },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_ArmorWars_ERTSCombatBehavior_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	"ERTSCombatBehavior",
	"ERTSCombatBehavior",
	Z_Construct_UEnum_ArmorWars_ERTSCombatBehavior_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSCombatBehavior_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSCombatBehavior_Statics::Enum_MetaDataParams), Z_Construct_UEnum_ArmorWars_ERTSCombatBehavior_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_ArmorWars_ERTSCombatBehavior()
{
	if (!Z_Registration_Info_UEnum_ERTSCombatBehavior.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERTSCombatBehavior.InnerSingleton, Z_Construct_UEnum_ArmorWars_ERTSCombatBehavior_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERTSCombatBehavior.InnerSingleton;
}
// ********** End Enum ERTSCombatBehavior **********************************************************

// ********** Begin ScriptStruct FRTSAITargetInfo **************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FRTSAITargetInfo;
class UScriptStruct* FRTSAITargetInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FRTSAITargetInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FRTSAITargetInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FRTSAITargetInfo, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("RTSAITargetInfo"));
	}
	return Z_Registration_Info_UScriptStruct_FRTSAITargetInfo.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FRTSAITargetInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Struct for AI target information\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Struct for AI target information" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Target_MetaData[] = {
		{ "Category", "AI Target" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// The target actor\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "The target actor" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Priority_MetaData[] = {
		{ "Category", "AI Target" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Priority of this target\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Priority of this target" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Distance_MetaData[] = {
		{ "Category", "AI Target" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Distance to target\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Distance to target" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ThreatLevel_MetaData[] = {
		{ "Category", "AI Target" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Threat level (0-1)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Threat level (0-1)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastSeenTime_MetaData[] = {
		{ "Category", "AI Target" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Last seen time\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Last seen time" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Distance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ThreatLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastSeenTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FRTSAITargetInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UScriptStruct_FRTSAITargetInfo_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0014000000000004, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSAITargetInfo, Target), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Target_MetaData), NewProp_Target_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FRTSAITargetInfo_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FRTSAITargetInfo_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSAITargetInfo, Priority), Z_Construct_UEnum_ArmorWars_ERTSAIPriority, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Priority_MetaData), NewProp_Priority_MetaData) }; // 2759675784
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSAITargetInfo_Statics::NewProp_Distance = { "Distance", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSAITargetInfo, Distance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Distance_MetaData), NewProp_Distance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSAITargetInfo_Statics::NewProp_ThreatLevel = { "ThreatLevel", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSAITargetInfo, ThreatLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ThreatLevel_MetaData), NewProp_ThreatLevel_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSAITargetInfo_Statics::NewProp_LastSeenTime = { "LastSeenTime", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSAITargetInfo, LastSeenTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastSeenTime_MetaData), NewProp_LastSeenTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FRTSAITargetInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSAITargetInfo_Statics::NewProp_Target,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSAITargetInfo_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSAITargetInfo_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSAITargetInfo_Statics::NewProp_Distance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSAITargetInfo_Statics::NewProp_ThreatLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSAITargetInfo_Statics::NewProp_LastSeenTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSAITargetInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FRTSAITargetInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	&NewStructOps,
	"RTSAITargetInfo",
	Z_Construct_UScriptStruct_FRTSAITargetInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSAITargetInfo_Statics::PropPointers),
	sizeof(FRTSAITargetInfo),
	alignof(FRTSAITargetInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSAITargetInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FRTSAITargetInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FRTSAITargetInfo()
{
	if (!Z_Registration_Info_UScriptStruct_FRTSAITargetInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FRTSAITargetInfo.InnerSingleton, Z_Construct_UScriptStruct_FRTSAITargetInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FRTSAITargetInfo.InnerSingleton;
}
// ********** End ScriptStruct FRTSAITargetInfo ****************************************************

// ********** Begin Delegate FOnAIStateChanged *****************************************************
struct Z_Construct_UDelegateFunction_ARTSAIController_OnAIStateChanged__DelegateSignature_Statics
{
	struct RTSAIController_eventOnAIStateChanged_Parms
	{
		ERTSAIState OldState;
		ERTSAIState NewState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldState;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_ARTSAIController_OnAIStateChanged__DelegateSignature_Statics::NewProp_OldState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_ARTSAIController_OnAIStateChanged__DelegateSignature_Statics::NewProp_OldState = { "OldState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventOnAIStateChanged_Parms, OldState), Z_Construct_UEnum_ArmorWars_ERTSAIState, METADATA_PARAMS(0, nullptr) }; // 1008175298
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_ARTSAIController_OnAIStateChanged__DelegateSignature_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_ARTSAIController_OnAIStateChanged__DelegateSignature_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventOnAIStateChanged_Parms, NewState), Z_Construct_UEnum_ArmorWars_ERTSAIState, METADATA_PARAMS(0, nullptr) }; // 1008175298
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_ARTSAIController_OnAIStateChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSAIController_OnAIStateChanged__DelegateSignature_Statics::NewProp_OldState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSAIController_OnAIStateChanged__DelegateSignature_Statics::NewProp_OldState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSAIController_OnAIStateChanged__DelegateSignature_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSAIController_OnAIStateChanged__DelegateSignature_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSAIController_OnAIStateChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_ARTSAIController_OnAIStateChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "OnAIStateChanged__DelegateSignature", Z_Construct_UDelegateFunction_ARTSAIController_OnAIStateChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSAIController_OnAIStateChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_ARTSAIController_OnAIStateChanged__DelegateSignature_Statics::RTSAIController_eventOnAIStateChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSAIController_OnAIStateChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_ARTSAIController_OnAIStateChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_ARTSAIController_OnAIStateChanged__DelegateSignature_Statics::RTSAIController_eventOnAIStateChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_ARTSAIController_OnAIStateChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_ARTSAIController_OnAIStateChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void ARTSAIController::FOnAIStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnAIStateChanged, ERTSAIState OldState, ERTSAIState NewState)
{
	struct RTSAIController_eventOnAIStateChanged_Parms
	{
		ERTSAIState OldState;
		ERTSAIState NewState;
	};
	RTSAIController_eventOnAIStateChanged_Parms Parms;
	Parms.OldState=OldState;
	Parms.NewState=NewState;
	OnAIStateChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnAIStateChanged *******************************************************

// ********** Begin Delegate FOnTargetAcquired *****************************************************
struct Z_Construct_UDelegateFunction_ARTSAIController_OnTargetAcquired__DelegateSignature_Statics
{
	struct RTSAIController_eventOnTargetAcquired_Parms
	{
		ARTSBaseActor* Target;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_ARTSAIController_OnTargetAcquired__DelegateSignature_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventOnTargetAcquired_Parms, Target), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_ARTSAIController_OnTargetAcquired__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSAIController_OnTargetAcquired__DelegateSignature_Statics::NewProp_Target,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSAIController_OnTargetAcquired__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_ARTSAIController_OnTargetAcquired__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "OnTargetAcquired__DelegateSignature", Z_Construct_UDelegateFunction_ARTSAIController_OnTargetAcquired__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSAIController_OnTargetAcquired__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_ARTSAIController_OnTargetAcquired__DelegateSignature_Statics::RTSAIController_eventOnTargetAcquired_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSAIController_OnTargetAcquired__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_ARTSAIController_OnTargetAcquired__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_ARTSAIController_OnTargetAcquired__DelegateSignature_Statics::RTSAIController_eventOnTargetAcquired_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_ARTSAIController_OnTargetAcquired__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_ARTSAIController_OnTargetAcquired__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void ARTSAIController::FOnTargetAcquired_DelegateWrapper(const FMulticastScriptDelegate& OnTargetAcquired, ARTSBaseActor* Target)
{
	struct RTSAIController_eventOnTargetAcquired_Parms
	{
		ARTSBaseActor* Target;
	};
	RTSAIController_eventOnTargetAcquired_Parms Parms;
	Parms.Target=Target;
	OnTargetAcquired.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnTargetAcquired *******************************************************

// ********** Begin Delegate FOnEnemyDetected ******************************************************
struct Z_Construct_UDelegateFunction_ARTSAIController_OnEnemyDetected__DelegateSignature_Statics
{
	struct RTSAIController_eventOnEnemyDetected_Parms
	{
		ARTSBaseActor* Enemy;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Enemy;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_ARTSAIController_OnEnemyDetected__DelegateSignature_Statics::NewProp_Enemy = { "Enemy", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventOnEnemyDetected_Parms, Enemy), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_ARTSAIController_OnEnemyDetected__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSAIController_OnEnemyDetected__DelegateSignature_Statics::NewProp_Enemy,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSAIController_OnEnemyDetected__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_ARTSAIController_OnEnemyDetected__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "OnEnemyDetected__DelegateSignature", Z_Construct_UDelegateFunction_ARTSAIController_OnEnemyDetected__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSAIController_OnEnemyDetected__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_ARTSAIController_OnEnemyDetected__DelegateSignature_Statics::RTSAIController_eventOnEnemyDetected_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSAIController_OnEnemyDetected__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_ARTSAIController_OnEnemyDetected__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_ARTSAIController_OnEnemyDetected__DelegateSignature_Statics::RTSAIController_eventOnEnemyDetected_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_ARTSAIController_OnEnemyDetected__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_ARTSAIController_OnEnemyDetected__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void ARTSAIController::FOnEnemyDetected_DelegateWrapper(const FMulticastScriptDelegate& OnEnemyDetected, ARTSBaseActor* Enemy)
{
	struct RTSAIController_eventOnEnemyDetected_Parms
	{
		ARTSBaseActor* Enemy;
	};
	RTSAIController_eventOnEnemyDetected_Parms Parms;
	Parms.Enemy=Enemy;
	OnEnemyDetected.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnEnemyDetected ********************************************************

// ********** Begin Class ARTSAIController Function AddKnownTarget *********************************
struct Z_Construct_UFunction_ARTSAIController_AddKnownTarget_Statics
{
	struct RTSAIController_eventAddKnownTarget_Parms
	{
		ARTSBaseActor* Target;
		ERTSAIPriority Priority;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Targeting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Target Management\n" },
#endif
		{ "CPP_Default_Priority", "Normal" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Target Management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSAIController_AddKnownTarget_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventAddKnownTarget_Parms, Target), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSAIController_AddKnownTarget_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSAIController_AddKnownTarget_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventAddKnownTarget_Parms, Priority), Z_Construct_UEnum_ArmorWars_ERTSAIPriority, METADATA_PARAMS(0, nullptr) }; // 2759675784
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_AddKnownTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_AddKnownTarget_Statics::NewProp_Target,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_AddKnownTarget_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_AddKnownTarget_Statics::NewProp_Priority,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_AddKnownTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_AddKnownTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "AddKnownTarget", Z_Construct_UFunction_ARTSAIController_AddKnownTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_AddKnownTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_AddKnownTarget_Statics::RTSAIController_eventAddKnownTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_AddKnownTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_AddKnownTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_AddKnownTarget_Statics::RTSAIController_eventAddKnownTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_AddKnownTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_AddKnownTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execAddKnownTarget)
{
	P_GET_OBJECT(ARTSBaseActor,Z_Param_Target);
	P_GET_ENUM(ERTSAIPriority,Z_Param_Priority);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddKnownTarget(Z_Param_Target,ERTSAIPriority(Z_Param_Priority));
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function AddKnownTarget ***********************************

// ********** Begin Class ARTSAIController Function BroadcastToGroup *******************************
struct Z_Construct_UFunction_ARTSAIController_BroadcastToGroup_Statics
{
	struct RTSAIController_eventBroadcastToGroup_Parms
	{
		FString Message;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Group" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Group coordination helpers\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Group coordination helpers" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Message_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Message;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_ARTSAIController_BroadcastToGroup_Statics::NewProp_Message = { "Message", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventBroadcastToGroup_Parms, Message), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Message_MetaData), NewProp_Message_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_BroadcastToGroup_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_BroadcastToGroup_Statics::NewProp_Message,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_BroadcastToGroup_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_BroadcastToGroup_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "BroadcastToGroup", Z_Construct_UFunction_ARTSAIController_BroadcastToGroup_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_BroadcastToGroup_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_BroadcastToGroup_Statics::RTSAIController_eventBroadcastToGroup_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_BroadcastToGroup_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_BroadcastToGroup_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_BroadcastToGroup_Statics::RTSAIController_eventBroadcastToGroup_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_BroadcastToGroup()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_BroadcastToGroup_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execBroadcastToGroup)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Message);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->BroadcastToGroup(Z_Param_Message);
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function BroadcastToGroup *********************************

// ********** Begin Class ARTSAIController Function CanSeeTarget ***********************************
struct Z_Construct_UFunction_ARTSAIController_CanSeeTarget_Statics
{
	struct RTSAIController_eventCanSeeTarget_Parms
	{
		ARTSBaseActor* Target;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Detection" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSAIController_CanSeeTarget_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventCanSeeTarget_Parms, Target), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_ARTSAIController_CanSeeTarget_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSAIController_eventCanSeeTarget_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSAIController_CanSeeTarget_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAIController_eventCanSeeTarget_Parms), &Z_Construct_UFunction_ARTSAIController_CanSeeTarget_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_CanSeeTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_CanSeeTarget_Statics::NewProp_Target,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_CanSeeTarget_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_CanSeeTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_CanSeeTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "CanSeeTarget", Z_Construct_UFunction_ARTSAIController_CanSeeTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_CanSeeTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_CanSeeTarget_Statics::RTSAIController_eventCanSeeTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_CanSeeTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_CanSeeTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_CanSeeTarget_Statics::RTSAIController_eventCanSeeTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_CanSeeTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_CanSeeTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execCanSeeTarget)
{
	P_GET_OBJECT(ARTSBaseActor,Z_Param_Target);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanSeeTarget(Z_Param_Target);
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function CanSeeTarget *************************************

// ********** Begin Class ARTSAIController Function CoordinateAttackWithGroup **********************
struct Z_Construct_UFunction_ARTSAIController_CoordinateAttackWithGroup_Statics
{
	struct RTSAIController_eventCoordinateAttackWithGroup_Parms
	{
		ARTSBaseActor* Target;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Combat" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSAIController_CoordinateAttackWithGroup_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventCoordinateAttackWithGroup_Parms, Target), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_CoordinateAttackWithGroup_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_CoordinateAttackWithGroup_Statics::NewProp_Target,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_CoordinateAttackWithGroup_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_CoordinateAttackWithGroup_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "CoordinateAttackWithGroup", Z_Construct_UFunction_ARTSAIController_CoordinateAttackWithGroup_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_CoordinateAttackWithGroup_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_CoordinateAttackWithGroup_Statics::RTSAIController_eventCoordinateAttackWithGroup_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_CoordinateAttackWithGroup_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_CoordinateAttackWithGroup_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_CoordinateAttackWithGroup_Statics::RTSAIController_eventCoordinateAttackWithGroup_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_CoordinateAttackWithGroup()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_CoordinateAttackWithGroup_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execCoordinateAttackWithGroup)
{
	P_GET_OBJECT(ARTSBaseActor,Z_Param_Target);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CoordinateAttackWithGroup(Z_Param_Target);
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function CoordinateAttackWithGroup ************************

// ********** Begin Class ARTSAIController Function CreateCombatBehaviorTree ***********************
struct Z_Construct_UFunction_ARTSAIController_CreateCombatBehaviorTree_Statics
{
	struct RTSAIController_eventCreateCombatBehaviorTree_Parms
	{
		URTSBehaviorNode* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Behavior Tree" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSAIController_CreateCombatBehaviorTree_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventCreateCombatBehaviorTree_Parms, ReturnValue), Z_Construct_UClass_URTSBehaviorNode_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_CreateCombatBehaviorTree_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_CreateCombatBehaviorTree_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_CreateCombatBehaviorTree_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_CreateCombatBehaviorTree_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "CreateCombatBehaviorTree", Z_Construct_UFunction_ARTSAIController_CreateCombatBehaviorTree_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_CreateCombatBehaviorTree_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_CreateCombatBehaviorTree_Statics::RTSAIController_eventCreateCombatBehaviorTree_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_CreateCombatBehaviorTree_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_CreateCombatBehaviorTree_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_CreateCombatBehaviorTree_Statics::RTSAIController_eventCreateCombatBehaviorTree_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_CreateCombatBehaviorTree()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_CreateCombatBehaviorTree_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execCreateCombatBehaviorTree)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(URTSBehaviorNode**)Z_Param__Result=P_THIS->CreateCombatBehaviorTree();
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function CreateCombatBehaviorTree *************************

// ********** Begin Class ARTSAIController Function CreateDefaultBehaviorTree **********************
struct Z_Construct_UFunction_ARTSAIController_CreateDefaultBehaviorTree_Statics
{
	struct RTSAIController_eventCreateDefaultBehaviorTree_Parms
	{
		URTSBehaviorNode* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Behavior Tree" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSAIController_CreateDefaultBehaviorTree_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventCreateDefaultBehaviorTree_Parms, ReturnValue), Z_Construct_UClass_URTSBehaviorNode_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_CreateDefaultBehaviorTree_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_CreateDefaultBehaviorTree_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_CreateDefaultBehaviorTree_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_CreateDefaultBehaviorTree_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "CreateDefaultBehaviorTree", Z_Construct_UFunction_ARTSAIController_CreateDefaultBehaviorTree_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_CreateDefaultBehaviorTree_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_CreateDefaultBehaviorTree_Statics::RTSAIController_eventCreateDefaultBehaviorTree_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_CreateDefaultBehaviorTree_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_CreateDefaultBehaviorTree_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_CreateDefaultBehaviorTree_Statics::RTSAIController_eventCreateDefaultBehaviorTree_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_CreateDefaultBehaviorTree()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_CreateDefaultBehaviorTree_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execCreateDefaultBehaviorTree)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(URTSBehaviorNode**)Z_Param__Result=P_THIS->CreateDefaultBehaviorTree();
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function CreateDefaultBehaviorTree ************************

// ********** Begin Class ARTSAIController Function CreatePatrolBehaviorTree ***********************
struct Z_Construct_UFunction_ARTSAIController_CreatePatrolBehaviorTree_Statics
{
	struct RTSAIController_eventCreatePatrolBehaviorTree_Parms
	{
		TArray<FVector> PatrolWaypoints;
		URTSBehaviorNode* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Behavior Tree" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PatrolWaypoints_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PatrolWaypoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PatrolWaypoints;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSAIController_CreatePatrolBehaviorTree_Statics::NewProp_PatrolWaypoints_Inner = { "PatrolWaypoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ARTSAIController_CreatePatrolBehaviorTree_Statics::NewProp_PatrolWaypoints = { "PatrolWaypoints", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventCreatePatrolBehaviorTree_Parms, PatrolWaypoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PatrolWaypoints_MetaData), NewProp_PatrolWaypoints_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSAIController_CreatePatrolBehaviorTree_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventCreatePatrolBehaviorTree_Parms, ReturnValue), Z_Construct_UClass_URTSBehaviorNode_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_CreatePatrolBehaviorTree_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_CreatePatrolBehaviorTree_Statics::NewProp_PatrolWaypoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_CreatePatrolBehaviorTree_Statics::NewProp_PatrolWaypoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_CreatePatrolBehaviorTree_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_CreatePatrolBehaviorTree_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_CreatePatrolBehaviorTree_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "CreatePatrolBehaviorTree", Z_Construct_UFunction_ARTSAIController_CreatePatrolBehaviorTree_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_CreatePatrolBehaviorTree_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_CreatePatrolBehaviorTree_Statics::RTSAIController_eventCreatePatrolBehaviorTree_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_CreatePatrolBehaviorTree_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_CreatePatrolBehaviorTree_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_CreatePatrolBehaviorTree_Statics::RTSAIController_eventCreatePatrolBehaviorTree_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_CreatePatrolBehaviorTree()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_CreatePatrolBehaviorTree_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execCreatePatrolBehaviorTree)
{
	P_GET_TARRAY_REF(FVector,Z_Param_Out_PatrolWaypoints);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(URTSBehaviorNode**)Z_Param__Result=P_THIS->CreatePatrolBehaviorTree(Z_Param_Out_PatrolWaypoints);
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function CreatePatrolBehaviorTree *************************

// ********** Begin Class ARTSAIController Function DetectEnemiesInRange ***************************
struct Z_Construct_UFunction_ARTSAIController_DetectEnemiesInRange_Statics
{
	struct RTSAIController_eventDetectEnemiesInRange_Parms
	{
		TArray<ARTSBaseActor*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Detection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Detection\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Detection" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSAIController_DetectEnemiesInRange_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ARTSAIController_DetectEnemiesInRange_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventDetectEnemiesInRange_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_DetectEnemiesInRange_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_DetectEnemiesInRange_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_DetectEnemiesInRange_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_DetectEnemiesInRange_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_DetectEnemiesInRange_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "DetectEnemiesInRange", Z_Construct_UFunction_ARTSAIController_DetectEnemiesInRange_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_DetectEnemiesInRange_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_DetectEnemiesInRange_Statics::RTSAIController_eventDetectEnemiesInRange_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_DetectEnemiesInRange_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_DetectEnemiesInRange_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_DetectEnemiesInRange_Statics::RTSAIController_eventDetectEnemiesInRange_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_DetectEnemiesInRange()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_DetectEnemiesInRange_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execDetectEnemiesInRange)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<ARTSBaseActor*>*)Z_Param__Result=P_THIS->DetectEnemiesInRange();
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function DetectEnemiesInRange *****************************

// ********** Begin Class ARTSAIController Function EnableCoverSeeking *****************************
struct Z_Construct_UFunction_ARTSAIController_EnableCoverSeeking_Statics
{
	struct RTSAIController_eventEnableCoverSeeking_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Combat" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSAIController_EnableCoverSeeking_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((RTSAIController_eventEnableCoverSeeking_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSAIController_EnableCoverSeeking_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAIController_eventEnableCoverSeeking_Parms), &Z_Construct_UFunction_ARTSAIController_EnableCoverSeeking_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_EnableCoverSeeking_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_EnableCoverSeeking_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_EnableCoverSeeking_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_EnableCoverSeeking_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "EnableCoverSeeking", Z_Construct_UFunction_ARTSAIController_EnableCoverSeeking_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_EnableCoverSeeking_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_EnableCoverSeeking_Statics::RTSAIController_eventEnableCoverSeeking_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_EnableCoverSeeking_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_EnableCoverSeeking_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_EnableCoverSeeking_Statics::RTSAIController_eventEnableCoverSeeking_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_EnableCoverSeeking()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_EnableCoverSeeking_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execEnableCoverSeeking)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableCoverSeeking(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function EnableCoverSeeking *******************************

// ********** Begin Class ARTSAIController Function EnableKitingBehavior ***************************
struct Z_Construct_UFunction_ARTSAIController_EnableKitingBehavior_Statics
{
	struct RTSAIController_eventEnableKitingBehavior_Parms
	{
		bool bEnabled;
		float KitingDistance;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Combat" },
		{ "CPP_Default_KitingDistance", "300.000000" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_KitingDistance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSAIController_EnableKitingBehavior_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((RTSAIController_eventEnableKitingBehavior_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSAIController_EnableKitingBehavior_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAIController_eventEnableKitingBehavior_Parms), &Z_Construct_UFunction_ARTSAIController_EnableKitingBehavior_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSAIController_EnableKitingBehavior_Statics::NewProp_KitingDistance = { "KitingDistance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventEnableKitingBehavior_Parms, KitingDistance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_EnableKitingBehavior_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_EnableKitingBehavior_Statics::NewProp_bEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_EnableKitingBehavior_Statics::NewProp_KitingDistance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_EnableKitingBehavior_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_EnableKitingBehavior_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "EnableKitingBehavior", Z_Construct_UFunction_ARTSAIController_EnableKitingBehavior_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_EnableKitingBehavior_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_EnableKitingBehavior_Statics::RTSAIController_eventEnableKitingBehavior_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_EnableKitingBehavior_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_EnableKitingBehavior_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_EnableKitingBehavior_Statics::RTSAIController_eventEnableKitingBehavior_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_EnableKitingBehavior()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_EnableKitingBehavior_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execEnableKitingBehavior)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_GET_PROPERTY(FFloatProperty,Z_Param_KitingDistance);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableKitingBehavior(Z_Param_bEnabled,Z_Param_KitingDistance);
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function EnableKitingBehavior *****************************

// ********** Begin Class ARTSAIController Function EnableObstacleAvoidance ************************
struct Z_Construct_UFunction_ARTSAIController_EnableObstacleAvoidance_Statics
{
	struct RTSAIController_eventEnableObstacleAvoidance_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Movement" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSAIController_EnableObstacleAvoidance_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((RTSAIController_eventEnableObstacleAvoidance_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSAIController_EnableObstacleAvoidance_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAIController_eventEnableObstacleAvoidance_Parms), &Z_Construct_UFunction_ARTSAIController_EnableObstacleAvoidance_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_EnableObstacleAvoidance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_EnableObstacleAvoidance_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_EnableObstacleAvoidance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_EnableObstacleAvoidance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "EnableObstacleAvoidance", Z_Construct_UFunction_ARTSAIController_EnableObstacleAvoidance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_EnableObstacleAvoidance_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_EnableObstacleAvoidance_Statics::RTSAIController_eventEnableObstacleAvoidance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_EnableObstacleAvoidance_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_EnableObstacleAvoidance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_EnableObstacleAvoidance_Statics::RTSAIController_eventEnableObstacleAvoidance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_EnableObstacleAvoidance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_EnableObstacleAvoidance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execEnableObstacleAvoidance)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableObstacleAvoidance(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function EnableObstacleAvoidance **************************

// ********** Begin Class ARTSAIController Function FindBestTarget *********************************
struct Z_Construct_UFunction_ARTSAIController_FindBestTarget_Statics
{
	struct RTSAIController_eventFindBestTarget_Parms
	{
		ARTSBaseActor* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Targeting" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSAIController_FindBestTarget_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventFindBestTarget_Parms, ReturnValue), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_FindBestTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_FindBestTarget_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_FindBestTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_FindBestTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "FindBestTarget", Z_Construct_UFunction_ARTSAIController_FindBestTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_FindBestTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_FindBestTarget_Statics::RTSAIController_eventFindBestTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_FindBestTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_FindBestTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_FindBestTarget_Statics::RTSAIController_eventFindBestTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_FindBestTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_FindBestTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execFindBestTarget)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ARTSBaseActor**)Z_Param__Result=P_THIS->FindBestTarget();
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function FindBestTarget ***********************************

// ********** Begin Class ARTSAIController Function GetDistanceToDefendPosition ********************
struct Z_Construct_UFunction_ARTSAIController_GetDistanceToDefendPosition_Statics
{
	struct RTSAIController_eventGetDistanceToDefendPosition_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI State" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSAIController_GetDistanceToDefendPosition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventGetDistanceToDefendPosition_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_GetDistanceToDefendPosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_GetDistanceToDefendPosition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_GetDistanceToDefendPosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_GetDistanceToDefendPosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "GetDistanceToDefendPosition", Z_Construct_UFunction_ARTSAIController_GetDistanceToDefendPosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_GetDistanceToDefendPosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_GetDistanceToDefendPosition_Statics::RTSAIController_eventGetDistanceToDefendPosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_GetDistanceToDefendPosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_GetDistanceToDefendPosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_GetDistanceToDefendPosition_Statics::RTSAIController_eventGetDistanceToDefendPosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_GetDistanceToDefendPosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_GetDistanceToDefendPosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execGetDistanceToDefendPosition)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetDistanceToDefendPosition();
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function GetDistanceToDefendPosition **********************

// ********** Begin Class ARTSAIController Function HasReachedDestination **************************
struct Z_Construct_UFunction_ARTSAIController_HasReachedDestination_Statics
{
	struct RTSAIController_eventHasReachedDestination_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Movement" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSAIController_HasReachedDestination_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSAIController_eventHasReachedDestination_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSAIController_HasReachedDestination_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAIController_eventHasReachedDestination_Parms), &Z_Construct_UFunction_ARTSAIController_HasReachedDestination_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_HasReachedDestination_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_HasReachedDestination_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_HasReachedDestination_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_HasReachedDestination_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "HasReachedDestination", Z_Construct_UFunction_ARTSAIController_HasReachedDestination_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_HasReachedDestination_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_HasReachedDestination_Statics::RTSAIController_eventHasReachedDestination_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_HasReachedDestination_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_HasReachedDestination_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_HasReachedDestination_Statics::RTSAIController_eventHasReachedDestination_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_HasReachedDestination()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_HasReachedDestination_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execHasReachedDestination)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasReachedDestination();
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function HasReachedDestination ****************************

// ********** Begin Class ARTSAIController Function HasValidTarget *********************************
struct Z_Construct_UFunction_ARTSAIController_HasValidTarget_Statics
{
	struct RTSAIController_eventHasValidTarget_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Targeting" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSAIController_HasValidTarget_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSAIController_eventHasValidTarget_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSAIController_HasValidTarget_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAIController_eventHasValidTarget_Parms), &Z_Construct_UFunction_ARTSAIController_HasValidTarget_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_HasValidTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_HasValidTarget_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_HasValidTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_HasValidTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "HasValidTarget", Z_Construct_UFunction_ARTSAIController_HasValidTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_HasValidTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_HasValidTarget_Statics::RTSAIController_eventHasValidTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_HasValidTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_HasValidTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_HasValidTarget_Statics::RTSAIController_eventHasValidTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_HasValidTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_HasValidTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execHasValidTarget)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasValidTarget();
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function HasValidTarget ***********************************

// ********** Begin Class ARTSAIController Function IsBehaviorTreeRunning **************************
struct Z_Construct_UFunction_ARTSAIController_IsBehaviorTreeRunning_Statics
{
	struct RTSAIController_eventIsBehaviorTreeRunning_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Behavior Tree" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSAIController_IsBehaviorTreeRunning_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSAIController_eventIsBehaviorTreeRunning_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSAIController_IsBehaviorTreeRunning_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAIController_eventIsBehaviorTreeRunning_Parms), &Z_Construct_UFunction_ARTSAIController_IsBehaviorTreeRunning_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_IsBehaviorTreeRunning_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_IsBehaviorTreeRunning_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_IsBehaviorTreeRunning_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_IsBehaviorTreeRunning_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "IsBehaviorTreeRunning", Z_Construct_UFunction_ARTSAIController_IsBehaviorTreeRunning_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_IsBehaviorTreeRunning_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_IsBehaviorTreeRunning_Statics::RTSAIController_eventIsBehaviorTreeRunning_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_IsBehaviorTreeRunning_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_IsBehaviorTreeRunning_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_IsBehaviorTreeRunning_Statics::RTSAIController_eventIsBehaviorTreeRunning_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_IsBehaviorTreeRunning()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_IsBehaviorTreeRunning_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execIsBehaviorTreeRunning)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsBehaviorTreeRunning();
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function IsBehaviorTreeRunning ****************************

// ********** Begin Class ARTSAIController Function IsGroupLeader **********************************
struct Z_Construct_UFunction_ARTSAIController_IsGroupLeader_Statics
{
	struct RTSAIController_eventIsGroupLeader_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Group" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSAIController_IsGroupLeader_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSAIController_eventIsGroupLeader_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSAIController_IsGroupLeader_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAIController_eventIsGroupLeader_Parms), &Z_Construct_UFunction_ARTSAIController_IsGroupLeader_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_IsGroupLeader_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_IsGroupLeader_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_IsGroupLeader_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_IsGroupLeader_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "IsGroupLeader", Z_Construct_UFunction_ARTSAIController_IsGroupLeader_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_IsGroupLeader_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_IsGroupLeader_Statics::RTSAIController_eventIsGroupLeader_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_IsGroupLeader_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_IsGroupLeader_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_IsGroupLeader_Statics::RTSAIController_eventIsGroupLeader_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_IsGroupLeader()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_IsGroupLeader_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execIsGroupLeader)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsGroupLeader();
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function IsGroupLeader ************************************

// ********** Begin Class ARTSAIController Function IsInCombat *************************************
struct Z_Construct_UFunction_ARTSAIController_IsInCombat_Statics
{
	struct RTSAIController_eventIsInCombat_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// State Queries\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "State Queries" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSAIController_IsInCombat_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSAIController_eventIsInCombat_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSAIController_IsInCombat_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAIController_eventIsInCombat_Parms), &Z_Construct_UFunction_ARTSAIController_IsInCombat_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_IsInCombat_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_IsInCombat_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_IsInCombat_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_IsInCombat_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "IsInCombat", Z_Construct_UFunction_ARTSAIController_IsInCombat_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_IsInCombat_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_IsInCombat_Statics::RTSAIController_eventIsInCombat_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_IsInCombat_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_IsInCombat_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_IsInCombat_Statics::RTSAIController_eventIsInCombat_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_IsInCombat()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_IsInCombat_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execIsInCombat)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInCombat();
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function IsInCombat ***************************************

// ********** Begin Class ARTSAIController Function IsInGroup **************************************
struct Z_Construct_UFunction_ARTSAIController_IsInGroup_Statics
{
	struct RTSAIController_eventIsInGroup_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Group" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSAIController_IsInGroup_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSAIController_eventIsInGroup_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSAIController_IsInGroup_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAIController_eventIsInGroup_Parms), &Z_Construct_UFunction_ARTSAIController_IsInGroup_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_IsInGroup_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_IsInGroup_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_IsInGroup_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_IsInGroup_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "IsInGroup", Z_Construct_UFunction_ARTSAIController_IsInGroup_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_IsInGroup_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_IsInGroup_Statics::RTSAIController_eventIsInGroup_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_IsInGroup_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_IsInGroup_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_IsInGroup_Statics::RTSAIController_eventIsInGroup_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_IsInGroup()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_IsInGroup_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execIsInGroup)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInGroup();
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function IsInGroup ****************************************

// ********** Begin Class ARTSAIController Function IsInOptimalRange *******************************
struct Z_Construct_UFunction_ARTSAIController_IsInOptimalRange_Statics
{
	struct RTSAIController_eventIsInOptimalRange_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Combat" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSAIController_IsInOptimalRange_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSAIController_eventIsInOptimalRange_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSAIController_IsInOptimalRange_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAIController_eventIsInOptimalRange_Parms), &Z_Construct_UFunction_ARTSAIController_IsInOptimalRange_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_IsInOptimalRange_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_IsInOptimalRange_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_IsInOptimalRange_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_IsInOptimalRange_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "IsInOptimalRange", Z_Construct_UFunction_ARTSAIController_IsInOptimalRange_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_IsInOptimalRange_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_IsInOptimalRange_Statics::RTSAIController_eventIsInOptimalRange_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_IsInOptimalRange_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_IsInOptimalRange_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_IsInOptimalRange_Statics::RTSAIController_eventIsInOptimalRange_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_IsInOptimalRange()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_IsInOptimalRange_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execIsInOptimalRange)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInOptimalRange();
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function IsInOptimalRange *********************************

// ********** Begin Class ARTSAIController Function IsMoving ***************************************
struct Z_Construct_UFunction_ARTSAIController_IsMoving_Statics
{
	struct RTSAIController_eventIsMoving_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Movement" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSAIController_IsMoving_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSAIController_eventIsMoving_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSAIController_IsMoving_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAIController_eventIsMoving_Parms), &Z_Construct_UFunction_ARTSAIController_IsMoving_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_IsMoving_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_IsMoving_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_IsMoving_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_IsMoving_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "IsMoving", Z_Construct_UFunction_ARTSAIController_IsMoving_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_IsMoving_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_IsMoving_Statics::RTSAIController_eventIsMoving_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_IsMoving_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_IsMoving_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_IsMoving_Statics::RTSAIController_eventIsMoving_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_IsMoving()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_IsMoving_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execIsMoving)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsMoving();
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function IsMoving *****************************************

// ********** Begin Class ARTSAIController Function JoinGroup **************************************
struct Z_Construct_UFunction_ARTSAIController_JoinGroup_Statics
{
	struct RTSAIController_eventJoinGroup_Parms
	{
		ARTSAIController* NewGroupLeader;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Group" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Group Coordination\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Group Coordination" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NewGroupLeader;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSAIController_JoinGroup_Statics::NewProp_NewGroupLeader = { "NewGroupLeader", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventJoinGroup_Parms, NewGroupLeader), Z_Construct_UClass_ARTSAIController_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_JoinGroup_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_JoinGroup_Statics::NewProp_NewGroupLeader,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_JoinGroup_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_JoinGroup_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "JoinGroup", Z_Construct_UFunction_ARTSAIController_JoinGroup_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_JoinGroup_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_JoinGroup_Statics::RTSAIController_eventJoinGroup_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_JoinGroup_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_JoinGroup_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_JoinGroup_Statics::RTSAIController_eventJoinGroup_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_JoinGroup()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_JoinGroup_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execJoinGroup)
{
	P_GET_OBJECT(ARTSAIController,Z_Param_NewGroupLeader);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->JoinGroup(Z_Param_NewGroupLeader);
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function JoinGroup ****************************************

// ********** Begin Class ARTSAIController Function LeaveGroup *************************************
struct Z_Construct_UFunction_ARTSAIController_LeaveGroup_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Group" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_LeaveGroup_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "LeaveGroup", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_LeaveGroup_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_LeaveGroup_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSAIController_LeaveGroup()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_LeaveGroup_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execLeaveGroup)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LeaveGroup();
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function LeaveGroup ***************************************

// ********** Begin Class ARTSAIController Function MoveToLocationWithPathfinding ******************
struct Z_Construct_UFunction_ARTSAIController_MoveToLocationWithPathfinding_Statics
{
	struct RTSAIController_eventMoveToLocationWithPathfinding_Parms
	{
		FVector Destination;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Advanced Movement Functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Advanced Movement Functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Destination_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Destination;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSAIController_MoveToLocationWithPathfinding_Statics::NewProp_Destination = { "Destination", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventMoveToLocationWithPathfinding_Parms, Destination), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Destination_MetaData), NewProp_Destination_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_MoveToLocationWithPathfinding_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_MoveToLocationWithPathfinding_Statics::NewProp_Destination,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_MoveToLocationWithPathfinding_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_MoveToLocationWithPathfinding_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "MoveToLocationWithPathfinding", Z_Construct_UFunction_ARTSAIController_MoveToLocationWithPathfinding_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_MoveToLocationWithPathfinding_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_MoveToLocationWithPathfinding_Statics::RTSAIController_eventMoveToLocationWithPathfinding_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_MoveToLocationWithPathfinding_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_MoveToLocationWithPathfinding_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_MoveToLocationWithPathfinding_Statics::RTSAIController_eventMoveToLocationWithPathfinding_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_MoveToLocationWithPathfinding()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_MoveToLocationWithPathfinding_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execMoveToLocationWithPathfinding)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Destination);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->MoveToLocationWithPathfinding(Z_Param_Out_Destination);
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function MoveToLocationWithPathfinding ********************

// ********** Begin Class ARTSAIController Function OnEnemyDetectedEvent ***************************
struct RTSAIController_eventOnEnemyDetectedEvent_Parms
{
	ARTSBaseActor* Enemy;
};
static FName NAME_ARTSAIController_OnEnemyDetectedEvent = FName(TEXT("OnEnemyDetectedEvent"));
void ARTSAIController::OnEnemyDetectedEvent(ARTSBaseActor* Enemy)
{
	RTSAIController_eventOnEnemyDetectedEvent_Parms Parms;
	Parms.Enemy=Enemy;
	UFunction* Func = FindFunctionChecked(NAME_ARTSAIController_OnEnemyDetectedEvent);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_ARTSAIController_OnEnemyDetectedEvent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Events" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Enemy;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSAIController_OnEnemyDetectedEvent_Statics::NewProp_Enemy = { "Enemy", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventOnEnemyDetectedEvent_Parms, Enemy), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_OnEnemyDetectedEvent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_OnEnemyDetectedEvent_Statics::NewProp_Enemy,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_OnEnemyDetectedEvent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_OnEnemyDetectedEvent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "OnEnemyDetectedEvent", Z_Construct_UFunction_ARTSAIController_OnEnemyDetectedEvent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_OnEnemyDetectedEvent_Statics::PropPointers), sizeof(RTSAIController_eventOnEnemyDetectedEvent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_OnEnemyDetectedEvent_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_OnEnemyDetectedEvent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSAIController_eventOnEnemyDetectedEvent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_OnEnemyDetectedEvent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_OnEnemyDetectedEvent_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSAIController Function OnEnemyDetectedEvent *****************************

// ********** Begin Class ARTSAIController Function OnStateChanged *********************************
struct RTSAIController_eventOnStateChanged_Parms
{
	ERTSAIState OldState;
	ERTSAIState NewState;
};
static FName NAME_ARTSAIController_OnStateChanged = FName(TEXT("OnStateChanged"));
void ARTSAIController::OnStateChanged(ERTSAIState OldState, ERTSAIState NewState)
{
	RTSAIController_eventOnStateChanged_Parms Parms;
	Parms.OldState=OldState;
	Parms.NewState=NewState;
	UFunction* Func = FindFunctionChecked(NAME_ARTSAIController_OnStateChanged);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_ARTSAIController_OnStateChanged_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Blueprint events\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blueprint events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldState;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSAIController_OnStateChanged_Statics::NewProp_OldState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSAIController_OnStateChanged_Statics::NewProp_OldState = { "OldState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventOnStateChanged_Parms, OldState), Z_Construct_UEnum_ArmorWars_ERTSAIState, METADATA_PARAMS(0, nullptr) }; // 1008175298
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSAIController_OnStateChanged_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSAIController_OnStateChanged_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventOnStateChanged_Parms, NewState), Z_Construct_UEnum_ArmorWars_ERTSAIState, METADATA_PARAMS(0, nullptr) }; // 1008175298
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_OnStateChanged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_OnStateChanged_Statics::NewProp_OldState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_OnStateChanged_Statics::NewProp_OldState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_OnStateChanged_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_OnStateChanged_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_OnStateChanged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_OnStateChanged_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "OnStateChanged", Z_Construct_UFunction_ARTSAIController_OnStateChanged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_OnStateChanged_Statics::PropPointers), sizeof(RTSAIController_eventOnStateChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_OnStateChanged_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_OnStateChanged_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSAIController_eventOnStateChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_OnStateChanged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_OnStateChanged_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSAIController Function OnStateChanged ***********************************

// ********** Begin Class ARTSAIController Function OnTargetAcquiredEvent **************************
struct RTSAIController_eventOnTargetAcquiredEvent_Parms
{
	ARTSBaseActor* Target;
};
static FName NAME_ARTSAIController_OnTargetAcquiredEvent = FName(TEXT("OnTargetAcquiredEvent"));
void ARTSAIController::OnTargetAcquiredEvent(ARTSBaseActor* Target)
{
	RTSAIController_eventOnTargetAcquiredEvent_Parms Parms;
	Parms.Target=Target;
	UFunction* Func = FindFunctionChecked(NAME_ARTSAIController_OnTargetAcquiredEvent);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_ARTSAIController_OnTargetAcquiredEvent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Events" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSAIController_OnTargetAcquiredEvent_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventOnTargetAcquiredEvent_Parms, Target), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_OnTargetAcquiredEvent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_OnTargetAcquiredEvent_Statics::NewProp_Target,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_OnTargetAcquiredEvent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_OnTargetAcquiredEvent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "OnTargetAcquiredEvent", Z_Construct_UFunction_ARTSAIController_OnTargetAcquiredEvent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_OnTargetAcquiredEvent_Statics::PropPointers), sizeof(RTSAIController_eventOnTargetAcquiredEvent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_OnTargetAcquiredEvent_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_OnTargetAcquiredEvent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSAIController_eventOnTargetAcquiredEvent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_OnTargetAcquiredEvent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_OnTargetAcquiredEvent_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSAIController Function OnTargetAcquiredEvent ****************************

// ********** Begin Class ARTSAIController Function OnTargetLost ***********************************
struct RTSAIController_eventOnTargetLost_Parms
{
	ARTSBaseActor* Target;
};
static FName NAME_ARTSAIController_OnTargetLost = FName(TEXT("OnTargetLost"));
void ARTSAIController::OnTargetLost(ARTSBaseActor* Target)
{
	RTSAIController_eventOnTargetLost_Parms Parms;
	Parms.Target=Target;
	UFunction* Func = FindFunctionChecked(NAME_ARTSAIController_OnTargetLost);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_ARTSAIController_OnTargetLost_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Events" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSAIController_OnTargetLost_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventOnTargetLost_Parms, Target), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_OnTargetLost_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_OnTargetLost_Statics::NewProp_Target,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_OnTargetLost_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_OnTargetLost_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "OnTargetLost", Z_Construct_UFunction_ARTSAIController_OnTargetLost_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_OnTargetLost_Statics::PropPointers), sizeof(RTSAIController_eventOnTargetLost_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_OnTargetLost_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_OnTargetLost_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSAIController_eventOnTargetLost_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_OnTargetLost()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_OnTargetLost_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSAIController Function OnTargetLost *************************************

// ********** Begin Class ARTSAIController Function OnUnitDeath ************************************
struct Z_Construct_UFunction_ARTSAIController_OnUnitDeath_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Unit death handling\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Unit death handling" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_OnUnitDeath_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "OnUnitDeath", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_OnUnitDeath_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_OnUnitDeath_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSAIController_OnUnitDeath()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_OnUnitDeath_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execOnUnitDeath)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnUnitDeath();
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function OnUnitDeath **************************************

// ********** Begin Class ARTSAIController Function PauseBehaviorTree ******************************
struct Z_Construct_UFunction_ARTSAIController_PauseBehaviorTree_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Behavior Tree" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_PauseBehaviorTree_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "PauseBehaviorTree", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_PauseBehaviorTree_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_PauseBehaviorTree_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSAIController_PauseBehaviorTree()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_PauseBehaviorTree_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execPauseBehaviorTree)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PauseBehaviorTree();
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function PauseBehaviorTree ********************************

// ********** Begin Class ARTSAIController Function RemoveKnownTarget ******************************
struct Z_Construct_UFunction_ARTSAIController_RemoveKnownTarget_Statics
{
	struct RTSAIController_eventRemoveKnownTarget_Parms
	{
		ARTSBaseActor* Target;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Targeting" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSAIController_RemoveKnownTarget_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventRemoveKnownTarget_Parms, Target), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_RemoveKnownTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_RemoveKnownTarget_Statics::NewProp_Target,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_RemoveKnownTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_RemoveKnownTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "RemoveKnownTarget", Z_Construct_UFunction_ARTSAIController_RemoveKnownTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_RemoveKnownTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_RemoveKnownTarget_Statics::RTSAIController_eventRemoveKnownTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_RemoveKnownTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_RemoveKnownTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_RemoveKnownTarget_Statics::RTSAIController_eventRemoveKnownTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_RemoveKnownTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_RemoveKnownTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execRemoveKnownTarget)
{
	P_GET_OBJECT(ARTSBaseActor,Z_Param_Target);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveKnownTarget(Z_Param_Target);
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function RemoveKnownTarget ********************************

// ********** Begin Class ARTSAIController Function ResumeBehaviorTree *****************************
struct Z_Construct_UFunction_ARTSAIController_ResumeBehaviorTree_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Behavior Tree" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_ResumeBehaviorTree_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "ResumeBehaviorTree", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_ResumeBehaviorTree_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_ResumeBehaviorTree_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSAIController_ResumeBehaviorTree()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_ResumeBehaviorTree_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execResumeBehaviorTree)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResumeBehaviorTree();
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function ResumeBehaviorTree *******************************

// ********** Begin Class ARTSAIController Function SetAIState *************************************
struct Z_Construct_UFunction_ARTSAIController_SetAIState_Statics
{
	struct RTSAIController_eventSetAIState_Parms
	{
		ERTSAIState NewState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Control" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// AI State Management\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "AI State Management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSAIController_SetAIState_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSAIController_SetAIState_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventSetAIState_Parms, NewState), Z_Construct_UEnum_ArmorWars_ERTSAIState, METADATA_PARAMS(0, nullptr) }; // 1008175298
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_SetAIState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_SetAIState_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_SetAIState_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_SetAIState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_SetAIState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "SetAIState", Z_Construct_UFunction_ARTSAIController_SetAIState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_SetAIState_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_SetAIState_Statics::RTSAIController_eventSetAIState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_SetAIState_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_SetAIState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_SetAIState_Statics::RTSAIController_eventSetAIState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_SetAIState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_SetAIState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execSetAIState)
{
	P_GET_ENUM(ERTSAIState,Z_Param_NewState);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetAIState(ERTSAIState(Z_Param_NewState));
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function SetAIState ***************************************

// ********** Begin Class ARTSAIController Function SetCombatBehavior ******************************
struct Z_Construct_UFunction_ARTSAIController_SetCombatBehavior_Statics
{
	struct RTSAIController_eventSetCombatBehavior_Parms
	{
		ERTSCombatBehavior Behavior;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Advanced Combat Functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Advanced Combat Functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Behavior_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Behavior;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSAIController_SetCombatBehavior_Statics::NewProp_Behavior_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSAIController_SetCombatBehavior_Statics::NewProp_Behavior = { "Behavior", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventSetCombatBehavior_Parms, Behavior), Z_Construct_UEnum_ArmorWars_ERTSCombatBehavior, METADATA_PARAMS(0, nullptr) }; // 3322899454
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_SetCombatBehavior_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_SetCombatBehavior_Statics::NewProp_Behavior_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_SetCombatBehavior_Statics::NewProp_Behavior,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_SetCombatBehavior_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_SetCombatBehavior_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "SetCombatBehavior", Z_Construct_UFunction_ARTSAIController_SetCombatBehavior_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_SetCombatBehavior_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_SetCombatBehavior_Statics::RTSAIController_eventSetCombatBehavior_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_SetCombatBehavior_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_SetCombatBehavior_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_SetCombatBehavior_Statics::RTSAIController_eventSetCombatBehavior_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_SetCombatBehavior()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_SetCombatBehavior_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execSetCombatBehavior)
{
	P_GET_ENUM(ERTSCombatBehavior,Z_Param_Behavior);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetCombatBehavior(ERTSCombatBehavior(Z_Param_Behavior));
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function SetCombatBehavior ********************************

// ********** Begin Class ARTSAIController Function SetDefendPosition ******************************
struct Z_Construct_UFunction_ARTSAIController_SetDefendPosition_Statics
{
	struct RTSAIController_eventSetDefendPosition_Parms
	{
		FVector Position;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Control" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSAIController_SetDefendPosition_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventSetDefendPosition_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_SetDefendPosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_SetDefendPosition_Statics::NewProp_Position,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_SetDefendPosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_SetDefendPosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "SetDefendPosition", Z_Construct_UFunction_ARTSAIController_SetDefendPosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_SetDefendPosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_SetDefendPosition_Statics::RTSAIController_eventSetDefendPosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_SetDefendPosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_SetDefendPosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_SetDefendPosition_Statics::RTSAIController_eventSetDefendPosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_SetDefendPosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_SetDefendPosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execSetDefendPosition)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetDefendPosition(Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function SetDefendPosition ********************************

// ********** Begin Class ARTSAIController Function SetEngagementRange *****************************
struct Z_Construct_UFunction_ARTSAIController_SetEngagementRange_Statics
{
	struct RTSAIController_eventSetEngagementRange_Parms
	{
		float MinRange;
		float MaxRange;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Combat" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxRange;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSAIController_SetEngagementRange_Statics::NewProp_MinRange = { "MinRange", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventSetEngagementRange_Parms, MinRange), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSAIController_SetEngagementRange_Statics::NewProp_MaxRange = { "MaxRange", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventSetEngagementRange_Parms, MaxRange), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_SetEngagementRange_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_SetEngagementRange_Statics::NewProp_MinRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_SetEngagementRange_Statics::NewProp_MaxRange,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_SetEngagementRange_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_SetEngagementRange_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "SetEngagementRange", Z_Construct_UFunction_ARTSAIController_SetEngagementRange_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_SetEngagementRange_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_SetEngagementRange_Statics::RTSAIController_eventSetEngagementRange_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_SetEngagementRange_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_SetEngagementRange_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_SetEngagementRange_Statics::RTSAIController_eventSetEngagementRange_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_SetEngagementRange()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_SetEngagementRange_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execSetEngagementRange)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_MinRange);
	P_GET_PROPERTY(FFloatProperty,Z_Param_MaxRange);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetEngagementRange(Z_Param_MinRange,Z_Param_MaxRange);
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function SetEngagementRange *******************************

// ********** Begin Class ARTSAIController Function SetFlankingBehavior ****************************
struct Z_Construct_UFunction_ARTSAIController_SetFlankingBehavior_Statics
{
	struct RTSAIController_eventSetFlankingBehavior_Parms
	{
		bool bEnabled;
		float FlankingRadius;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Combat" },
		{ "CPP_Default_FlankingRadius", "500.000000" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FlankingRadius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSAIController_SetFlankingBehavior_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((RTSAIController_eventSetFlankingBehavior_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSAIController_SetFlankingBehavior_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAIController_eventSetFlankingBehavior_Parms), &Z_Construct_UFunction_ARTSAIController_SetFlankingBehavior_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSAIController_SetFlankingBehavior_Statics::NewProp_FlankingRadius = { "FlankingRadius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventSetFlankingBehavior_Parms, FlankingRadius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_SetFlankingBehavior_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_SetFlankingBehavior_Statics::NewProp_bEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_SetFlankingBehavior_Statics::NewProp_FlankingRadius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_SetFlankingBehavior_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_SetFlankingBehavior_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "SetFlankingBehavior", Z_Construct_UFunction_ARTSAIController_SetFlankingBehavior_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_SetFlankingBehavior_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_SetFlankingBehavior_Statics::RTSAIController_eventSetFlankingBehavior_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_SetFlankingBehavior_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_SetFlankingBehavior_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_SetFlankingBehavior_Statics::RTSAIController_eventSetFlankingBehavior_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_SetFlankingBehavior()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_SetFlankingBehavior_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execSetFlankingBehavior)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_GET_PROPERTY(FFloatProperty,Z_Param_FlankingRadius);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetFlankingBehavior(Z_Param_bEnabled,Z_Param_FlankingRadius);
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function SetFlankingBehavior ******************************

// ********** Begin Class ARTSAIController Function SetFollowTarget ********************************
struct Z_Construct_UFunction_ARTSAIController_SetFollowTarget_Statics
{
	struct RTSAIController_eventSetFollowTarget_Parms
	{
		ARTSBaseActor* Target;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Control" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSAIController_SetFollowTarget_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventSetFollowTarget_Parms, Target), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_SetFollowTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_SetFollowTarget_Statics::NewProp_Target,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_SetFollowTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_SetFollowTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "SetFollowTarget", Z_Construct_UFunction_ARTSAIController_SetFollowTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_SetFollowTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_SetFollowTarget_Statics::RTSAIController_eventSetFollowTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_SetFollowTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_SetFollowTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_SetFollowTarget_Statics::RTSAIController_eventSetFollowTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_SetFollowTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_SetFollowTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execSetFollowTarget)
{
	P_GET_OBJECT(ARTSBaseActor,Z_Param_Target);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetFollowTarget(Z_Param_Target);
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function SetFollowTarget **********************************

// ********** Begin Class ARTSAIController Function SetFormationMovement ***************************
struct Z_Construct_UFunction_ARTSAIController_SetFormationMovement_Statics
{
	struct RTSAIController_eventSetFormationMovement_Parms
	{
		bool bEnabled;
		ARTSAIController* NewFormationLeader;
		FVector NewFormationOffset;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Movement" },
		{ "CPP_Default_NewFormationLeader", "None" },
		{ "CPP_Default_NewFormationOffset", "" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NewFormationLeader;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewFormationOffset;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSAIController_SetFormationMovement_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((RTSAIController_eventSetFormationMovement_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSAIController_SetFormationMovement_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAIController_eventSetFormationMovement_Parms), &Z_Construct_UFunction_ARTSAIController_SetFormationMovement_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSAIController_SetFormationMovement_Statics::NewProp_NewFormationLeader = { "NewFormationLeader", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventSetFormationMovement_Parms, NewFormationLeader), Z_Construct_UClass_ARTSAIController_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSAIController_SetFormationMovement_Statics::NewProp_NewFormationOffset = { "NewFormationOffset", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventSetFormationMovement_Parms, NewFormationOffset), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_SetFormationMovement_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_SetFormationMovement_Statics::NewProp_bEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_SetFormationMovement_Statics::NewProp_NewFormationLeader,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_SetFormationMovement_Statics::NewProp_NewFormationOffset,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_SetFormationMovement_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_SetFormationMovement_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "SetFormationMovement", Z_Construct_UFunction_ARTSAIController_SetFormationMovement_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_SetFormationMovement_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_SetFormationMovement_Statics::RTSAIController_eventSetFormationMovement_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_SetFormationMovement_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_SetFormationMovement_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_SetFormationMovement_Statics::RTSAIController_eventSetFormationMovement_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_SetFormationMovement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_SetFormationMovement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execSetFormationMovement)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_GET_OBJECT(ARTSAIController,Z_Param_NewFormationLeader);
	P_GET_STRUCT(FVector,Z_Param_NewFormationOffset);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetFormationMovement(Z_Param_bEnabled,Z_Param_NewFormationLeader,Z_Param_NewFormationOffset);
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function SetFormationMovement *****************************

// ********** Begin Class ARTSAIController Function SetGroupFormation ******************************
struct Z_Construct_UFunction_ARTSAIController_SetGroupFormation_Statics
{
	struct RTSAIController_eventSetGroupFormation_Parms
	{
		ERTSFormationType Formation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Group" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Formation_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Formation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSAIController_SetGroupFormation_Statics::NewProp_Formation_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSAIController_SetGroupFormation_Statics::NewProp_Formation = { "Formation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventSetGroupFormation_Parms, Formation), Z_Construct_UEnum_ArmorWars_ERTSFormationType, METADATA_PARAMS(0, nullptr) }; // 1472012862
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_SetGroupFormation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_SetGroupFormation_Statics::NewProp_Formation_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_SetGroupFormation_Statics::NewProp_Formation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_SetGroupFormation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_SetGroupFormation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "SetGroupFormation", Z_Construct_UFunction_ARTSAIController_SetGroupFormation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_SetGroupFormation_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_SetGroupFormation_Statics::RTSAIController_eventSetGroupFormation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_SetGroupFormation_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_SetGroupFormation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_SetGroupFormation_Statics::RTSAIController_eventSetGroupFormation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_SetGroupFormation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_SetGroupFormation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execSetGroupFormation)
{
	P_GET_ENUM(ERTSFormationType,Z_Param_Formation);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetGroupFormation(ERTSFormationType(Z_Param_Formation));
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function SetGroupFormation ********************************

// ********** Begin Class ARTSAIController Function SetMovementSpeed *******************************
struct Z_Construct_UFunction_ARTSAIController_SetMovementSpeed_Statics
{
	struct RTSAIController_eventSetMovementSpeed_Parms
	{
		float NewSpeed;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Movement" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewSpeed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSAIController_SetMovementSpeed_Statics::NewProp_NewSpeed = { "NewSpeed", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventSetMovementSpeed_Parms, NewSpeed), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_SetMovementSpeed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_SetMovementSpeed_Statics::NewProp_NewSpeed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_SetMovementSpeed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_SetMovementSpeed_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "SetMovementSpeed", Z_Construct_UFunction_ARTSAIController_SetMovementSpeed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_SetMovementSpeed_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_SetMovementSpeed_Statics::RTSAIController_eventSetMovementSpeed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_SetMovementSpeed_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_SetMovementSpeed_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_SetMovementSpeed_Statics::RTSAIController_eventSetMovementSpeed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_SetMovementSpeed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_SetMovementSpeed_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execSetMovementSpeed)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewSpeed);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetMovementSpeed(Z_Param_NewSpeed);
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function SetMovementSpeed *********************************

// ********** Begin Class ARTSAIController Function SetPatrolPoints ********************************
struct Z_Construct_UFunction_ARTSAIController_SetPatrolPoints_Statics
{
	struct RTSAIController_eventSetPatrolPoints_Parms
	{
		TArray<FVector> Points;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Control" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Points_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Points_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Points;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSAIController_SetPatrolPoints_Statics::NewProp_Points_Inner = { "Points", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ARTSAIController_SetPatrolPoints_Statics::NewProp_Points = { "Points", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventSetPatrolPoints_Parms, Points), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Points_MetaData), NewProp_Points_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_SetPatrolPoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_SetPatrolPoints_Statics::NewProp_Points_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_SetPatrolPoints_Statics::NewProp_Points,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_SetPatrolPoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_SetPatrolPoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "SetPatrolPoints", Z_Construct_UFunction_ARTSAIController_SetPatrolPoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_SetPatrolPoints_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_SetPatrolPoints_Statics::RTSAIController_eventSetPatrolPoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_SetPatrolPoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_SetPatrolPoints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_SetPatrolPoints_Statics::RTSAIController_eventSetPatrolPoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_SetPatrolPoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_SetPatrolPoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execSetPatrolPoints)
{
	P_GET_TARRAY_REF(FVector,Z_Param_Out_Points);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetPatrolPoints(Z_Param_Out_Points);
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function SetPatrolPoints **********************************

// ********** Begin Class ARTSAIController Function SetRetreatThreshold ****************************
struct Z_Construct_UFunction_ARTSAIController_SetRetreatThreshold_Statics
{
	struct RTSAIController_eventSetRetreatThreshold_Parms
	{
		float HealthPercentage;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Combat" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HealthPercentage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSAIController_SetRetreatThreshold_Statics::NewProp_HealthPercentage = { "HealthPercentage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventSetRetreatThreshold_Parms, HealthPercentage), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_SetRetreatThreshold_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_SetRetreatThreshold_Statics::NewProp_HealthPercentage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_SetRetreatThreshold_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_SetRetreatThreshold_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "SetRetreatThreshold", Z_Construct_UFunction_ARTSAIController_SetRetreatThreshold_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_SetRetreatThreshold_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_SetRetreatThreshold_Statics::RTSAIController_eventSetRetreatThreshold_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_SetRetreatThreshold_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_SetRetreatThreshold_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_SetRetreatThreshold_Statics::RTSAIController_eventSetRetreatThreshold_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_SetRetreatThreshold()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_SetRetreatThreshold_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execSetRetreatThreshold)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_HealthPercentage);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetRetreatThreshold(Z_Param_HealthPercentage);
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function SetRetreatThreshold ******************************

// ********** Begin Class ARTSAIController Function ShouldFlank ************************************
struct Z_Construct_UFunction_ARTSAIController_ShouldFlank_Statics
{
	struct RTSAIController_eventShouldFlank_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Combat" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSAIController_ShouldFlank_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSAIController_eventShouldFlank_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSAIController_ShouldFlank_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAIController_eventShouldFlank_Parms), &Z_Construct_UFunction_ARTSAIController_ShouldFlank_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_ShouldFlank_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_ShouldFlank_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_ShouldFlank_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_ShouldFlank_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "ShouldFlank", Z_Construct_UFunction_ARTSAIController_ShouldFlank_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_ShouldFlank_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_ShouldFlank_Statics::RTSAIController_eventShouldFlank_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_ShouldFlank_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_ShouldFlank_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_ShouldFlank_Statics::RTSAIController_eventShouldFlank_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_ShouldFlank()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_ShouldFlank_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execShouldFlank)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ShouldFlank();
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function ShouldFlank **************************************

// ********** Begin Class ARTSAIController Function ShouldKite *************************************
struct Z_Construct_UFunction_ARTSAIController_ShouldKite_Statics
{
	struct RTSAIController_eventShouldKite_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Combat" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSAIController_ShouldKite_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSAIController_eventShouldKite_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSAIController_ShouldKite_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAIController_eventShouldKite_Parms), &Z_Construct_UFunction_ARTSAIController_ShouldKite_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_ShouldKite_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_ShouldKite_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_ShouldKite_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_ShouldKite_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "ShouldKite", Z_Construct_UFunction_ARTSAIController_ShouldKite_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_ShouldKite_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_ShouldKite_Statics::RTSAIController_eventShouldKite_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_ShouldKite_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_ShouldKite_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_ShouldKite_Statics::RTSAIController_eventShouldKite_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_ShouldKite()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_ShouldKite_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execShouldKite)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ShouldKite();
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function ShouldKite ***************************************

// ********** Begin Class ARTSAIController Function ShouldRetreat **********************************
struct Z_Construct_UFunction_ARTSAIController_ShouldRetreat_Statics
{
	struct RTSAIController_eventShouldRetreat_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI State" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSAIController_ShouldRetreat_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSAIController_eventShouldRetreat_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSAIController_ShouldRetreat_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAIController_eventShouldRetreat_Parms), &Z_Construct_UFunction_ARTSAIController_ShouldRetreat_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_ShouldRetreat_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_ShouldRetreat_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_ShouldRetreat_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_ShouldRetreat_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "ShouldRetreat", Z_Construct_UFunction_ARTSAIController_ShouldRetreat_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_ShouldRetreat_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_ShouldRetreat_Statics::RTSAIController_eventShouldRetreat_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_ShouldRetreat_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_ShouldRetreat_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_ShouldRetreat_Statics::RTSAIController_eventShouldRetreat_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_ShouldRetreat()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_ShouldRetreat_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execShouldRetreat)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ShouldRetreat();
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function ShouldRetreat ************************************

// ********** Begin Class ARTSAIController Function StartBehaviorTree ******************************
struct Z_Construct_UFunction_ARTSAIController_StartBehaviorTree_Statics
{
	struct RTSAIController_eventStartBehaviorTree_Parms
	{
		URTSBehaviorNode* RootNode;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Behavior Tree" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Behavior Tree Functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Behavior Tree Functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RootNode;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSAIController_StartBehaviorTree_Statics::NewProp_RootNode = { "RootNode", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIController_eventStartBehaviorTree_Parms, RootNode), Z_Construct_UClass_URTSBehaviorNode_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAIController_StartBehaviorTree_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAIController_StartBehaviorTree_Statics::NewProp_RootNode,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_StartBehaviorTree_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_StartBehaviorTree_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "StartBehaviorTree", Z_Construct_UFunction_ARTSAIController_StartBehaviorTree_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_StartBehaviorTree_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAIController_StartBehaviorTree_Statics::RTSAIController_eventStartBehaviorTree_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_StartBehaviorTree_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_StartBehaviorTree_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAIController_StartBehaviorTree_Statics::RTSAIController_eventStartBehaviorTree_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAIController_StartBehaviorTree()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_StartBehaviorTree_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execStartBehaviorTree)
{
	P_GET_OBJECT(URTSBehaviorNode,Z_Param_RootNode);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartBehaviorTree(Z_Param_RootNode);
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function StartBehaviorTree ********************************

// ********** Begin Class ARTSAIController Function StopBehaviorTree *******************************
struct Z_Construct_UFunction_ARTSAIController_StopBehaviorTree_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Behavior Tree" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAIController_StopBehaviorTree_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAIController, nullptr, "StopBehaviorTree", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAIController_StopBehaviorTree_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAIController_StopBehaviorTree_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSAIController_StopBehaviorTree()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAIController_StopBehaviorTree_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAIController::execStopBehaviorTree)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopBehaviorTree();
	P_NATIVE_END;
}
// ********** End Class ARTSAIController Function StopBehaviorTree *********************************

// ********** Begin Class ARTSAIController *********************************************************
void ARTSAIController::StaticRegisterNativesARTSAIController()
{
	UClass* Class = ARTSAIController::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddKnownTarget", &ARTSAIController::execAddKnownTarget },
		{ "BroadcastToGroup", &ARTSAIController::execBroadcastToGroup },
		{ "CanSeeTarget", &ARTSAIController::execCanSeeTarget },
		{ "CoordinateAttackWithGroup", &ARTSAIController::execCoordinateAttackWithGroup },
		{ "CreateCombatBehaviorTree", &ARTSAIController::execCreateCombatBehaviorTree },
		{ "CreateDefaultBehaviorTree", &ARTSAIController::execCreateDefaultBehaviorTree },
		{ "CreatePatrolBehaviorTree", &ARTSAIController::execCreatePatrolBehaviorTree },
		{ "DetectEnemiesInRange", &ARTSAIController::execDetectEnemiesInRange },
		{ "EnableCoverSeeking", &ARTSAIController::execEnableCoverSeeking },
		{ "EnableKitingBehavior", &ARTSAIController::execEnableKitingBehavior },
		{ "EnableObstacleAvoidance", &ARTSAIController::execEnableObstacleAvoidance },
		{ "FindBestTarget", &ARTSAIController::execFindBestTarget },
		{ "GetDistanceToDefendPosition", &ARTSAIController::execGetDistanceToDefendPosition },
		{ "HasReachedDestination", &ARTSAIController::execHasReachedDestination },
		{ "HasValidTarget", &ARTSAIController::execHasValidTarget },
		{ "IsBehaviorTreeRunning", &ARTSAIController::execIsBehaviorTreeRunning },
		{ "IsGroupLeader", &ARTSAIController::execIsGroupLeader },
		{ "IsInCombat", &ARTSAIController::execIsInCombat },
		{ "IsInGroup", &ARTSAIController::execIsInGroup },
		{ "IsInOptimalRange", &ARTSAIController::execIsInOptimalRange },
		{ "IsMoving", &ARTSAIController::execIsMoving },
		{ "JoinGroup", &ARTSAIController::execJoinGroup },
		{ "LeaveGroup", &ARTSAIController::execLeaveGroup },
		{ "MoveToLocationWithPathfinding", &ARTSAIController::execMoveToLocationWithPathfinding },
		{ "OnUnitDeath", &ARTSAIController::execOnUnitDeath },
		{ "PauseBehaviorTree", &ARTSAIController::execPauseBehaviorTree },
		{ "RemoveKnownTarget", &ARTSAIController::execRemoveKnownTarget },
		{ "ResumeBehaviorTree", &ARTSAIController::execResumeBehaviorTree },
		{ "SetAIState", &ARTSAIController::execSetAIState },
		{ "SetCombatBehavior", &ARTSAIController::execSetCombatBehavior },
		{ "SetDefendPosition", &ARTSAIController::execSetDefendPosition },
		{ "SetEngagementRange", &ARTSAIController::execSetEngagementRange },
		{ "SetFlankingBehavior", &ARTSAIController::execSetFlankingBehavior },
		{ "SetFollowTarget", &ARTSAIController::execSetFollowTarget },
		{ "SetFormationMovement", &ARTSAIController::execSetFormationMovement },
		{ "SetGroupFormation", &ARTSAIController::execSetGroupFormation },
		{ "SetMovementSpeed", &ARTSAIController::execSetMovementSpeed },
		{ "SetPatrolPoints", &ARTSAIController::execSetPatrolPoints },
		{ "SetRetreatThreshold", &ARTSAIController::execSetRetreatThreshold },
		{ "ShouldFlank", &ARTSAIController::execShouldFlank },
		{ "ShouldKite", &ARTSAIController::execShouldKite },
		{ "ShouldRetreat", &ARTSAIController::execShouldRetreat },
		{ "StartBehaviorTree", &ARTSAIController::execStartBehaviorTree },
		{ "StopBehaviorTree", &ARTSAIController::execStopBehaviorTree },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_ARTSAIController;
UClass* ARTSAIController::GetPrivateStaticClass()
{
	using TClass = ARTSAIController;
	if (!Z_Registration_Info_UClass_ARTSAIController.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSAIController"),
			Z_Registration_Info_UClass_ARTSAIController.InnerSingleton,
			StaticRegisterNativesARTSAIController,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_ARTSAIController.InnerSingleton;
}
UClass* Z_Construct_UClass_ARTSAIController_NoRegister()
{
	return ARTSAIController::GetPrivateStaticClass();
}
struct Z_Construct_UClass_ARTSAIController_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * AI Controller for RTS units with autonomous behavior\n * Handles target acquisition, state management, and tactical decisions\n */" },
#endif
		{ "HideCategories", "Collision Rendering Transformation" },
		{ "IncludePath", "RTSAIController.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "AI Controller for RTS units with autonomous behavior\nHandles target acquisition, state management, and tactical decisions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentState_MetaData[] = {
		{ "Category", "AI State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Current AI state\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current AI state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DetectionRange_MetaData[] = {
		{ "Category", "AI Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Detection range for enemies\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Detection range for enemies" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackRange_MetaData[] = {
		{ "Category", "AI Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Attack range (will use unit's attack range if 0)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Attack range (will use unit's attack range if 0)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AIUpdateInterval_MetaData[] = {
		{ "Category", "AI Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// How often to update AI logic (seconds)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "How often to update AI logic (seconds)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsAggressive_MetaData[] = {
		{ "Category", "AI Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Whether this AI should be aggressive\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether this AI should be aggressive" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShouldDefend_MetaData[] = {
		{ "Category", "AI Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Whether this AI should defend its position\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether this AI should defend its position" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PatrolPoints_MetaData[] = {
		{ "Category", "AI Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Patrol points for patrol behavior\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Patrol points for patrol behavior" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentPatrolIndex_MetaData[] = {
		{ "Category", "AI State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Current patrol index\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current patrol index" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentTarget_MetaData[] = {
		{ "Category", "AI State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Current target information\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current target information" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_KnownTargets_MetaData[] = {
		{ "Category", "AI State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// List of known targets\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "List of known targets" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefendPosition_MetaData[] = {
		{ "Category", "AI Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Defend position\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Defend position" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefendRadius_MetaData[] = {
		{ "Category", "AI Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Maximum distance from defend position\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Maximum distance from defend position" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDebugLogging_MetaData[] = {
		{ "Category", "Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Whether debug logging is enabled */" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether debug logging is enabled" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BehaviorTreeComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Behavior Tree Component\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Behavior Tree Component" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUsePathfinding_MetaData[] = {
		{ "Category", "AI Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Advanced Movement State\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Advanced Movement State" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseObstacleAvoidance_MetaData[] = {
		{ "Category", "AI Movement" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MovementSpeed_MetaData[] = {
		{ "Category", "AI Movement" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ArrivalTolerance_MetaData[] = {
		{ "Category", "AI Movement" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObstacleAvoidanceRadius_MetaData[] = {
		{ "Category", "AI Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Obstacle avoidance state\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obstacle avoidance state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObstacleAvoidanceStrength_MetaData[] = {
		{ "Category", "AI Movement" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CombatBehavior_MetaData[] = {
		{ "Category", "AI Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Combat AI state\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Combat AI state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinEngagementRange_MetaData[] = {
		{ "Category", "AI Combat" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxEngagementRange_MetaData[] = {
		{ "Category", "AI Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Use weapon range if 0\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Use weapon range if 0" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseKiting_MetaData[] = {
		{ "Category", "AI Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Use weapon range if 0\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Use weapon range if 0" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_KitingDistance_MetaData[] = {
		{ "Category", "AI Combat" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseFlanking_MetaData[] = {
		{ "Category", "AI Combat" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlankingRadius_MetaData[] = {
		{ "Category", "AI Combat" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseCoverSeeking_MetaData[] = {
		{ "Category", "AI Combat" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RetreatHealthThreshold_MetaData[] = {
		{ "Category", "AI Combat" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CombatUpdateInterval_MetaData[] = {
		{ "Category", "AI Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Retreat when below 25% health\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Retreat when below 25% health" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseBehaviorTree_MetaData[] = {
		{ "Category", "AI Behavior Tree" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Behavior Tree Settings\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Behavior Tree Settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultBehaviorTree_MetaData[] = {
		{ "Category", "AI Behavior Tree" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoStartBehaviorTree_MetaData[] = {
		{ "Category", "AI Behavior Tree" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnAIStateChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnTargetAcquired_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnEnemyDetected_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/RTSAIController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentState;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DetectionRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AttackRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AIUpdateInterval;
	static void NewProp_bIsAggressive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsAggressive;
	static void NewProp_bShouldDefend_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShouldDefend;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PatrolPoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PatrolPoints;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentPatrolIndex;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentTarget;
	static const UECodeGen_Private::FStructPropertyParams NewProp_KnownTargets_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_KnownTargets;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DefendPosition;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefendRadius;
	static void NewProp_bEnableDebugLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDebugLogging;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BehaviorTreeComponent;
	static void NewProp_bUsePathfinding_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUsePathfinding;
	static void NewProp_bUseObstacleAvoidance_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseObstacleAvoidance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MovementSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ArrivalTolerance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ObstacleAvoidanceRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ObstacleAvoidanceStrength;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CombatBehavior_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CombatBehavior;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinEngagementRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxEngagementRange;
	static void NewProp_bUseKiting_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseKiting;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_KitingDistance;
	static void NewProp_bUseFlanking_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseFlanking;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FlankingRadius;
	static void NewProp_bUseCoverSeeking_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseCoverSeeking;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RetreatHealthThreshold;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CombatUpdateInterval;
	static void NewProp_bUseBehaviorTree_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseBehaviorTree;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DefaultBehaviorTree;
	static void NewProp_bAutoStartBehaviorTree_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoStartBehaviorTree;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnAIStateChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnTargetAcquired;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnEnemyDetected;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_ARTSAIController_AddKnownTarget, "AddKnownTarget" }, // 3369943632
		{ &Z_Construct_UFunction_ARTSAIController_BroadcastToGroup, "BroadcastToGroup" }, // 1023152558
		{ &Z_Construct_UFunction_ARTSAIController_CanSeeTarget, "CanSeeTarget" }, // 3968426645
		{ &Z_Construct_UFunction_ARTSAIController_CoordinateAttackWithGroup, "CoordinateAttackWithGroup" }, // 2848753866
		{ &Z_Construct_UFunction_ARTSAIController_CreateCombatBehaviorTree, "CreateCombatBehaviorTree" }, // 1330584502
		{ &Z_Construct_UFunction_ARTSAIController_CreateDefaultBehaviorTree, "CreateDefaultBehaviorTree" }, // 3994050042
		{ &Z_Construct_UFunction_ARTSAIController_CreatePatrolBehaviorTree, "CreatePatrolBehaviorTree" }, // 3929902076
		{ &Z_Construct_UFunction_ARTSAIController_DetectEnemiesInRange, "DetectEnemiesInRange" }, // 743395241
		{ &Z_Construct_UFunction_ARTSAIController_EnableCoverSeeking, "EnableCoverSeeking" }, // 2930145805
		{ &Z_Construct_UFunction_ARTSAIController_EnableKitingBehavior, "EnableKitingBehavior" }, // 3144244228
		{ &Z_Construct_UFunction_ARTSAIController_EnableObstacleAvoidance, "EnableObstacleAvoidance" }, // 777230284
		{ &Z_Construct_UFunction_ARTSAIController_FindBestTarget, "FindBestTarget" }, // 3723027234
		{ &Z_Construct_UFunction_ARTSAIController_GetDistanceToDefendPosition, "GetDistanceToDefendPosition" }, // 1545139654
		{ &Z_Construct_UFunction_ARTSAIController_HasReachedDestination, "HasReachedDestination" }, // 4291823107
		{ &Z_Construct_UFunction_ARTSAIController_HasValidTarget, "HasValidTarget" }, // 1570079096
		{ &Z_Construct_UFunction_ARTSAIController_IsBehaviorTreeRunning, "IsBehaviorTreeRunning" }, // 1694425072
		{ &Z_Construct_UFunction_ARTSAIController_IsGroupLeader, "IsGroupLeader" }, // 1841678176
		{ &Z_Construct_UFunction_ARTSAIController_IsInCombat, "IsInCombat" }, // 1951220678
		{ &Z_Construct_UFunction_ARTSAIController_IsInGroup, "IsInGroup" }, // 1536701597
		{ &Z_Construct_UFunction_ARTSAIController_IsInOptimalRange, "IsInOptimalRange" }, // 1438119447
		{ &Z_Construct_UFunction_ARTSAIController_IsMoving, "IsMoving" }, // 2863398723
		{ &Z_Construct_UFunction_ARTSAIController_JoinGroup, "JoinGroup" }, // 1898298356
		{ &Z_Construct_UFunction_ARTSAIController_LeaveGroup, "LeaveGroup" }, // 1032836870
		{ &Z_Construct_UFunction_ARTSAIController_MoveToLocationWithPathfinding, "MoveToLocationWithPathfinding" }, // 4016042635
		{ &Z_Construct_UDelegateFunction_ARTSAIController_OnAIStateChanged__DelegateSignature, "OnAIStateChanged__DelegateSignature" }, // 2203862463
		{ &Z_Construct_UDelegateFunction_ARTSAIController_OnEnemyDetected__DelegateSignature, "OnEnemyDetected__DelegateSignature" }, // 3610772013
		{ &Z_Construct_UFunction_ARTSAIController_OnEnemyDetectedEvent, "OnEnemyDetectedEvent" }, // 1595423301
		{ &Z_Construct_UFunction_ARTSAIController_OnStateChanged, "OnStateChanged" }, // 2247250448
		{ &Z_Construct_UDelegateFunction_ARTSAIController_OnTargetAcquired__DelegateSignature, "OnTargetAcquired__DelegateSignature" }, // 1170378601
		{ &Z_Construct_UFunction_ARTSAIController_OnTargetAcquiredEvent, "OnTargetAcquiredEvent" }, // 363738045
		{ &Z_Construct_UFunction_ARTSAIController_OnTargetLost, "OnTargetLost" }, // 1521714159
		{ &Z_Construct_UFunction_ARTSAIController_OnUnitDeath, "OnUnitDeath" }, // 758541992
		{ &Z_Construct_UFunction_ARTSAIController_PauseBehaviorTree, "PauseBehaviorTree" }, // 4213413713
		{ &Z_Construct_UFunction_ARTSAIController_RemoveKnownTarget, "RemoveKnownTarget" }, // 103421994
		{ &Z_Construct_UFunction_ARTSAIController_ResumeBehaviorTree, "ResumeBehaviorTree" }, // 258805465
		{ &Z_Construct_UFunction_ARTSAIController_SetAIState, "SetAIState" }, // 1319572812
		{ &Z_Construct_UFunction_ARTSAIController_SetCombatBehavior, "SetCombatBehavior" }, // 4287849957
		{ &Z_Construct_UFunction_ARTSAIController_SetDefendPosition, "SetDefendPosition" }, // 189135263
		{ &Z_Construct_UFunction_ARTSAIController_SetEngagementRange, "SetEngagementRange" }, // 2061672004
		{ &Z_Construct_UFunction_ARTSAIController_SetFlankingBehavior, "SetFlankingBehavior" }, // 1971013584
		{ &Z_Construct_UFunction_ARTSAIController_SetFollowTarget, "SetFollowTarget" }, // 1246078508
		{ &Z_Construct_UFunction_ARTSAIController_SetFormationMovement, "SetFormationMovement" }, // 3438419081
		{ &Z_Construct_UFunction_ARTSAIController_SetGroupFormation, "SetGroupFormation" }, // 3991515053
		{ &Z_Construct_UFunction_ARTSAIController_SetMovementSpeed, "SetMovementSpeed" }, // 2176684660
		{ &Z_Construct_UFunction_ARTSAIController_SetPatrolPoints, "SetPatrolPoints" }, // 1945479410
		{ &Z_Construct_UFunction_ARTSAIController_SetRetreatThreshold, "SetRetreatThreshold" }, // 3852852450
		{ &Z_Construct_UFunction_ARTSAIController_ShouldFlank, "ShouldFlank" }, // 3194256110
		{ &Z_Construct_UFunction_ARTSAIController_ShouldKite, "ShouldKite" }, // 2296854591
		{ &Z_Construct_UFunction_ARTSAIController_ShouldRetreat, "ShouldRetreat" }, // 3817341084
		{ &Z_Construct_UFunction_ARTSAIController_StartBehaviorTree, "StartBehaviorTree" }, // 1884764582
		{ &Z_Construct_UFunction_ARTSAIController_StopBehaviorTree, "StopBehaviorTree" }, // 2576575112
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<ARTSAIController>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_CurrentState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_CurrentState = { "CurrentState", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAIController, CurrentState), Z_Construct_UEnum_ArmorWars_ERTSAIState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentState_MetaData), NewProp_CurrentState_MetaData) }; // 1008175298
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_DetectionRange = { "DetectionRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAIController, DetectionRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DetectionRange_MetaData), NewProp_DetectionRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_AttackRange = { "AttackRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAIController, AttackRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackRange_MetaData), NewProp_AttackRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_AIUpdateInterval = { "AIUpdateInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAIController, AIUpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AIUpdateInterval_MetaData), NewProp_AIUpdateInterval_MetaData) };
void Z_Construct_UClass_ARTSAIController_Statics::NewProp_bIsAggressive_SetBit(void* Obj)
{
	((ARTSAIController*)Obj)->bIsAggressive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_bIsAggressive = { "bIsAggressive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARTSAIController), &Z_Construct_UClass_ARTSAIController_Statics::NewProp_bIsAggressive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsAggressive_MetaData), NewProp_bIsAggressive_MetaData) };
void Z_Construct_UClass_ARTSAIController_Statics::NewProp_bShouldDefend_SetBit(void* Obj)
{
	((ARTSAIController*)Obj)->bShouldDefend = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_bShouldDefend = { "bShouldDefend", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARTSAIController), &Z_Construct_UClass_ARTSAIController_Statics::NewProp_bShouldDefend_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShouldDefend_MetaData), NewProp_bShouldDefend_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_PatrolPoints_Inner = { "PatrolPoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_PatrolPoints = { "PatrolPoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAIController, PatrolPoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PatrolPoints_MetaData), NewProp_PatrolPoints_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_CurrentPatrolIndex = { "CurrentPatrolIndex", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAIController, CurrentPatrolIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentPatrolIndex_MetaData), NewProp_CurrentPatrolIndex_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_CurrentTarget = { "CurrentTarget", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAIController, CurrentTarget), Z_Construct_UScriptStruct_FRTSAITargetInfo, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentTarget_MetaData), NewProp_CurrentTarget_MetaData) }; // 3480857937
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_KnownTargets_Inner = { "KnownTargets", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FRTSAITargetInfo, METADATA_PARAMS(0, nullptr) }; // 3480857937
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_KnownTargets = { "KnownTargets", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAIController, KnownTargets), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_KnownTargets_MetaData), NewProp_KnownTargets_MetaData) }; // 3480857937
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_DefendPosition = { "DefendPosition", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAIController, DefendPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefendPosition_MetaData), NewProp_DefendPosition_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_DefendRadius = { "DefendRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAIController, DefendRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefendRadius_MetaData), NewProp_DefendRadius_MetaData) };
void Z_Construct_UClass_ARTSAIController_Statics::NewProp_bEnableDebugLogging_SetBit(void* Obj)
{
	((ARTSAIController*)Obj)->bEnableDebugLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_bEnableDebugLogging = { "bEnableDebugLogging", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARTSAIController), &Z_Construct_UClass_ARTSAIController_Statics::NewProp_bEnableDebugLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDebugLogging_MetaData), NewProp_bEnableDebugLogging_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_BehaviorTreeComponent = { "BehaviorTreeComponent", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAIController, BehaviorTreeComponent), Z_Construct_UClass_URTSBehaviorTreeComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BehaviorTreeComponent_MetaData), NewProp_BehaviorTreeComponent_MetaData) };
void Z_Construct_UClass_ARTSAIController_Statics::NewProp_bUsePathfinding_SetBit(void* Obj)
{
	((ARTSAIController*)Obj)->bUsePathfinding = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_bUsePathfinding = { "bUsePathfinding", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARTSAIController), &Z_Construct_UClass_ARTSAIController_Statics::NewProp_bUsePathfinding_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUsePathfinding_MetaData), NewProp_bUsePathfinding_MetaData) };
void Z_Construct_UClass_ARTSAIController_Statics::NewProp_bUseObstacleAvoidance_SetBit(void* Obj)
{
	((ARTSAIController*)Obj)->bUseObstacleAvoidance = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_bUseObstacleAvoidance = { "bUseObstacleAvoidance", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARTSAIController), &Z_Construct_UClass_ARTSAIController_Statics::NewProp_bUseObstacleAvoidance_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseObstacleAvoidance_MetaData), NewProp_bUseObstacleAvoidance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_MovementSpeed = { "MovementSpeed", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAIController, MovementSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MovementSpeed_MetaData), NewProp_MovementSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_ArrivalTolerance = { "ArrivalTolerance", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAIController, ArrivalTolerance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ArrivalTolerance_MetaData), NewProp_ArrivalTolerance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_ObstacleAvoidanceRadius = { "ObstacleAvoidanceRadius", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAIController, ObstacleAvoidanceRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObstacleAvoidanceRadius_MetaData), NewProp_ObstacleAvoidanceRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_ObstacleAvoidanceStrength = { "ObstacleAvoidanceStrength", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAIController, ObstacleAvoidanceStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObstacleAvoidanceStrength_MetaData), NewProp_ObstacleAvoidanceStrength_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_CombatBehavior_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_CombatBehavior = { "CombatBehavior", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAIController, CombatBehavior), Z_Construct_UEnum_ArmorWars_ERTSCombatBehavior, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CombatBehavior_MetaData), NewProp_CombatBehavior_MetaData) }; // 3322899454
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_MinEngagementRange = { "MinEngagementRange", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAIController, MinEngagementRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinEngagementRange_MetaData), NewProp_MinEngagementRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_MaxEngagementRange = { "MaxEngagementRange", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAIController, MaxEngagementRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxEngagementRange_MetaData), NewProp_MaxEngagementRange_MetaData) };
void Z_Construct_UClass_ARTSAIController_Statics::NewProp_bUseKiting_SetBit(void* Obj)
{
	((ARTSAIController*)Obj)->bUseKiting = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_bUseKiting = { "bUseKiting", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARTSAIController), &Z_Construct_UClass_ARTSAIController_Statics::NewProp_bUseKiting_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseKiting_MetaData), NewProp_bUseKiting_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_KitingDistance = { "KitingDistance", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAIController, KitingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_KitingDistance_MetaData), NewProp_KitingDistance_MetaData) };
void Z_Construct_UClass_ARTSAIController_Statics::NewProp_bUseFlanking_SetBit(void* Obj)
{
	((ARTSAIController*)Obj)->bUseFlanking = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_bUseFlanking = { "bUseFlanking", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARTSAIController), &Z_Construct_UClass_ARTSAIController_Statics::NewProp_bUseFlanking_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseFlanking_MetaData), NewProp_bUseFlanking_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_FlankingRadius = { "FlankingRadius", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAIController, FlankingRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlankingRadius_MetaData), NewProp_FlankingRadius_MetaData) };
void Z_Construct_UClass_ARTSAIController_Statics::NewProp_bUseCoverSeeking_SetBit(void* Obj)
{
	((ARTSAIController*)Obj)->bUseCoverSeeking = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_bUseCoverSeeking = { "bUseCoverSeeking", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARTSAIController), &Z_Construct_UClass_ARTSAIController_Statics::NewProp_bUseCoverSeeking_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseCoverSeeking_MetaData), NewProp_bUseCoverSeeking_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_RetreatHealthThreshold = { "RetreatHealthThreshold", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAIController, RetreatHealthThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RetreatHealthThreshold_MetaData), NewProp_RetreatHealthThreshold_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_CombatUpdateInterval = { "CombatUpdateInterval", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAIController, CombatUpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CombatUpdateInterval_MetaData), NewProp_CombatUpdateInterval_MetaData) };
void Z_Construct_UClass_ARTSAIController_Statics::NewProp_bUseBehaviorTree_SetBit(void* Obj)
{
	((ARTSAIController*)Obj)->bUseBehaviorTree = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_bUseBehaviorTree = { "bUseBehaviorTree", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARTSAIController), &Z_Construct_UClass_ARTSAIController_Statics::NewProp_bUseBehaviorTree_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseBehaviorTree_MetaData), NewProp_bUseBehaviorTree_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_DefaultBehaviorTree = { "DefaultBehaviorTree", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAIController, DefaultBehaviorTree), Z_Construct_UClass_URTSBehaviorNode_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultBehaviorTree_MetaData), NewProp_DefaultBehaviorTree_MetaData) };
void Z_Construct_UClass_ARTSAIController_Statics::NewProp_bAutoStartBehaviorTree_SetBit(void* Obj)
{
	((ARTSAIController*)Obj)->bAutoStartBehaviorTree = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_bAutoStartBehaviorTree = { "bAutoStartBehaviorTree", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARTSAIController), &Z_Construct_UClass_ARTSAIController_Statics::NewProp_bAutoStartBehaviorTree_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoStartBehaviorTree_MetaData), NewProp_bAutoStartBehaviorTree_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_OnAIStateChanged = { "OnAIStateChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAIController, OnAIStateChanged), Z_Construct_UDelegateFunction_ARTSAIController_OnAIStateChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnAIStateChanged_MetaData), NewProp_OnAIStateChanged_MetaData) }; // 2203862463
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_OnTargetAcquired = { "OnTargetAcquired", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAIController, OnTargetAcquired), Z_Construct_UDelegateFunction_ARTSAIController_OnTargetAcquired__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnTargetAcquired_MetaData), NewProp_OnTargetAcquired_MetaData) }; // 1170378601
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ARTSAIController_Statics::NewProp_OnEnemyDetected = { "OnEnemyDetected", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAIController, OnEnemyDetected), Z_Construct_UDelegateFunction_ARTSAIController_OnEnemyDetected__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnEnemyDetected_MetaData), NewProp_OnEnemyDetected_MetaData) }; // 3610772013
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_ARTSAIController_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_CurrentState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_CurrentState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_DetectionRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_AttackRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_AIUpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_bIsAggressive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_bShouldDefend,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_PatrolPoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_PatrolPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_CurrentPatrolIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_CurrentTarget,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_KnownTargets_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_KnownTargets,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_DefendPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_DefendRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_bEnableDebugLogging,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_BehaviorTreeComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_bUsePathfinding,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_bUseObstacleAvoidance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_MovementSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_ArrivalTolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_ObstacleAvoidanceRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_ObstacleAvoidanceStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_CombatBehavior_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_CombatBehavior,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_MinEngagementRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_MaxEngagementRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_bUseKiting,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_KitingDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_bUseFlanking,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_FlankingRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_bUseCoverSeeking,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_RetreatHealthThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_CombatUpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_bUseBehaviorTree,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_DefaultBehaviorTree,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_bAutoStartBehaviorTree,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_OnAIStateChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_OnTargetAcquired,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAIController_Statics::NewProp_OnEnemyDetected,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ARTSAIController_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_ARTSAIController_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AAIController,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ARTSAIController_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_ARTSAIController_Statics::ClassParams = {
	&ARTSAIController::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_ARTSAIController_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_ARTSAIController_Statics::PropPointers),
	0,
	0x009003A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_ARTSAIController_Statics::Class_MetaDataParams), Z_Construct_UClass_ARTSAIController_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_ARTSAIController()
{
	if (!Z_Registration_Info_UClass_ARTSAIController.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_ARTSAIController.OuterSingleton, Z_Construct_UClass_ARTSAIController_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_ARTSAIController.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(ARTSAIController);
ARTSAIController::~ARTSAIController() {}
// ********** End Class ARTSAIController ***********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSAIController_h__Script_ArmorWars_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ERTSAIState_StaticEnum, TEXT("ERTSAIState"), &Z_Registration_Info_UEnum_ERTSAIState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1008175298U) },
		{ ERTSAIPriority_StaticEnum, TEXT("ERTSAIPriority"), &Z_Registration_Info_UEnum_ERTSAIPriority, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2759675784U) },
		{ ERTSCombatBehavior_StaticEnum, TEXT("ERTSCombatBehavior"), &Z_Registration_Info_UEnum_ERTSCombatBehavior, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3322899454U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FRTSAITargetInfo::StaticStruct, Z_Construct_UScriptStruct_FRTSAITargetInfo_Statics::NewStructOps, TEXT("RTSAITargetInfo"), &Z_Registration_Info_UScriptStruct_FRTSAITargetInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FRTSAITargetInfo), 3480857937U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_ARTSAIController, ARTSAIController::StaticClass, TEXT("ARTSAIController"), &Z_Registration_Info_UClass_ARTSAIController, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(ARTSAIController), 2272347309U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSAIController_h__Script_ArmorWars_3311331402(TEXT("/Script/ArmorWars"),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSAIController_h__Script_ArmorWars_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSAIController_h__Script_ArmorWars_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSAIController_h__Script_ArmorWars_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSAIController_h__Script_ArmorWars_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSAIController_h__Script_ArmorWars_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSAIController_h__Script_ArmorWars_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
