#include "RTSCombatBehaviorTree.h"
#include "RTSBehaviorTreeComponent.h"
#include "RTSUnit.h"
#include "RTSCommandComponent.h"
#include "RTSTeamManager.h"
#include "RTSFormationManager.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Kismet/GameplayStatics.h"

// URTSCombatBehaviorTreeFactory Implementation

URTSCombatBehaviorTreeFactory::URTSCombatBehaviorTreeFactory()
{
}

URTSBehaviorNode* URTSCombatBehaviorTreeFactory::CreateBasicCombatTree()
{
    // Create root selector
    URTSCompositeNode* RootSelector = NewObject<URTSCompositeNode>();
    RootSelector->CompositeType = ERTSCompositeType::Selector;
    RootSelector->NodeName = TEXT("Basic Combat Root");
    
    // Target acquisition and engagement
    URTSCompositeNode* CombatSequence = NewObject<URTSCompositeNode>();
    CombatSequence->CompositeType = ERTSCompositeType::Sequence;
    CombatSequence->NodeName = TEXT("Combat Sequence");
    
    // Find and select target
    URTSAdvancedTargetSelectionTask* TargetSelection = NewObject<URTSAdvancedTargetSelectionTask>();
    CombatSequence->AddChildNode(TargetSelection);
    
    // Engage target
    URTSEnhancedCombatTask* CombatTask = NewObject<URTSEnhancedCombatTask>();
    CombatTask->bAutoSelectTargets = false; // Use selected target
    CombatTask->TargetActorKey = TEXT("SelectedTarget");
    CombatSequence->AddChildNode(CombatTask);
    
    RootSelector->AddChildNode(CombatSequence);
    
    return RootSelector;
}

URTSBehaviorNode* URTSCombatBehaviorTreeFactory::CreateTacticalCombatTree()
{
    // Create root selector
    URTSCompositeNode* RootSelector = NewObject<URTSCompositeNode>();
    RootSelector->CompositeType = ERTSCompositeType::Selector;
    RootSelector->NodeName = TEXT("Tactical Combat Root");
    
    // Tactical advantage check - DISABLED due to missing implementations
    // URTSCompositeNode* AdvantageSequence = NewObject<URTSCompositeNode>();
    // AdvantageSequence->CompositeType = ERTSCompositeType::Sequence;
    // AdvantageSequence->NodeName = TEXT("Tactical Advantage");

    // URTSTacticalAdvantageCondition* AdvantageCheck = NewObject<URTSTacticalAdvantageCondition>();
    // AdvantageSequence->AddChildNode(AdvantageCheck);

    // Aggressive engagement when advantage
    // AdvantageSequence->AddChildNode(CreateEngagementSequence());
    // RootSelector->AddChildNode(AdvantageSequence);

    // Defensive positioning when disadvantaged - DISABLED due to missing implementations
    // URTSCompositeNode* DefensiveSequence = NewObject<URTSCompositeNode>();
    // DefensiveSequence->CompositeType = ERTSCompositeType::Sequence;
    // DefensiveSequence->NodeName = TEXT("Defensive Positioning");

    // URTSTacticalPositioningTask* Positioning = NewObject<URTSTacticalPositioningTask>();
    // Positioning->bSeekCover = true;
    // DefensiveSequence->AddChildNode(Positioning);

    // DefensiveSequence->AddChildNode(CreateReturnFireSequence());
    // RootSelector->AddChildNode(DefensiveSequence);
    
    return RootSelector;
}

URTSBehaviorNode* URTSCombatBehaviorTreeFactory::CreateSupportCombatTree()
{
    // Create root selector
    URTSCompositeNode* RootSelector = NewObject<URTSCompositeNode>();
    RootSelector->CompositeType = ERTSCompositeType::Selector;
    RootSelector->NodeName = TEXT("Support Combat Root");
    
    // Coordinated attack sequence
    URTSCompositeNode* CoordinatedSequence = NewObject<URTSCompositeNode>();
    CoordinatedSequence->CompositeType = ERTSCompositeType::Sequence;
    CoordinatedSequence->NodeName = TEXT("Coordinated Attack");
    
    URTSAdvancedTargetSelectionTask* TargetSelection = NewObject<URTSAdvancedTargetSelectionTask>();
    CoordinatedSequence->AddChildNode(TargetSelection);
    
    URTSCoordinatedAttackTask* CoordinatedAttack = NewObject<URTSCoordinatedAttackTask>();
    CoordinatedSequence->AddChildNode(CoordinatedAttack);
    
    RootSelector->AddChildNode(CoordinatedSequence);
    
    // Fallback to return fire
    RootSelector->AddChildNode(CreateReturnFireSequence());
    
    return RootSelector;
}

URTSBehaviorNode* URTSCombatBehaviorTreeFactory::CreateAggressiveCombatTree()
{
    // Create root selector
    URTSCompositeNode* RootSelector = NewObject<URTSCompositeNode>();
    RootSelector->CompositeType = ERTSCompositeType::Selector;
    RootSelector->NodeName = TEXT("Aggressive Combat Root");
    
    // Always engage when enemies in range
    URTSCompositeNode* AggressiveSequence = NewObject<URTSCompositeNode>();
    AggressiveSequence->CompositeType = ERTSCompositeType::Sequence;
    AggressiveSequence->NodeName = TEXT("Aggressive Engagement");
    
    URTSEnemiesInRangeCondition* EnemiesInRange = NewObject<URTSEnemiesInRangeCondition>();
    EnemiesInRange->bUseWeaponRange = true;
    AggressiveSequence->AddChildNode(EnemiesInRange);
    
    URTSAdvancedTargetSelectionTask* TargetSelection = NewObject<URTSAdvancedTargetSelectionTask>();
    TargetSelection->ThreatPriorityWeight = 1.0f; // Less threat consideration
    TargetSelection->DistancePriorityWeight = 3.0f; // Prefer closer targets
    AggressiveSequence->AddChildNode(TargetSelection);
    
    URTSEnhancedCombatTask* CombatTask = NewObject<URTSEnhancedCombatTask>();
    CombatTask->bPursueTargets = true;
    CombatTask->TargetActorKey = TEXT("SelectedTarget");
    AggressiveSequence->AddChildNode(CombatTask);
    
    RootSelector->AddChildNode(AggressiveSequence);
    
    return RootSelector;
}

URTSBehaviorNode* URTSCombatBehaviorTreeFactory::CreateDefensiveCombatTree()
{
    // Create root selector
    URTSCompositeNode* RootSelector = NewObject<URTSCompositeNode>();
    RootSelector->CompositeType = ERTSCompositeType::Selector;
    RootSelector->NodeName = TEXT("Defensive Combat Root");
    
    // Return fire when under attack
    URTSCompositeNode* DefenseSequence = NewObject<URTSCompositeNode>();
    DefenseSequence->CompositeType = ERTSCompositeType::Sequence;
    DefenseSequence->NodeName = TEXT("Defensive Response");
    
    URTSUnderAttackCondition* UnderAttack = NewObject<URTSUnderAttackCondition>();
    DefenseSequence->AddChildNode(UnderAttack);
    
    URTSReturnFireTask* ReturnFire = NewObject<URTSReturnFireTask>();
    ReturnFire->bPrioritizeAttackers = true;
    DefenseSequence->AddChildNode(ReturnFire);
    
    RootSelector->AddChildNode(DefenseSequence);
    
    // Tactical positioning
    URTSTacticalPositioningTask* Positioning = NewObject<URTSTacticalPositioningTask>();
    Positioning->bSeekCover = true;
    Positioning->bMaintainFormationPosition = true;
    RootSelector->AddChildNode(Positioning);
    
    return RootSelector;
}

URTSCompositeNode* URTSCombatBehaviorTreeFactory::CreateTargetAcquisitionSequence()
{
    URTSCompositeNode* TargetSequence = NewObject<URTSCompositeNode>();
    TargetSequence->CompositeType = ERTSCompositeType::Sequence;
    TargetSequence->NodeName = TEXT("Target Acquisition");
    
    URTSEnemiesInRangeCondition* EnemiesInRange = NewObject<URTSEnemiesInRangeCondition>();
    TargetSequence->AddChildNode(EnemiesInRange);
    
    URTSAdvancedTargetSelectionTask* TargetSelection = NewObject<URTSAdvancedTargetSelectionTask>();
    TargetSequence->AddChildNode(TargetSelection);
    
    return TargetSequence;
}

URTSCompositeNode* URTSCombatBehaviorTreeFactory::CreateEngagementSequence()
{
    URTSCompositeNode* EngagementSequence = NewObject<URTSCompositeNode>();
    EngagementSequence->CompositeType = ERTSCompositeType::Sequence;
    EngagementSequence->NodeName = TEXT("Engagement");
    
    EngagementSequence->AddChildNode(CreateTargetAcquisitionSequence());
    
    URTSEnhancedCombatTask* CombatTask = NewObject<URTSEnhancedCombatTask>();
    CombatTask->TargetActorKey = TEXT("SelectedTarget");
    EngagementSequence->AddChildNode(CombatTask);
    
    return EngagementSequence;
}

URTSCompositeNode* URTSCombatBehaviorTreeFactory::CreateTacticalPositioningSequence()
{
    URTSCompositeNode* PositioningSequence = NewObject<URTSCompositeNode>();
    PositioningSequence->CompositeType = ERTSCompositeType::Sequence;
    PositioningSequence->NodeName = TEXT("Tactical Positioning");
    
    URTSAdvancedTargetSelectionTask* TargetSelection = NewObject<URTSAdvancedTargetSelectionTask>();
    PositioningSequence->AddChildNode(TargetSelection);
    
    URTSTacticalPositioningTask* Positioning = NewObject<URTSTacticalPositioningTask>();
    PositioningSequence->AddChildNode(Positioning);
    
    return PositioningSequence;
}

URTSCompositeNode* URTSCombatBehaviorTreeFactory::CreateReturnFireSequence()
{
    URTSCompositeNode* ReturnFireSequence = NewObject<URTSCompositeNode>();
    ReturnFireSequence->CompositeType = ERTSCompositeType::Sequence;
    ReturnFireSequence->NodeName = TEXT("Return Fire");
    
    URTSUnderAttackCondition* UnderAttack = NewObject<URTSUnderAttackCondition>();
    ReturnFireSequence->AddChildNode(UnderAttack);
    
    URTSReturnFireTask* ReturnFire = NewObject<URTSReturnFireTask>();
    ReturnFireSequence->AddChildNode(ReturnFire);
    
    return ReturnFireSequence;
}

URTSCompositeNode* URTSCombatBehaviorTreeFactory::CreateRetreatSequence()
{
    URTSCompositeNode* RetreatSequence = NewObject<URTSCompositeNode>();
    RetreatSequence->CompositeType = ERTSCompositeType::Sequence;
    RetreatSequence->NodeName = TEXT("Retreat");
    
    URTSHealthCondition* LowHealth = NewObject<URTSHealthCondition>();
    LowHealth->HealthThreshold = 0.3f; // 30% health
    LowHealth->bCheckLowHealth = true;
    RetreatSequence->AddChildNode(LowHealth);
    
    // Move away from enemies
    URTSEnhancedMoveTask* RetreatMove = NewObject<URTSEnhancedMoveTask>();
    RetreatMove->TargetLocationKey = TEXT("RetreatLocation");
    RetreatMove->bReturnFireWhileMoving = true;
    RetreatSequence->AddChildNode(RetreatMove);
    
    return RetreatSequence;
}

// URTSAdvancedTargetSelectionTask Implementation

URTSAdvancedTargetSelectionTask::URTSAdvancedTargetSelectionTask()
{
    NodeType = ERTSBehaviorNodeType::Task;
    NodeName = TEXT("Advanced Target Selection");
    NodeDescription = TEXT("Selects optimal targets based on priority system");
    MaxTargetRange = 1000.0f;
    bUseUnitWeaponRange = true;
    HealthPriorityWeight = 2.0f;
    DistancePriorityWeight = 1.0f;
    ThreatPriorityWeight = 3.0f;
    SelectedTargetKey = TEXT("SelectedTarget");
    TargetSwitchCooldown = 3.0f;
    LastTargetSwitchTime = 0.0f;
}

void URTSAdvancedTargetSelectionTask::InitializeNode(URTSBehaviorTreeComponent* BehaviorTreeComponent)
{
    Super::InitializeNode(BehaviorTreeComponent);
    LastTargetSwitchTime = 0.0f;
    CurrentTarget = nullptr;
}

ERTSBehaviorNodeStatus URTSAdvancedTargetSelectionTask::ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime)
{
    if (!BehaviorTreeComponent)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    ARTSUnit* Unit = Cast<ARTSUnit>(GetControlledPawn(BehaviorTreeComponent));
    if (!Unit)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;

    // Check if we should switch targets
    bool bShouldSelectNewTarget = false;
    if (!CurrentTarget.IsValid() || !CurrentTarget->IsAlive())
    {
        bShouldSelectNewTarget = true;
    }
    else if (CurrentTime - LastTargetSwitchTime > TargetSwitchCooldown)
    {
        bShouldSelectNewTarget = true;
    }

    if (bShouldSelectNewTarget)
    {
        UWorld* World = Unit->GetWorld();
        if (!World)
        {
            return ERTSBehaviorNodeStatus::Failure;
        }

        URTSTeamManager* TeamManager = World->GetSubsystem<URTSTeamManager>();
        if (!TeamManager)
        {
            return ERTSBehaviorNodeStatus::Failure;
        }

        // Determine range to use
        float RangeToUse = bUseUnitWeaponRange ? Unit->GetMaxAttackRange() : MaxTargetRange;
        if (RangeToUse <= 0.0f)
        {
            RangeToUse = MaxTargetRange;
        }

        // Find all enemies in range
        TArray<ARTSBaseActor*> EnemiesInRange = TeamManager->FindEnemiesInRange(Unit->TeamID, Unit->GetActorLocation(), RangeToUse);

        if (EnemiesInRange.Num() == 0)
        {
            return ERTSBehaviorNodeStatus::Failure;
        }

        // Find best target based on priority
        ARTSBaseActor* BestTarget = nullptr;
        float BestPriority = -1.0f;

        for (ARTSBaseActor* Enemy : EnemiesInRange)
        {
            if (IsValidTarget(Unit, Enemy))
            {
                float Priority = CalculateTargetPriority(Unit, Enemy);
                if (Priority > BestPriority)
                {
                    BestPriority = Priority;
                    BestTarget = Enemy;
                }
            }
        }

        if (BestTarget)
        {
            CurrentTarget = BestTarget;
            BehaviorTreeComponent->SetBlackboardObject(SelectedTargetKey, BestTarget);
            LastTargetSwitchTime = CurrentTime;
            return ERTSBehaviorNodeStatus::Success;
        }
    }
    else if (CurrentTarget.IsValid())
    {
        // Keep current target
        BehaviorTreeComponent->SetBlackboardObject(SelectedTargetKey, CurrentTarget.Get());
        return ERTSBehaviorNodeStatus::Success;
    }

    return ERTSBehaviorNodeStatus::Failure;
}

float URTSAdvancedTargetSelectionTask::CalculateTargetPriority(ARTSUnit* Unit, ARTSBaseActor* Target)
{
    if (!Unit || !Target)
    {
        return 0.0f;
    }

    float Priority = 1.0f;

    // Health priority (lower health = higher priority)
    if (Target->GetMaxHealth() > 0.0f)
    {
        float HealthRatio = Target->GetCurrentHealth() / Target->GetMaxHealth();
        Priority += (1.0f - HealthRatio) * HealthPriorityWeight;
    }

    // Distance priority (closer = higher priority)
    float Distance = FVector::Dist(Unit->GetActorLocation(), Target->GetActorLocation());
    float MaxRange = bUseUnitWeaponRange ? Unit->GetMaxAttackRange() : MaxTargetRange;
    if (MaxRange > 0.0f)
    {
        float DistanceRatio = 1.0f - (Distance / MaxRange);
        Priority += DistanceRatio * DistancePriorityWeight;
    }

    // Threat priority
    float ThreatLevel = CalculateThreatLevel(Unit, Target);
    Priority += ThreatLevel * ThreatPriorityWeight;

    return Priority;
}

float URTSAdvancedTargetSelectionTask::CalculateThreatLevel(ARTSUnit* Unit, ARTSBaseActor* Target)
{
    if (!Unit || !Target)
    {
        return 0.0f;
    }

    float ThreatLevel = 1.0f;

    // Check if target can attack us
    if (ARTSUnit* TargetUnit = Cast<ARTSUnit>(Target))
    {
        if (TargetUnit->HasWeapons())
        {
            float TargetRange = TargetUnit->GetMaxAttackRange();
            float Distance = FVector::Dist(Unit->GetActorLocation(), Target->GetActorLocation());

            if (Distance <= TargetRange)
            {
                ThreatLevel += 2.0f; // High threat if target can attack us
            }

            // Consider target's damage potential
            ThreatLevel += TargetRange / 1000.0f; // Normalize range
        }
    }

    return ThreatLevel;
}

bool URTSAdvancedTargetSelectionTask::IsValidTarget(ARTSUnit* Unit, ARTSBaseActor* Target)
{
    if (!Unit || !Target)
    {
        return false;
    }

    if (!Target->IsAlive())
    {
        return false;
    }

    // Check if it's an enemy
    UWorld* World = Unit->GetWorld();
    if (World)
    {
        if (URTSTeamManager* TeamManager = World->GetSubsystem<URTSTeamManager>())
        {
            return TeamManager->AreTeamsEnemies(Unit->TeamID, Target->TeamID);
        }
    }

    return false;
}

// URTSTacticalPositioningTask Implementation

URTSTacticalPositioningTask::URTSTacticalPositioningTask()
{
    NodeName = TEXT("Tactical Positioning");
    bSeekCover = false;
    bMaintainFormationPosition = false;
    CoverSearchRadius = 500.0f;
    MinCoverDistance = 100.0f;
}

ERTSBehaviorNodeStatus URTSTacticalPositioningTask::ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime)
{
    if (!BehaviorTreeComponent)
    {
        return ERTSBehaviorNodeStatus::Failed;
    }

    ARTSUnit* Unit = BehaviorTreeComponent->GetOwnerUnit();
    if (!Unit)
    {
        return ERTSBehaviorNodeStatus::Failed;
    }

    // Simple positioning logic - move to a tactical position
    FVector CurrentPosition = Unit->GetActorLocation();
    FVector TacticalPosition = CalculateOptimalPosition(Unit, nullptr);

    if (!TacticalPosition.IsZero())
    {
        Unit->MoveToLocation(TacticalPosition);
        return ERTSBehaviorNodeStatus::Running;
    }

    return ERTSBehaviorNodeStatus::Succeeded;
}

void URTSTacticalPositioningTask::InitializeNode(URTSBehaviorTreeComponent* BehaviorTreeComponent)
{
    Super::InitializeNode(BehaviorTreeComponent);
    // Initialize any task-specific data
}

FVector URTSTacticalPositioningTask::CalculateOptimalPosition(ARTSUnit* Unit, ARTSBaseActor* Target)
{
    if (!Unit)
    {
        return FVector::ZeroVector;
    }

    FVector CurrentPosition = Unit->GetActorLocation();

    if (bSeekCover)
    {
        return FindCoverPosition(Unit, Target);
    }

    // Default: move slightly away from current position
    return CurrentPosition + FVector(100, 0, 0);
}

FVector URTSTacticalPositioningTask::FindCoverPosition(ARTSUnit* Unit, ARTSBaseActor* Target)
{
    if (!Unit)
    {
        return FVector::ZeroVector;
    }

    // Simple cover finding - just move behind nearby objects
    FVector CurrentPosition = Unit->GetActorLocation();

    // For now, just return a position slightly offset from current
    return CurrentPosition + FVector(-200, 0, 0);
}

bool URTSTacticalPositioningTask::IsPositionSafe(ARTSUnit* Unit, const FVector& Position)
{
    if (!Unit)
    {
        return false;
    }

    // Basic safety check - ensure position is not too close to enemies
    // TODO: Implement proper safety checking
    return true;
}

// URTSCoordinatedAttackTask Implementation

URTSCoordinatedAttackTask::URTSCoordinatedAttackTask()
{
    NodeName = TEXT("Coordinated Attack");
    CoordinationRadius = 500.0f;
    MinCoordinatedUnits = 2;
    AttackSynchronizationDelay = 0.5f;
    bWaitForAllies = true;
    TargetActorKey = TEXT("SelectedTarget");
    AttackStartTime = 0.0f;
    bAttackInitiated = false;
}

ERTSBehaviorNodeStatus URTSCoordinatedAttackTask::ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime)
{
    if (!BehaviorTreeComponent)
    {
        return ERTSBehaviorNodeStatus::Failed;
    }

    ARTSUnit* Unit = BehaviorTreeComponent->GetOwnerUnit();
    if (!Unit)
    {
        return ERTSBehaviorNodeStatus::Failed;
    }

    // Find nearby units for coordination
    TArray<ARTSUnit*> NearbyUnits = FindCoordinatedUnits(Unit);

    if (NearbyUnits.Num() < MinCoordinatedUnits)
    {
        return ERTSBehaviorNodeStatus::Failed;
    }

    // Check if allies are ready
    if (bWaitForAllies && !AreAlliesReady(Unit, NearbyUnits))
    {
        return ERTSBehaviorNodeStatus::Running;
    }

    // Initiate coordinated attack
    if (!bAttackInitiated)
    {
        InitiateCoordinatedAttack(Unit, nullptr);
        bAttackInitiated = true;
        AttackStartTime = GetWorld()->GetTimeSeconds();
    }

    return ERTSBehaviorNodeStatus::Succeeded;
}

void URTSCoordinatedAttackTask::InitializeNode(URTSBehaviorTreeComponent* BehaviorTreeComponent)
{
    Super::InitializeNode(BehaviorTreeComponent);
    bAttackInitiated = false;
    AttackStartTime = 0.0f;
}

TArray<ARTSUnit*> URTSCoordinatedAttackTask::FindCoordinatedUnits(ARTSUnit* Unit)
{
    TArray<ARTSUnit*> CoordinatedUnits;

    if (!Unit || !Unit->GetWorld())
    {
        return CoordinatedUnits;
    }

    // Find nearby friendly units
    TArray<AActor*> FoundActors;
    UGameplayStatics::GetAllActorsOfClass(Unit->GetWorld(), ARTSUnit::StaticClass(), FoundActors);

    FVector UnitLocation = Unit->GetActorLocation();

    for (AActor* Actor : FoundActors)
    {
        if (ARTSUnit* OtherUnit = Cast<ARTSUnit>(Actor))
        {
            if (OtherUnit != Unit && OtherUnit->GetTeamID() == Unit->GetTeamID())
            {
                float Distance = FVector::Dist(UnitLocation, OtherUnit->GetActorLocation());
                if (Distance <= CoordinationRadius)
                {
                    CoordinatedUnits.Add(OtherUnit);
                }
            }
        }
    }

    return CoordinatedUnits;
}

bool URTSCoordinatedAttackTask::AreAlliesReady(ARTSUnit* Unit, const TArray<ARTSUnit*>& Allies)
{
    // Simple readiness check - all allies should be alive and not moving
    for (ARTSUnit* Ally : Allies)
    {
        if (!Ally || !Ally->IsAlive() || Ally->IsMoving())
        {
            return false;
        }
    }
    return true;
}

void URTSCoordinatedAttackTask::InitiateCoordinatedAttack(ARTSUnit* Unit, ARTSBaseActor* Target)
{
    if (!Unit)
    {
        return;
    }

    // Simple coordinated attack - just start attacking
    if (Target)
    {
        Unit->AttackTarget(Target);
    }
}

// URTSReturnFireTask Implementation

URTSReturnFireTask::URTSReturnFireTask()
{
    NodeName = TEXT("Return Fire");
    bPrioritizeAttackers = true;
    ReturnFireRange = 800.0f;
    AttackerKey = TEXT("NearestAttacker");
}

ERTSBehaviorNodeStatus URTSReturnFireTask::ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime)
{
    if (!BehaviorTreeComponent)
    {
        return ERTSBehaviorNodeStatus::Failed;
    }

    ARTSUnit* Unit = BehaviorTreeComponent->GetOwnerUnit();
    if (!Unit)
    {
        return ERTSBehaviorNodeStatus::Failed;
    }

    // Check if unit is being attacked
    if (!IsBeingAttacked(Unit))
    {
        return ERTSBehaviorNodeStatus::Failed;
    }

    // Find nearest attacker
    ARTSBaseActor* Attacker = FindNearestAttacker(Unit);
    if (!Attacker)
    {
        return ERTSBehaviorNodeStatus::Failed;
    }

    // Start return fire
    StartReturnFire(Unit, Attacker);
    return ERTSBehaviorNodeStatus::Succeeded;
}

void URTSReturnFireTask::InitializeNode(URTSBehaviorTreeComponent* BehaviorTreeComponent)
{
    Super::InitializeNode(BehaviorTreeComponent);
}

ARTSBaseActor* URTSReturnFireTask::FindNearestAttacker(ARTSUnit* Unit)
{
    if (!Unit || !Unit->GetWorld())
    {
        return nullptr;
    }

    // Find nearest enemy unit
    TArray<AActor*> FoundActors;
    UGameplayStatics::GetAllActorsOfClass(Unit->GetWorld(), ARTSUnit::StaticClass(), FoundActors);

    ARTSBaseActor* NearestAttacker = nullptr;
    float NearestDistance = ReturnFireRange;
    FVector UnitLocation = Unit->GetActorLocation();

    for (AActor* Actor : FoundActors)
    {
        if (ARTSUnit* OtherUnit = Cast<ARTSUnit>(Actor))
        {
            if (OtherUnit != Unit && OtherUnit->GetTeamID() != Unit->GetTeamID())
            {
                float Distance = FVector::Dist(UnitLocation, OtherUnit->GetActorLocation());
                if (Distance < NearestDistance)
                {
                    NearestDistance = Distance;
                    NearestAttacker = OtherUnit;
                }
            }
        }
    }

    return NearestAttacker;
}

bool URTSReturnFireTask::IsBeingAttacked(ARTSUnit* Unit)
{
    if (!Unit)
    {
        return false;
    }

    // Simple check - assume unit is being attacked if health is below max
    return Unit->GetHealthPercentage() < 1.0f;
}

void URTSReturnFireTask::StartReturnFire(ARTSUnit* Unit, ARTSBaseActor* Target)
{
    if (Unit && Target)
    {
        Unit->AttackTarget(Target);
    }
}

// URTSUnderAttackCondition Implementation

URTSUnderAttackCondition::URTSUnderAttackCondition()
{
    NodeName = TEXT("Under Attack Check");
    AttackDetectionRadius = 600.0f;
    RecentDamageThreshold = 2.0f;
}

ERTSBehaviorNodeStatus URTSUnderAttackCondition::ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime)
{
    if (!BehaviorTreeComponent)
    {
        return ERTSBehaviorNodeStatus::Failed;
    }

    ARTSUnit* Unit = BehaviorTreeComponent->GetOwnerUnit();
    if (!Unit)
    {
        return ERTSBehaviorNodeStatus::Failed;
    }

    // Check if unit is under attack
    bool bUnderAttack = Unit->GetHealthPercentage() < 1.0f; // Simple check

    return bUnderAttack ? ERTSBehaviorNodeStatus::Succeeded : ERTSBehaviorNodeStatus::Failed;
}

// URTSTacticalAdvantageCondition Implementation

URTSTacticalAdvantageCondition::URTSTacticalAdvantageCondition()
{
    NodeName = TEXT("Tactical Advantage Check");
    AdvantageRadius = 800.0f;
    MinAdvantageRatio = 1.5f;
}

ERTSBehaviorNodeStatus URTSTacticalAdvantageCondition::ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime)
{
    if (!BehaviorTreeComponent)
    {
        return ERTSBehaviorNodeStatus::Failed;
    }

    ARTSUnit* Unit = BehaviorTreeComponent->GetOwnerUnit();
    if (!Unit)
    {
        return ERTSBehaviorNodeStatus::Failed;
    }

    float TacticalAdvantage = CalculateTacticalAdvantage(Unit);

    return TacticalAdvantage >= MinAdvantageRatio ? ERTSBehaviorNodeStatus::Succeeded : ERTSBehaviorNodeStatus::Failed;
}

float URTSTacticalAdvantageCondition::CalculateTacticalAdvantage(ARTSUnit* Unit)
{
    if (!Unit)
    {
        return 0.0f;
    }

    int32 NearbyAllies = CountNearbyAllies(Unit);
    int32 NearbyEnemies = CountNearbyEnemies(Unit);

    if (NearbyEnemies == 0)
    {
        return 10.0f; // High advantage if no enemies
    }

    float AllyHealth = CalculateAverageAllyHealth(Unit);
    float EnemyHealth = CalculateAverageEnemyHealth(Unit);

    float NumberAdvantage = static_cast<float>(NearbyAllies + 1) / static_cast<float>(NearbyEnemies);
    float HealthAdvantage = AllyHealth / FMath::Max(EnemyHealth, 0.1f);

    return (NumberAdvantage + HealthAdvantage) / 2.0f;
}

int32 URTSTacticalAdvantageCondition::CountNearbyAllies(ARTSUnit* Unit)
{
    if (!Unit || !Unit->GetWorld())
    {
        return 0;
    }

    int32 AllyCount = 0;
    TArray<AActor*> FoundActors;
    UGameplayStatics::GetAllActorsOfClass(Unit->GetWorld(), ARTSUnit::StaticClass(), FoundActors);

    FVector UnitLocation = Unit->GetActorLocation();

    for (AActor* Actor : FoundActors)
    {
        if (ARTSUnit* OtherUnit = Cast<ARTSUnit>(Actor))
        {
            if (OtherUnit != Unit && OtherUnit->GetTeamID() == Unit->GetTeamID())
            {
                float Distance = FVector::Dist(UnitLocation, OtherUnit->GetActorLocation());
                if (Distance <= AdvantageRadius)
                {
                    AllyCount++;
                }
            }
        }
    }

    return AllyCount;
}

int32 URTSTacticalAdvantageCondition::CountNearbyEnemies(ARTSUnit* Unit)
{
    if (!Unit || !Unit->GetWorld())
    {
        return 0;
    }

    int32 EnemyCount = 0;
    TArray<AActor*> FoundActors;
    UGameplayStatics::GetAllActorsOfClass(Unit->GetWorld(), ARTSUnit::StaticClass(), FoundActors);

    FVector UnitLocation = Unit->GetActorLocation();

    for (AActor* Actor : FoundActors)
    {
        if (ARTSUnit* OtherUnit = Cast<ARTSUnit>(Actor))
        {
            if (OtherUnit->GetTeamID() != Unit->GetTeamID())
            {
                float Distance = FVector::Dist(UnitLocation, OtherUnit->GetActorLocation());
                if (Distance <= AdvantageRadius)
                {
                    EnemyCount++;
                }
            }
        }
    }

    return EnemyCount;
}

float URTSTacticalAdvantageCondition::CalculateAverageAllyHealth(ARTSUnit* Unit)
{
    if (!Unit || !Unit->GetWorld())
    {
        return 0.0f;
    }

    float TotalHealth = Unit->GetHealthPercentage();
    int32 UnitCount = 1;

    TArray<AActor*> FoundActors;
    UGameplayStatics::GetAllActorsOfClass(Unit->GetWorld(), ARTSUnit::StaticClass(), FoundActors);

    FVector UnitLocation = Unit->GetActorLocation();

    for (AActor* Actor : FoundActors)
    {
        if (ARTSUnit* OtherUnit = Cast<ARTSUnit>(Actor))
        {
            if (OtherUnit != Unit && OtherUnit->GetTeamID() == Unit->GetTeamID())
            {
                float Distance = FVector::Dist(UnitLocation, OtherUnit->GetActorLocation());
                if (Distance <= AdvantageRadius)
                {
                    TotalHealth += OtherUnit->GetHealthPercentage();
                    UnitCount++;
                }
            }
        }
    }

    return UnitCount > 0 ? TotalHealth / UnitCount : 0.0f;
}

float URTSTacticalAdvantageCondition::CalculateAverageEnemyHealth(ARTSUnit* Unit)
{
    if (!Unit || !Unit->GetWorld())
    {
        return 1.0f; // Assume full health if no data
    }

    float TotalHealth = 0.0f;
    int32 UnitCount = 0;

    TArray<AActor*> FoundActors;
    UGameplayStatics::GetAllActorsOfClass(Unit->GetWorld(), ARTSUnit::StaticClass(), FoundActors);

    FVector UnitLocation = Unit->GetActorLocation();

    for (AActor* Actor : FoundActors)
    {
        if (ARTSUnit* OtherUnit = Cast<ARTSUnit>(Actor))
        {
            if (OtherUnit->GetTeamID() != Unit->GetTeamID())
            {
                float Distance = FVector::Dist(UnitLocation, OtherUnit->GetActorLocation());
                if (Distance <= AdvantageRadius)
                {
                    TotalHealth += OtherUnit->GetHealthPercentage();
                    UnitCount++;
                }
            }
        }
    }

    return UnitCount > 0 ? TotalHealth / UnitCount : 1.0f;
}
