{"FileVersion": 3, "EngineAssociation": "5.6", "Category": "", "Description": "", "Modules": [{"Name": "ArmorWars", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "AdditionalDependencies": ["Engine"]}], "Plugins": [{"Name": "ModelingToolsEditorMode", "Enabled": true, "TargetAllowList": ["Editor"]}, {"Name": "MassAI", "Enabled": false}, {"Name": "MassCrowd", "Enabled": false}, {"Name": "MassGameplay", "Enabled": false}, {"Name": "DataRegistry", "Enabled": false}, {"Name": "GameFeatures", "Enabled": false}, {"Name": "AbilitySystemGameFeatureActions", "Enabled": false}, {"Name": "InstancedActors", "Enabled": true}, {"Name": "PCGInstancedActorsInterop", "Enabled": false}, {"Name": "LightWeightInstancesEditor", "Enabled": true}, {"Name": "StructUtils", "Enabled": true}, {"Name": "PCGNiagaraInterop", "Enabled": false}, {"Name": "AnimatorKit", "Enabled": true}, {"Name": "PCGExternalDataInterop", "Enabled": true}, {"Name": "PCG", "Enabled": true}, {"Name": "SampleToolsEditorMode", "Enabled": true}, {"Name": "ScriptableToolsEditorMode", "Enabled": true}, {"Name": "StaticMeshEditorModeling", "Enabled": true}, {"Name": "TextureGraph", "Enabled": true, "SupportedTargetPlatforms": ["Win64"]}, {"Name": "Landmass", "Enabled": true}, {"Name": "PerformanceMonitor", "Enabled": true}, {"Name": "ReplicationGraph", "Enabled": true}, {"Name": "PythonFoundationPackages", "Enabled": false}, {"Name": "GameplayBehaviors", "Enabled": false}, {"Name": "GameplayAbilities", "Enabled": false}, {"Name": "GameplayStateTree", "Enabled": true}, {"Name": "TargetingSystem", "Enabled": true}]}