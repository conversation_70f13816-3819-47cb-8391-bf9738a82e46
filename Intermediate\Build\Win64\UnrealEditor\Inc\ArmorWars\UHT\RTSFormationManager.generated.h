// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "RTSFormationManager.h"

#ifdef ARMORWARS_RTSFormationManager_generated_h
#error "RTSFormationManager.generated.h already included, missing '#pragma once' in RTSFormationManager.h"
#endif
#define ARMORWARS_RTSFormationManager_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class ARTSUnit;
enum class ERTSCommandPriority : uint8;
enum class ERTSFormationType : uint8;
struct FRTSFormationGroup;

// ********** Begin ScriptStruct FRTSFormationGroup ************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSFormationManager_h_16_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FRTSFormationGroup_Statics; \
	static class UScriptStruct* StaticStruct();


struct FRTSFormationGroup;
// ********** End ScriptStruct FRTSFormationGroup **************************************************

// ********** Begin Delegate FOnFormationCreated ***************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSFormationManager_h_96_DELEGATE \
ARMORWARS_API void FOnFormationCreated_DelegateWrapper(const FMulticastScriptDelegate& OnFormationCreated, int32 GroupID, FRTSFormationGroup const& FormationGroup);


// ********** End Delegate FOnFormationCreated *****************************************************

// ********** Begin Delegate FOnFormationDisbanded *************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSFormationManager_h_97_DELEGATE \
ARMORWARS_API void FOnFormationDisbanded_DelegateWrapper(const FMulticastScriptDelegate& OnFormationDisbanded, int32 GroupID);


// ********** End Delegate FOnFormationDisbanded ***************************************************

// ********** Begin Delegate FOnFormationLeaderChanged *********************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSFormationManager_h_98_DELEGATE \
ARMORWARS_API void FOnFormationLeaderChanged_DelegateWrapper(const FMulticastScriptDelegate& OnFormationLeaderChanged, int32 GroupID, ARTSUnit* NewLeader);


// ********** End Delegate FOnFormationLeaderChanged ***********************************************

// ********** Begin Class URTSFormationManager *****************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSFormationManager_h_107_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execCleanupInvalidFormations); \
	DECLARE_FUNCTION(execUpdateFormation); \
	DECLARE_FUNCTION(execUpdateAllFormations); \
	DECLARE_FUNCTION(execChangeFormationLeader); \
	DECLARE_FUNCTION(execElectSquadLeader); \
	DECLARE_FUNCTION(execCalculateUnitFormationOffset); \
	DECLARE_FUNCTION(execCalculateFormationPositions); \
	DECLARE_FUNCTION(execSetFormationSpacing); \
	DECLARE_FUNCTION(execSetFormationType); \
	DECLARE_FUNCTION(execMoveFormation); \
	DECLARE_FUNCTION(execGetAllFormationIDs); \
	DECLARE_FUNCTION(execGetFormationUnits); \
	DECLARE_FUNCTION(execGetFormationLeader); \
	DECLARE_FUNCTION(execGetFormationForUnit); \
	DECLARE_FUNCTION(execGetFormationGroup); \
	DECLARE_FUNCTION(execDoesFormationExist); \
	DECLARE_FUNCTION(execRemoveUnitFromFormation); \
	DECLARE_FUNCTION(execAddUnitToFormation); \
	DECLARE_FUNCTION(execDisbandFormation); \
	DECLARE_FUNCTION(execCreateFormation);


ARMORWARS_API UClass* Z_Construct_UClass_URTSFormationManager_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSFormationManager_h_107_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSFormationManager(); \
	friend struct Z_Construct_UClass_URTSFormationManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSFormationManager_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSFormationManager, UWorldSubsystem, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSFormationManager_NoRegister) \
	DECLARE_SERIALIZER(URTSFormationManager)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSFormationManager_h_107_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSFormationManager(URTSFormationManager&&) = delete; \
	URTSFormationManager(const URTSFormationManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSFormationManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSFormationManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSFormationManager) \
	NO_API virtual ~URTSFormationManager();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSFormationManager_h_104_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSFormationManager_h_107_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSFormationManager_h_107_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSFormationManager_h_107_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSFormationManager_h_107_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSFormationManager;

// ********** End Class URTSFormationManager *******************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_ArmorWars_Source_ArmorWars_Public_RTSFormationManager_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
