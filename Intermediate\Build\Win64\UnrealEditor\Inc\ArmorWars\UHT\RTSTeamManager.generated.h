// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "RTSTeamManager.h"

#ifdef ARMORWARS_RTSTeamManager_generated_h
#error "RTSTeamManager.generated.h already included, missing '#pragma once' in RTSTeamManager.h"
#endif
#define ARMORWARS_RTSTeamManager_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class ARTSBaseActor;
struct FRTSTeamInfo;

// ********** Begin ScriptStruct FRTSTeamInfo ******************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSTeamManager_h_12_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FRTSTeamInfo_Statics; \
	static class UScriptStruct* StaticStruct();


struct FRTSTeamInfo;
// ********** End ScriptStruct FRTSTeamInfo ********************************************************

// ********** Begin Delegate FOnTeamCreated ********************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSTeamManager_h_146_DELEGATE \
static void FOnTeamCreated_DelegateWrapper(const FMulticastScriptDelegate& OnTeamCreated, int32 TeamID, FRTSTeamInfo const& TeamInfo);


// ********** End Delegate FOnTeamCreated **********************************************************

// ********** Begin Delegate FOnTeamRemoved ********************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSTeamManager_h_147_DELEGATE \
static void FOnTeamRemoved_DelegateWrapper(const FMulticastScriptDelegate& OnTeamRemoved, int32 TeamID);


// ********** End Delegate FOnTeamRemoved **********************************************************

// ********** Begin Class URTSTeamManager **********************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSTeamManager_h_54_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execClearAllTeams); \
	DECLARE_FUNCTION(execInitializeDefaultTeams); \
	DECLARE_FUNCTION(execGetActiveTeamCount); \
	DECLARE_FUNCTION(execIsTeamActive); \
	DECLARE_FUNCTION(execSetTeamActive); \
	DECLARE_FUNCTION(execCalculateTargetPriority); \
	DECLARE_FUNCTION(execIsValidTarget); \
	DECLARE_FUNCTION(execFindHighestPriorityTarget); \
	DECLARE_FUNCTION(execFindNearestEnemy); \
	DECLARE_FUNCTION(execFindEnemiesInRange); \
	DECLARE_FUNCTION(execGetAllEnemyActors); \
	DECLARE_FUNCTION(execGetAllActorsOnTeam); \
	DECLARE_FUNCTION(execAreActorsEnemies); \
	DECLARE_FUNCTION(execAreActorsAllied); \
	DECLARE_FUNCTION(execAreTeamsEnemies); \
	DECLARE_FUNCTION(execAreTeamsAllied); \
	DECLARE_FUNCTION(execGetAllTeams); \
	DECLARE_FUNCTION(execGetAllTeamIDs); \
	DECLARE_FUNCTION(execSetTeamInfo); \
	DECLARE_FUNCTION(execGetTeamInfo); \
	DECLARE_FUNCTION(execDoesTeamExist); \
	DECLARE_FUNCTION(execRemoveTeam); \
	DECLARE_FUNCTION(execCreateTeam);


ARMORWARS_API UClass* Z_Construct_UClass_URTSTeamManager_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSTeamManager_h_54_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSTeamManager(); \
	friend struct Z_Construct_UClass_URTSTeamManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSTeamManager_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSTeamManager, UWorldSubsystem, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSTeamManager_NoRegister) \
	DECLARE_SERIALIZER(URTSTeamManager)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSTeamManager_h_54_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSTeamManager(URTSTeamManager&&) = delete; \
	URTSTeamManager(const URTSTeamManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSTeamManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSTeamManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSTeamManager) \
	NO_API virtual ~URTSTeamManager();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSTeamManager_h_51_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSTeamManager_h_54_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSTeamManager_h_54_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSTeamManager_h_54_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSTeamManager_h_54_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSTeamManager;

// ********** End Class URTSTeamManager ************************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_ArmorWars_Source_ArmorWars_Public_RTSTeamManager_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
