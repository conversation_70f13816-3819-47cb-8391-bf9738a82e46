// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RTSAITestScenario.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeRTSAITestScenario() {}

// ********** Begin Cross Module References ********************************************************
ARMORWARS_API UClass* Z_Construct_UClass_ARTSAITestScenario();
ARMORWARS_API UClass* Z_Construct_UClass_ARTSAITestScenario_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_ARTSGroupManager_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_ARTSUnit_NoRegister();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSTestScenarioType();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestCompleted__DelegateSignature();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestPhaseChanged__DelegateSignature();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestStarted__DelegateSignature();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
UPackage* Z_Construct_UPackage__Script_ArmorWars();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum ERTSTestScenarioType ******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERTSTestScenarioType;
static UEnum* ERTSTestScenarioType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERTSTestScenarioType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERTSTestScenarioType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_ArmorWars_ERTSTestScenarioType, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("ERTSTestScenarioType"));
	}
	return Z_Registration_Info_UEnum_ERTSTestScenarioType.OuterSingleton;
}
template<> ARMORWARS_API UEnum* StaticEnum<ERTSTestScenarioType>()
{
	return ERTSTestScenarioType_StaticEnum();
}
struct Z_Construct_UEnum_ArmorWars_ERTSTestScenarioType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BehaviorTreeTest.DisplayName", "Behavior Tree Test" },
		{ "BehaviorTreeTest.Name", "ERTSTestScenarioType::BehaviorTreeTest" },
		{ "BlueprintType", "true" },
		{ "CombatTest.DisplayName", "Combat Test" },
		{ "CombatTest.Name", "ERTSTestScenarioType::CombatTest" },
		{ "FormationTest.DisplayName", "Formation Test" },
		{ "FormationTest.Name", "ERTSTestScenarioType::FormationTest" },
		{ "GroupCoordination.DisplayName", "Group Coordination" },
		{ "GroupCoordination.Name", "ERTSTestScenarioType::GroupCoordination" },
		{ "ModuleRelativePath", "Public/RTSAITestScenario.h" },
		{ "MovementTest.DisplayName", "Movement Test" },
		{ "MovementTest.Name", "ERTSTestScenarioType::MovementTest" },
		{ "ObstacleAvoidance.DisplayName", "Obstacle Avoidance" },
		{ "ObstacleAvoidance.Name", "ERTSTestScenarioType::ObstacleAvoidance" },
		{ "PathfindingTest.DisplayName", "Pathfinding Test" },
		{ "PathfindingTest.Name", "ERTSTestScenarioType::PathfindingTest" },
		{ "StressTest.DisplayName", "Stress Test" },
		{ "StressTest.Name", "ERTSTestScenarioType::StressTest" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERTSTestScenarioType::MovementTest", (int64)ERTSTestScenarioType::MovementTest },
		{ "ERTSTestScenarioType::CombatTest", (int64)ERTSTestScenarioType::CombatTest },
		{ "ERTSTestScenarioType::FormationTest", (int64)ERTSTestScenarioType::FormationTest },
		{ "ERTSTestScenarioType::BehaviorTreeTest", (int64)ERTSTestScenarioType::BehaviorTreeTest },
		{ "ERTSTestScenarioType::GroupCoordination", (int64)ERTSTestScenarioType::GroupCoordination },
		{ "ERTSTestScenarioType::PathfindingTest", (int64)ERTSTestScenarioType::PathfindingTest },
		{ "ERTSTestScenarioType::ObstacleAvoidance", (int64)ERTSTestScenarioType::ObstacleAvoidance },
		{ "ERTSTestScenarioType::StressTest", (int64)ERTSTestScenarioType::StressTest },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_ArmorWars_ERTSTestScenarioType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	"ERTSTestScenarioType",
	"ERTSTestScenarioType",
	Z_Construct_UEnum_ArmorWars_ERTSTestScenarioType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSTestScenarioType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSTestScenarioType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_ArmorWars_ERTSTestScenarioType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_ArmorWars_ERTSTestScenarioType()
{
	if (!Z_Registration_Info_UEnum_ERTSTestScenarioType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERTSTestScenarioType.InnerSingleton, Z_Construct_UEnum_ArmorWars_ERTSTestScenarioType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERTSTestScenarioType.InnerSingleton;
}
// ********** End Enum ERTSTestScenarioType ********************************************************

// ********** Begin Delegate FOnTestStarted ********************************************************
struct Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestStarted__DelegateSignature_Statics
{
	struct RTSAITestScenario_eventOnTestStarted_Parms
	{
		ERTSTestScenarioType TestType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAITestScenario.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_TestType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TestType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestStarted__DelegateSignature_Statics::NewProp_TestType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestStarted__DelegateSignature_Statics::NewProp_TestType = { "TestType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAITestScenario_eventOnTestStarted_Parms, TestType), Z_Construct_UEnum_ArmorWars_ERTSTestScenarioType, METADATA_PARAMS(0, nullptr) }; // 406864
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestStarted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestStarted__DelegateSignature_Statics::NewProp_TestType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestStarted__DelegateSignature_Statics::NewProp_TestType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestStarted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestStarted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAITestScenario, nullptr, "OnTestStarted__DelegateSignature", Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestStarted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestStarted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestStarted__DelegateSignature_Statics::RTSAITestScenario_eventOnTestStarted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestStarted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestStarted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestStarted__DelegateSignature_Statics::RTSAITestScenario_eventOnTestStarted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestStarted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestStarted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void ARTSAITestScenario::FOnTestStarted_DelegateWrapper(const FMulticastScriptDelegate& OnTestStarted, ERTSTestScenarioType TestType)
{
	struct RTSAITestScenario_eventOnTestStarted_Parms
	{
		ERTSTestScenarioType TestType;
	};
	RTSAITestScenario_eventOnTestStarted_Parms Parms;
	Parms.TestType=TestType;
	OnTestStarted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnTestStarted **********************************************************

// ********** Begin Delegate FOnTestCompleted ******************************************************
struct Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestCompleted__DelegateSignature_Statics
{
	struct RTSAITestScenario_eventOnTestCompleted_Parms
	{
		ERTSTestScenarioType TestType;
		bool bPassed;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/RTSAITestScenario.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_TestType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TestType;
	static void NewProp_bPassed_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPassed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestCompleted__DelegateSignature_Statics::NewProp_TestType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestCompleted__DelegateSignature_Statics::NewProp_TestType = { "TestType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAITestScenario_eventOnTestCompleted_Parms, TestType), Z_Construct_UEnum_ArmorWars_ERTSTestScenarioType, METADATA_PARAMS(0, nullptr) }; // 406864
void Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestCompleted__DelegateSignature_Statics::NewProp_bPassed_SetBit(void* Obj)
{
	((RTSAITestScenario_eventOnTestCompleted_Parms*)Obj)->bPassed = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestCompleted__DelegateSignature_Statics::NewProp_bPassed = { "bPassed", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAITestScenario_eventOnTestCompleted_Parms), &Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestCompleted__DelegateSignature_Statics::NewProp_bPassed_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestCompleted__DelegateSignature_Statics::NewProp_TestType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestCompleted__DelegateSignature_Statics::NewProp_TestType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestCompleted__DelegateSignature_Statics::NewProp_bPassed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestCompleted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAITestScenario, nullptr, "OnTestCompleted__DelegateSignature", Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestCompleted__DelegateSignature_Statics::RTSAITestScenario_eventOnTestCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestCompleted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestCompleted__DelegateSignature_Statics::RTSAITestScenario_eventOnTestCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void ARTSAITestScenario::FOnTestCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnTestCompleted, ERTSTestScenarioType TestType, bool bPassed)
{
	struct RTSAITestScenario_eventOnTestCompleted_Parms
	{
		ERTSTestScenarioType TestType;
		bool bPassed;
	};
	RTSAITestScenario_eventOnTestCompleted_Parms Parms;
	Parms.TestType=TestType;
	Parms.bPassed=bPassed ? true : false;
	OnTestCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnTestCompleted ********************************************************

// ********** Begin Delegate FOnTestPhaseChanged ***************************************************
struct Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestPhaseChanged__DelegateSignature_Statics
{
	struct RTSAITestScenario_eventOnTestPhaseChanged_Parms
	{
		int32 CurrentPhase;
		int32 MaxPhases;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/RTSAITestScenario.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentPhase;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxPhases;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestPhaseChanged__DelegateSignature_Statics::NewProp_CurrentPhase = { "CurrentPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAITestScenario_eventOnTestPhaseChanged_Parms, CurrentPhase), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestPhaseChanged__DelegateSignature_Statics::NewProp_MaxPhases = { "MaxPhases", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAITestScenario_eventOnTestPhaseChanged_Parms, MaxPhases), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestPhaseChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestPhaseChanged__DelegateSignature_Statics::NewProp_CurrentPhase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestPhaseChanged__DelegateSignature_Statics::NewProp_MaxPhases,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestPhaseChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestPhaseChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAITestScenario, nullptr, "OnTestPhaseChanged__DelegateSignature", Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestPhaseChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestPhaseChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestPhaseChanged__DelegateSignature_Statics::RTSAITestScenario_eventOnTestPhaseChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestPhaseChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestPhaseChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestPhaseChanged__DelegateSignature_Statics::RTSAITestScenario_eventOnTestPhaseChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestPhaseChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestPhaseChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void ARTSAITestScenario::FOnTestPhaseChanged_DelegateWrapper(const FMulticastScriptDelegate& OnTestPhaseChanged, int32 CurrentPhase, int32 MaxPhases)
{
	struct RTSAITestScenario_eventOnTestPhaseChanged_Parms
	{
		int32 CurrentPhase;
		int32 MaxPhases;
	};
	RTSAITestScenario_eventOnTestPhaseChanged_Parms Parms;
	Parms.CurrentPhase=CurrentPhase;
	Parms.MaxPhases=MaxPhases;
	OnTestPhaseChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnTestPhaseChanged *****************************************************

// ********** Begin Class ARTSAITestScenario Function GetTestProgress ******************************
struct Z_Construct_UFunction_ARTSAITestScenario_GetTestProgress_Statics
{
	struct RTSAITestScenario_eventGetTestProgress_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Test" },
		{ "ModuleRelativePath", "Public/RTSAITestScenario.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSAITestScenario_GetTestProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAITestScenario_eventGetTestProgress_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAITestScenario_GetTestProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAITestScenario_GetTestProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAITestScenario_GetTestProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAITestScenario_GetTestProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAITestScenario, nullptr, "GetTestProgress", Z_Construct_UFunction_ARTSAITestScenario_GetTestProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAITestScenario_GetTestProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAITestScenario_GetTestProgress_Statics::RTSAITestScenario_eventGetTestProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAITestScenario_GetTestProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAITestScenario_GetTestProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAITestScenario_GetTestProgress_Statics::RTSAITestScenario_eventGetTestProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAITestScenario_GetTestProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAITestScenario_GetTestProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAITestScenario::execGetTestProgress)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetTestProgress();
	P_NATIVE_END;
}
// ********** End Class ARTSAITestScenario Function GetTestProgress ********************************

// ********** Begin Class ARTSAITestScenario Function IsTestRunning ********************************
struct Z_Construct_UFunction_ARTSAITestScenario_IsTestRunning_Statics
{
	struct RTSAITestScenario_eventIsTestRunning_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Test" },
		{ "ModuleRelativePath", "Public/RTSAITestScenario.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSAITestScenario_IsTestRunning_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSAITestScenario_eventIsTestRunning_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSAITestScenario_IsTestRunning_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAITestScenario_eventIsTestRunning_Parms), &Z_Construct_UFunction_ARTSAITestScenario_IsTestRunning_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAITestScenario_IsTestRunning_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAITestScenario_IsTestRunning_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAITestScenario_IsTestRunning_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAITestScenario_IsTestRunning_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAITestScenario, nullptr, "IsTestRunning", Z_Construct_UFunction_ARTSAITestScenario_IsTestRunning_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAITestScenario_IsTestRunning_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAITestScenario_IsTestRunning_Statics::RTSAITestScenario_eventIsTestRunning_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAITestScenario_IsTestRunning_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAITestScenario_IsTestRunning_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAITestScenario_IsTestRunning_Statics::RTSAITestScenario_eventIsTestRunning_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAITestScenario_IsTestRunning()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAITestScenario_IsTestRunning_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAITestScenario::execIsTestRunning)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsTestRunning();
	P_NATIVE_END;
}
// ********** End Class ARTSAITestScenario Function IsTestRunning **********************************

// ********** Begin Class ARTSAITestScenario Function LogTestResults *******************************
struct Z_Construct_UFunction_ARTSAITestScenario_LogTestResults_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Test" },
		{ "ModuleRelativePath", "Public/RTSAITestScenario.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAITestScenario_LogTestResults_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAITestScenario, nullptr, "LogTestResults", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAITestScenario_LogTestResults_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAITestScenario_LogTestResults_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSAITestScenario_LogTestResults()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAITestScenario_LogTestResults_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAITestScenario::execLogTestResults)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogTestResults();
	P_NATIVE_END;
}
// ********** End Class ARTSAITestScenario Function LogTestResults *********************************

// ********** Begin Class ARTSAITestScenario Function ResetTest ************************************
struct Z_Construct_UFunction_ARTSAITestScenario_ResetTest_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Test" },
		{ "ModuleRelativePath", "Public/RTSAITestScenario.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAITestScenario_ResetTest_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAITestScenario, nullptr, "ResetTest", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAITestScenario_ResetTest_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAITestScenario_ResetTest_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSAITestScenario_ResetTest()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAITestScenario_ResetTest_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAITestScenario::execResetTest)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetTest();
	P_NATIVE_END;
}
// ********** End Class ARTSAITestScenario Function ResetTest **************************************

// ********** Begin Class ARTSAITestScenario Function StartTest ************************************
struct Z_Construct_UFunction_ARTSAITestScenario_StartTest_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Test" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Test Management\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAITestScenario.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test Management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAITestScenario_StartTest_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAITestScenario, nullptr, "StartTest", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAITestScenario_StartTest_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAITestScenario_StartTest_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSAITestScenario_StartTest()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAITestScenario_StartTest_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAITestScenario::execStartTest)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartTest();
	P_NATIVE_END;
}
// ********** End Class ARTSAITestScenario Function StartTest **************************************

// ********** Begin Class ARTSAITestScenario Function StopTest *************************************
struct Z_Construct_UFunction_ARTSAITestScenario_StopTest_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Test" },
		{ "ModuleRelativePath", "Public/RTSAITestScenario.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAITestScenario_StopTest_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAITestScenario, nullptr, "StopTest", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAITestScenario_StopTest_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAITestScenario_StopTest_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSAITestScenario_StopTest()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAITestScenario_StopTest_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAITestScenario::execStopTest)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopTest();
	P_NATIVE_END;
}
// ********** End Class ARTSAITestScenario Function StopTest ***************************************

// ********** Begin Class ARTSAITestScenario *******************************************************
void ARTSAITestScenario::StaticRegisterNativesARTSAITestScenario()
{
	UClass* Class = ARTSAITestScenario::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "GetTestProgress", &ARTSAITestScenario::execGetTestProgress },
		{ "IsTestRunning", &ARTSAITestScenario::execIsTestRunning },
		{ "LogTestResults", &ARTSAITestScenario::execLogTestResults },
		{ "ResetTest", &ARTSAITestScenario::execResetTest },
		{ "StartTest", &ARTSAITestScenario::execStartTest },
		{ "StopTest", &ARTSAITestScenario::execStopTest },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_ARTSAITestScenario;
UClass* ARTSAITestScenario::GetPrivateStaticClass()
{
	using TClass = ARTSAITestScenario;
	if (!Z_Registration_Info_UClass_ARTSAITestScenario.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSAITestScenario"),
			Z_Registration_Info_UClass_ARTSAITestScenario.InnerSingleton,
			StaticRegisterNativesARTSAITestScenario,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_ARTSAITestScenario.InnerSingleton;
}
UClass* Z_Construct_UClass_ARTSAITestScenario_NoRegister()
{
	return ARTSAITestScenario::GetPrivateStaticClass();
}
struct Z_Construct_UClass_ARTSAITestScenario_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Test scenario class for verifying RTS AI functionality\n * Creates controlled test environments to validate AI behaviors\n */" },
#endif
		{ "IncludePath", "RTSAITestScenario.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSAITestScenario.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test scenario class for verifying RTS AI functionality\nCreates controlled test environments to validate AI behaviors" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestType_MetaData[] = {
		{ "Category", "Test Setup" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Test Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAITestScenario.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NumberOfUnits_MetaData[] = {
		{ "Category", "Test Setup" },
		{ "ModuleRelativePath", "Public/RTSAITestScenario.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestDuration_MetaData[] = {
		{ "Category", "Test Setup" },
		{ "ModuleRelativePath", "Public/RTSAITestScenario.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoStartTest_MetaData[] = {
		{ "Category", "Test Setup" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Test duration in seconds\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAITestScenario.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test duration in seconds" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDetailedLogging_MetaData[] = {
		{ "Category", "Test Setup" },
		{ "ModuleRelativePath", "Public/RTSAITestScenario.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bTestPassed_MetaData[] = {
		{ "Category", "Test Results" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Test Results\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAITestScenario.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test Results" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestResults_MetaData[] = {
		{ "Category", "Test Results" },
		{ "ModuleRelativePath", "Public/RTSAITestScenario.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestLog_MetaData[] = {
		{ "Category", "Test Results" },
		{ "ModuleRelativePath", "Public/RTSAITestScenario.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestUnits_MetaData[] = {
		{ "Category", "Test Units" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Test objects\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAITestScenario.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test objects" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestEnemies_MetaData[] = {
		{ "Category", "Test Units" },
		{ "ModuleRelativePath", "Public/RTSAITestScenario.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestObstacles_MetaData[] = {
		{ "Category", "Test Environment" },
		{ "ModuleRelativePath", "Public/RTSAITestScenario.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestGroupManager_MetaData[] = {
		{ "ModuleRelativePath", "Public/RTSAITestScenario.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnTestStarted_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/RTSAITestScenario.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnTestCompleted_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/RTSAITestScenario.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnTestPhaseChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/RTSAITestScenario.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_TestType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TestType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NumberOfUnits;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TestDuration;
	static void NewProp_bAutoStartTest_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoStartTest;
	static void NewProp_bEnableDetailedLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDetailedLogging;
	static void NewProp_bTestPassed_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bTestPassed;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TestResults;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TestLog_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TestLog;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TestUnits_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TestUnits;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TestEnemies_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TestEnemies;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TestObstacles_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TestObstacles;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TestGroupManager;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnTestStarted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnTestCompleted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnTestPhaseChanged;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_ARTSAITestScenario_GetTestProgress, "GetTestProgress" }, // 2877046906
		{ &Z_Construct_UFunction_ARTSAITestScenario_IsTestRunning, "IsTestRunning" }, // 3493208312
		{ &Z_Construct_UFunction_ARTSAITestScenario_LogTestResults, "LogTestResults" }, // 3049949238
		{ &Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestCompleted__DelegateSignature, "OnTestCompleted__DelegateSignature" }, // 3373912379
		{ &Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestPhaseChanged__DelegateSignature, "OnTestPhaseChanged__DelegateSignature" }, // 3835048990
		{ &Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestStarted__DelegateSignature, "OnTestStarted__DelegateSignature" }, // 3023747802
		{ &Z_Construct_UFunction_ARTSAITestScenario_ResetTest, "ResetTest" }, // 1008805626
		{ &Z_Construct_UFunction_ARTSAITestScenario_StartTest, "StartTest" }, // 3370733275
		{ &Z_Construct_UFunction_ARTSAITestScenario_StopTest, "StopTest" }, // 2177472214
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<ARTSAITestScenario>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_TestType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_TestType = { "TestType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAITestScenario, TestType), Z_Construct_UEnum_ArmorWars_ERTSTestScenarioType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestType_MetaData), NewProp_TestType_MetaData) }; // 406864
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_NumberOfUnits = { "NumberOfUnits", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAITestScenario, NumberOfUnits), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NumberOfUnits_MetaData), NewProp_NumberOfUnits_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_TestDuration = { "TestDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAITestScenario, TestDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestDuration_MetaData), NewProp_TestDuration_MetaData) };
void Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_bAutoStartTest_SetBit(void* Obj)
{
	((ARTSAITestScenario*)Obj)->bAutoStartTest = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_bAutoStartTest = { "bAutoStartTest", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARTSAITestScenario), &Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_bAutoStartTest_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoStartTest_MetaData), NewProp_bAutoStartTest_MetaData) };
void Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_bEnableDetailedLogging_SetBit(void* Obj)
{
	((ARTSAITestScenario*)Obj)->bEnableDetailedLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_bEnableDetailedLogging = { "bEnableDetailedLogging", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARTSAITestScenario), &Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_bEnableDetailedLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDetailedLogging_MetaData), NewProp_bEnableDetailedLogging_MetaData) };
void Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_bTestPassed_SetBit(void* Obj)
{
	((ARTSAITestScenario*)Obj)->bTestPassed = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_bTestPassed = { "bTestPassed", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARTSAITestScenario), &Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_bTestPassed_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bTestPassed_MetaData), NewProp_bTestPassed_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_TestResults = { "TestResults", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAITestScenario, TestResults), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestResults_MetaData), NewProp_TestResults_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_TestLog_Inner = { "TestLog", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_TestLog = { "TestLog", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAITestScenario, TestLog), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestLog_MetaData), NewProp_TestLog_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_TestUnits_Inner = { "TestUnits", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_TestUnits = { "TestUnits", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAITestScenario, TestUnits), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestUnits_MetaData), NewProp_TestUnits_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_TestEnemies_Inner = { "TestEnemies", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_TestEnemies = { "TestEnemies", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAITestScenario, TestEnemies), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestEnemies_MetaData), NewProp_TestEnemies_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_TestObstacles_Inner = { "TestObstacles", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_TestObstacles = { "TestObstacles", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAITestScenario, TestObstacles), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestObstacles_MetaData), NewProp_TestObstacles_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_TestGroupManager = { "TestGroupManager", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAITestScenario, TestGroupManager), Z_Construct_UClass_ARTSGroupManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestGroupManager_MetaData), NewProp_TestGroupManager_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_OnTestStarted = { "OnTestStarted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAITestScenario, OnTestStarted), Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestStarted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnTestStarted_MetaData), NewProp_OnTestStarted_MetaData) }; // 3023747802
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_OnTestCompleted = { "OnTestCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAITestScenario, OnTestCompleted), Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnTestCompleted_MetaData), NewProp_OnTestCompleted_MetaData) }; // 3373912379
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_OnTestPhaseChanged = { "OnTestPhaseChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAITestScenario, OnTestPhaseChanged), Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestPhaseChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnTestPhaseChanged_MetaData), NewProp_OnTestPhaseChanged_MetaData) }; // 3835048990
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_ARTSAITestScenario_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_TestType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_TestType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_NumberOfUnits,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_TestDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_bAutoStartTest,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_bEnableDetailedLogging,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_bTestPassed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_TestResults,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_TestLog_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_TestLog,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_TestUnits_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_TestUnits,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_TestEnemies_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_TestEnemies,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_TestObstacles_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_TestObstacles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_TestGroupManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_OnTestStarted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_OnTestCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAITestScenario_Statics::NewProp_OnTestPhaseChanged,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ARTSAITestScenario_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_ARTSAITestScenario_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ARTSAITestScenario_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_ARTSAITestScenario_Statics::ClassParams = {
	&ARTSAITestScenario::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_ARTSAITestScenario_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_ARTSAITestScenario_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_ARTSAITestScenario_Statics::Class_MetaDataParams), Z_Construct_UClass_ARTSAITestScenario_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_ARTSAITestScenario()
{
	if (!Z_Registration_Info_UClass_ARTSAITestScenario.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_ARTSAITestScenario.OuterSingleton, Z_Construct_UClass_ARTSAITestScenario_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_ARTSAITestScenario.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(ARTSAITestScenario);
ARTSAITestScenario::~ARTSAITestScenario() {}
// ********** End Class ARTSAITestScenario *********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSAITestScenario_h__Script_ArmorWars_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ERTSTestScenarioType_StaticEnum, TEXT("ERTSTestScenarioType"), &Z_Registration_Info_UEnum_ERTSTestScenarioType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 406864U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_ARTSAITestScenario, ARTSAITestScenario::StaticClass, TEXT("ARTSAITestScenario"), &Z_Registration_Info_UClass_ARTSAITestScenario, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(ARTSAITestScenario), 4173685273U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSAITestScenario_h__Script_ArmorWars_3556818991(TEXT("/Script/ArmorWars"),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSAITestScenario_h__Script_ArmorWars_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSAITestScenario_h__Script_ArmorWars_Statics::ClassInfo),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSAITestScenario_h__Script_ArmorWars_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSAITestScenario_h__Script_ArmorWars_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
