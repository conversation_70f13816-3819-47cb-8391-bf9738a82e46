#include "RTSAIController.h"
#include "RTSUnit.h"
#include "RTSBehaviorTreeComponent.h"
#include "RTSBehaviorNode.h"
#include "RTSBehaviorNodes.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "GameFramework/PawnMovementComponent.h"
#include "GameFramework/FloatingPawnMovement.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetMathLibrary.h"
#include "DrawDebugHelpers.h"

ARTSAIController::ARTSAIController()
{
    PrimaryActorTick.bCanEverTick = true;
    bStartAILogicOnPossess = true;

    // Create behavior tree component
    BehaviorTreeComponent = CreateDefaultSubobject<URTSBehaviorTreeComponent>(TEXT("BehaviorTreeComponent"));

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSAIController: Constructor called"));
    }
}

void ARTSAIController::BeginPlay()
{
    Super::BeginPlay();

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSAIController: BeginPlay called for %s"), *GetName());
    }

    // Set initial defend position to current location
    if (GetPawn())
    {
        DefendPosition = GetPawn()->GetActorLocation();

        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSAIController: Initial defend position set to %s"), *DefendPosition.ToString());
        }
    }
}

void ARTSAIController::OnPossess(APawn* InPawn)
{
    Super::OnPossess(InPawn);

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSAIController: OnPossess called for pawn %s"),
            InPawn ? *InPawn->GetName() : TEXT("None"));
    }

    // Initialize AI state
    SetAIState(ERTSAIState::Idle);

    // Set defend position to current location if not set
    if (DefendPosition.IsZero())
    {
        DefendPosition = InPawn->GetActorLocation();

        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSAIController: Defend position set to %s on possess"), *DefendPosition.ToString());
        }
    }

    // Start behavior tree if enabled
    if (bUseBehaviorTree && bAutoStartBehaviorTree)
    {
        if (DefaultBehaviorTree)
        {
            StartBehaviorTree(DefaultBehaviorTree);
        }
        else
        {
            // Create and start default behavior tree
            URTSBehaviorNode* DefaultTree = CreateDefaultBehaviorTree();
            if (DefaultTree)
            {
                StartBehaviorTree(DefaultTree);
            }
        }
    }
}

void ARTSAIController::OnUnPossess()
{
    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSAIController: OnUnPossess called - cleaning up AI state"));
    }

    // Clean up AI state
    CurrentTarget = FRTSAITargetInfo();
    KnownTargets.Empty();

    Super::OnUnPossess();
}

void ARTSAIController::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    // Update AI logic at specified intervals
    float CurrentTime = GetWorld()->GetTimeSeconds();
    if (CurrentTime - LastAIUpdateTime >= AIUpdateInterval)
    {
        UpdateAI(DeltaTime);
        LastAIUpdateTime = CurrentTime;
    }
}

// AI State Management
void ARTSAIController::SetAIState(ERTSAIState NewState)
{
    if (CurrentState != NewState)
    {
        ERTSAIState OldState = CurrentState;
        CurrentState = NewState;

        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSAIController: State changed from %s to %s"),
                *UEnum::GetValueAsString(OldState), *UEnum::GetValueAsString(NewState));
        }

        OnAIStateChanged.Broadcast(OldState, NewState);
        OnStateChanged(OldState, NewState);
    }
}

void ARTSAIController::SetPatrolPoints(const TArray<FVector>& Points)
{
    PatrolPoints = Points;
    CurrentPatrolIndex = 0;

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSAIController: Patrol points set - %d points"), PatrolPoints.Num());
    }

    if (PatrolPoints.Num() > 0)
    {
        SetAIState(ERTSAIState::Patrol);
    }
}

void ARTSAIController::SetDefendPosition(const FVector& Position)
{
    DefendPosition = Position;

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSAIController: Defend position set to %s"), *DefendPosition.ToString());
    }

    SetAIState(ERTSAIState::Defend);
}

void ARTSAIController::SetFollowTarget(ARTSBaseActor* Target)
{
    FollowTarget = Target;

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSAIController: Follow target set to %s"),
            Target ? *Target->GetName() : TEXT("None"));
    }

    if (Target)
    {
        SetAIState(ERTSAIState::Follow);
    }
    else
    {
        SetAIState(ERTSAIState::Idle);
    }
}

// Target Management
void ARTSAIController::AddKnownTarget(ARTSBaseActor* Target, ERTSAIPriority Priority)
{
    if (!Target)
    {
        return;
    }

    // Check if target already exists
    for (FRTSAITargetInfo& TargetInfo : KnownTargets)
    {
        if (TargetInfo.Target == Target)
        {
            // Update existing target info
            TargetInfo.Priority = Priority;
            TargetInfo.LastSeenTime = GetWorld()->GetTimeSeconds();
            TargetInfo.Distance = FVector::Dist(GetPawn()->GetActorLocation(), Target->GetActorLocation());
            TargetInfo.ThreatLevel = CalculateThreatLevel(Target);

            if (bEnableDebugLogging)
            {
                UE_LOG(LogTemp, Log, TEXT("RTSAIController: Updated known target %s - Priority: %s, Distance: %.2f"),
                    *Target->GetName(), *UEnum::GetValueAsString(Priority), TargetInfo.Distance);
            }
            return;
        }
    }

    // Add new target
    FRTSAITargetInfo NewTarget;
    NewTarget.Target = Target;
    NewTarget.Priority = Priority;
    NewTarget.LastSeenTime = GetWorld()->GetTimeSeconds();
    NewTarget.Distance = FVector::Dist(GetPawn()->GetActorLocation(), Target->GetActorLocation());
    NewTarget.ThreatLevel = CalculateThreatLevel(Target);

    KnownTargets.Add(NewTarget);

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSAIController: Added new target %s - Priority: %s, Distance: %.2f, Threat: %.2f"),
            *Target->GetName(), *UEnum::GetValueAsString(Priority), NewTarget.Distance, NewTarget.ThreatLevel);
    }

    OnEnemyDetected.Broadcast(Target);
    OnEnemyDetectedEvent(Target);
}

void ARTSAIController::RemoveKnownTarget(ARTSBaseActor* Target)
{
    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSAIController: Removing known target %s"),
            Target ? *Target->GetName() : TEXT("None"));
    }

    KnownTargets.RemoveAll([Target](const FRTSAITargetInfo& TargetInfo)
    {
        return TargetInfo.Target == Target;
    });

    // Clear current target if it was removed
    if (CurrentTarget.Target == Target)
    {
        CurrentTarget = FRTSAITargetInfo();

        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSAIController: Current target cleared"));
        }
    }
}

ARTSBaseActor* ARTSAIController::FindBestTarget()
{
    if (KnownTargets.Num() == 0)
    {
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, VeryVerbose, TEXT("RTSAIController: FindBestTarget - No known targets"));
        }
        return nullptr;
    }

    // Remove invalid targets
    int32 InitialTargetCount = KnownTargets.Num();
    KnownTargets.RemoveAll([](const FRTSAITargetInfo& TargetInfo)
    {
        return !TargetInfo.Target.IsValid() || !TargetInfo.Target->IsAlive();
    });

    if (bEnableDebugLogging && KnownTargets.Num() != InitialTargetCount)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSAIController: Removed %d invalid targets, %d remaining"),
            InitialTargetCount - KnownTargets.Num(), KnownTargets.Num());
    }

    if (KnownTargets.Num() == 0)
    {
        return nullptr;
    }

    // Find best target based on priority, threat level, and distance
    FRTSAITargetInfo* BestTarget = nullptr;
    float BestScore = -1.0f;

    for (FRTSAITargetInfo& TargetInfo : KnownTargets)
    {
        if (!TargetInfo.Target.IsValid())
        {
            continue;
        }

        // Update distance
        TargetInfo.Distance = FVector::Dist(GetPawn()->GetActorLocation(), TargetInfo.Target->GetActorLocation());

        // Calculate score (higher is better)
        float Score = 0.0f;

        // Priority weight
        switch (TargetInfo.Priority)
        {
            case ERTSAIPriority::Critical: Score += 100.0f; break;
            case ERTSAIPriority::High: Score += 75.0f; break;
            case ERTSAIPriority::Normal: Score += 50.0f; break;
            case ERTSAIPriority::Low: Score += 25.0f; break;
        }

        // Threat level weight
        Score += TargetInfo.ThreatLevel * 50.0f;

        // Distance penalty (closer is better)
        Score -= TargetInfo.Distance * 0.01f;

        if (Score > BestScore)
        {
            BestScore = Score;
            BestTarget = &TargetInfo;
        }
    }

    if (bEnableDebugLogging && BestTarget)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSAIController: Best target selected: %s (Score: %.2f, Distance: %.2f)"),
            *BestTarget->Target->GetName(), BestScore, BestTarget->Distance);
    }

    return BestTarget ? BestTarget->Target.Get() : nullptr;
}

bool ARTSAIController::HasValidTarget() const
{
    return CurrentTarget.Target.IsValid() && CurrentTarget.Target->IsAlive();
}

// Detection
TArray<ARTSBaseActor*> ARTSAIController::DetectEnemiesInRange()
{
    TArray<ARTSBaseActor*> DetectedEnemies;

    if (!GetPawn())
    {
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Warning, TEXT("RTSAIController: DetectEnemiesInRange - No pawn"));
        }
        return DetectedEnemies;
    }

    UWorld* World = GetWorld();
    if (!World)
    {
        return DetectedEnemies;
    }

    // Find all RTS actors in range
    TArray<AActor*> FoundActors;
    UGameplayStatics::GetAllActorsOfClass(World, ARTSBaseActor::StaticClass(), FoundActors);

    FVector MyLocation = GetPawn()->GetActorLocation();
    ARTSUnit* MyUnit = GetControlledUnit();

    for (AActor* Actor : FoundActors)
    {
        ARTSBaseActor* RTSActor = Cast<ARTSBaseActor>(Actor);
        if (!RTSActor || Actor == GetPawn() || !RTSActor->IsAlive())
        {
            continue;
        }

        // Check if it's an enemy
        if (MyUnit && MyUnit->IsOnSameTeam(RTSActor))
        {
            continue;
        }

        float Distance = FVector::Dist(MyLocation, RTSActor->GetActorLocation());
        if (Distance <= DetectionRange && CanSeeTarget(RTSActor))
        {
            DetectedEnemies.Add(RTSActor);
        }
    }

    if (bEnableDebugLogging && DetectedEnemies.Num() > 0)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSAIController: Detected %d enemies in range"), DetectedEnemies.Num());
    }

    return DetectedEnemies;
}

bool ARTSAIController::CanSeeTarget(ARTSBaseActor* Target) const
{
    if (!Target || !GetPawn())
    {
        return false;
    }
    
    // Simple line of sight check
    FVector Start = GetPawn()->GetActorLocation();
    FVector End = Target->GetActorLocation();
    
    FHitResult HitResult;
    FCollisionQueryParams QueryParams;
    QueryParams.AddIgnoredActor(GetPawn());
    QueryParams.AddIgnoredActor(Target);
    
    bool bHit = GetWorld()->LineTraceSingleByChannel(
        HitResult,
        Start,
        End,
        ECollisionChannel::ECC_Visibility,
        QueryParams
    );
    
    return !bHit; // Can see if no obstruction
}

// State Queries
bool ARTSAIController::IsInCombat() const
{
    return CurrentState == ERTSAIState::Attack && HasValidTarget();
}

bool ARTSAIController::ShouldRetreat() const
{
    ARTSUnit* MyUnit = GetControlledUnit();
    if (!MyUnit)
    {
        return false;
    }
    
    // Retreat if health is low
    float HealthPercentage = MyUnit->GetHealthPercentage();
    return HealthPercentage < 0.25f; // Retreat when below 25% health
}

float ARTSAIController::GetDistanceToDefendPosition() const
{
    if (!GetPawn())
    {
        return 0.0f;
    }
    
    return FVector::Dist(GetPawn()->GetActorLocation(), DefendPosition);
}

// AI Update Functions
void ARTSAIController::UpdateAI(float DeltaTime)
{
    if (!GetPawn())
    {
        return;
    }

    // Update advanced movement systems
    UpdateMovementAI(DeltaTime);

    if (bInFormation)
    {
        UpdateFormationMovement(DeltaTime);
    }

    if (bUseObstacleAvoidance)
    {
        UpdateObstacleAvoidance(DeltaTime);
    }

    UpdateGroupCoordination(DeltaTime);

    // Update combat AI
    float CurrentTime = GetWorld()->GetTimeSeconds();
    if (CurrentTime - LastCombatUpdateTime >= CombatUpdateInterval)
    {
        UpdateCombatAI(DeltaTime);
        LastCombatUpdateTime = CurrentTime;
    }

    // Update target acquisition
    UpdateTargetAcquisition(DeltaTime);

    // Update current state behavior
    UpdateCurrentState(DeltaTime);
}

void ARTSAIController::UpdateTargetAcquisition(float DeltaTime)
{
    // Detect new enemies
    TArray<ARTSBaseActor*> DetectedEnemies = DetectEnemiesInRange();

    // Add newly detected enemies to known targets
    for (ARTSBaseActor* Enemy : DetectedEnemies)
    {
        ERTSAIPriority Priority = CalculateTargetPriority(Enemy);
        AddKnownTarget(Enemy, Priority);
    }

    // Update current target if needed
    if (!HasValidTarget() || ShouldRetreat())
    {
        ARTSBaseActor* BestTarget = FindBestTarget();
        if (BestTarget && BestTarget != CurrentTarget.Target.Get())
        {
            if (bEnableDebugLogging)
            {
                UE_LOG(LogTemp, Log, TEXT("RTSAIController: Target acquired - %s"), *BestTarget->GetName());
            }

            CurrentTarget.Target = BestTarget;
            CurrentTarget.Distance = FVector::Dist(GetPawn()->GetActorLocation(), BestTarget->GetActorLocation());
            CurrentTarget.ThreatLevel = CalculateThreatLevel(BestTarget);
            CurrentTarget.Priority = CalculateTargetPriority(BestTarget);
            CurrentTarget.LastSeenTime = GetWorld()->GetTimeSeconds();

            OnTargetAcquired.Broadcast(BestTarget);
            OnTargetAcquiredEvent(BestTarget);
        }
    }
}

void ARTSAIController::UpdateCurrentState(float DeltaTime)
{
    // Check for state transitions
    if (ShouldRetreat() && CurrentState != ERTSAIState::Retreat)
    {
        SetAIState(ERTSAIState::Retreat);
    }
    else if (HasValidTarget() && bIsAggressive && CurrentState != ERTSAIState::Attack)
    {
        SetAIState(ERTSAIState::Attack);
    }

    // Handle current state
    switch (CurrentState)
    {
        case ERTSAIState::Idle:
            HandleIdleState(DeltaTime);
            break;
        case ERTSAIState::Patrol:
            HandlePatrolState(DeltaTime);
            break;
        case ERTSAIState::Attack:
            HandleAttackState(DeltaTime);
            break;
        case ERTSAIState::Defend:
            HandleDefendState(DeltaTime);
            break;
        case ERTSAIState::Follow:
            HandleFollowState(DeltaTime);
            break;
        case ERTSAIState::Retreat:
            HandleRetreatState(DeltaTime);
            break;
    }
}

// State Handlers
void ARTSAIController::HandleIdleState(float DeltaTime)
{
    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, VeryVerbose, TEXT("RTSAIController: HandleIdleState"));
    }

    // If we have patrol points, start patrolling
    if (PatrolPoints.Num() > 0)
    {
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSAIController: Switching from Idle to Patrol"));
        }
        SetAIState(ERTSAIState::Patrol);
        return;
    }

    // If we should defend, go to defend state
    if (bShouldDefend)
    {
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSAIController: Switching from Idle to Defend"));
        }
        SetAIState(ERTSAIState::Defend);
        return;
    }

    // Otherwise just stay idle and watch for enemies
}

void ARTSAIController::HandlePatrolState(float DeltaTime)
{
    if (PatrolPoints.Num() == 0)
    {
        SetAIState(ERTSAIState::Idle);
        return;
    }

    FVector CurrentPatrolPoint = PatrolPoints[CurrentPatrolIndex];

    // Move to current patrol point
    if (IsAtLocation(CurrentPatrolPoint))
    {
        MoveToNextPatrolPoint();
    }
    else
    {
        MoveToLocation(CurrentPatrolPoint);
    }
}

void ARTSAIController::HandleAttackState(float DeltaTime)
{
    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, VeryVerbose, TEXT("RTSAIController: HandleAttackState"));
    }

    if (!HasValidTarget())
    {
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSAIController: No valid target in attack state, switching behavior"));
        }

        // No valid target, return to previous behavior
        if (PatrolPoints.Num() > 0)
        {
            SetAIState(ERTSAIState::Patrol);
        }
        else if (bShouldDefend)
        {
            SetAIState(ERTSAIState::Defend);
        }
        else
        {
            SetAIState(ERTSAIState::Idle);
        }
        return;
    }

    ARTSUnit* MyUnit = GetControlledUnit();
    if (!MyUnit)
    {
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Warning, TEXT("RTSAIController: No controlled unit in attack state"));
        }
        return;
    }

    ARTSBaseActor* Target = CurrentTarget.Target.Get();
    float DistanceToTarget = FVector::Dist(GetPawn()->GetActorLocation(), Target->GetActorLocation());
    float EffectiveAttackRange = AttackRange > 0 ? AttackRange : MyUnit->GetAttackRange();

    if (DistanceToTarget <= EffectiveAttackRange)
    {
        // In range, attack
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSAIController: Attacking target %s at distance %.2f"),
                *Target->GetName(), DistanceToTarget);
        }
        MyUnit->AttackTarget(Target);
        StopMovement();
    }
    else
    {
        // Move closer to target
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, VeryVerbose, TEXT("RTSAIController: Moving closer to target %s (Distance: %.2f, Range: %.2f)"),
                *Target->GetName(), DistanceToTarget, EffectiveAttackRange);
        }
        MoveToActor(Target, EffectiveAttackRange * 0.8f);
    }
}

void ARTSAIController::HandleDefendState(float DeltaTime)
{
    float DistanceToDefendPos = GetDistanceToDefendPosition();

    // If too far from defend position, return to it
    if (DistanceToDefendPos > DefendRadius)
    {
        MoveToLocation(DefendPosition);
    }
    else
    {
        // At defend position, stop moving
        StopMovement();
    }
}

void ARTSAIController::HandleFollowState(float DeltaTime)
{
    if (!FollowTarget.IsValid())
    {
        SetAIState(ERTSAIState::Idle);
        return;
    }

    float DistanceToTarget = FVector::Dist(GetPawn()->GetActorLocation(), FollowTarget->GetActorLocation());

    // Follow at a reasonable distance
    if (DistanceToTarget > 500.0f) // 5 meters
    {
        MoveToActor(FollowTarget.Get(), 300.0f); // Stop at 3 meters
    }
    else
    {
        StopMovement();
    }
}

void ARTSAIController::HandleRetreatState(float DeltaTime)
{
    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, VeryVerbose, TEXT("RTSAIController: HandleRetreatState"));
    }

    // Move away from current target or back to defend position
    if (HasValidTarget())
    {
        FVector TargetLocation = CurrentTarget.Target->GetActorLocation();
        FVector MyLocation = GetPawn()->GetActorLocation();
        FVector RetreatDirection = (MyLocation - TargetLocation).GetSafeNormal();
        FVector RetreatLocation = MyLocation + RetreatDirection * 1000.0f; // Retreat 10 meters

        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSAIController: Retreating from target %s to location %s"),
                *CurrentTarget.Target->GetName(), *RetreatLocation.ToString());
        }

        MoveToLocation(RetreatLocation);
    }
    else
    {
        // No immediate threat, return to defend position
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSAIController: No threat, returning to defend position %s"),
                *DefendPosition.ToString());
        }

        MoveToLocation(DefendPosition);

        // Check if we can exit retreat state
        ARTSUnit* MyUnit = GetControlledUnit();
        if (MyUnit && MyUnit->GetHealthPercentage() > 0.5f)
        {
            if (bEnableDebugLogging)
            {
                UE_LOG(LogTemp, Log, TEXT("RTSAIController: Health recovered (%.2f%%), exiting retreat state"),
                    MyUnit->GetHealthPercentage() * 100.0f);
            }
            SetAIState(ERTSAIState::Defend);
        }
    }
}

// Utility Functions
void ARTSAIController::MoveToNextPatrolPoint()
{
    int32 OldIndex = CurrentPatrolIndex;
    CurrentPatrolIndex = (CurrentPatrolIndex + 1) % PatrolPoints.Num();

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSAIController: Moving to next patrol point %d (was %d)"),
            CurrentPatrolIndex, OldIndex);
    }
}

bool ARTSAIController::IsAtLocation(const FVector& Location, float Tolerance) const
{
    if (!GetPawn())
    {
        return false;
    }

    return FVector::Dist(GetPawn()->GetActorLocation(), Location) <= Tolerance;
}

float ARTSAIController::CalculateThreatLevel(ARTSBaseActor* Target) const
{
    if (!Target)
    {
        return 0.0f;
    }

    ARTSUnit* TargetUnit = Cast<ARTSUnit>(Target);
    if (!TargetUnit)
    {
        return 0.5f; // Default threat level
    }

    // Calculate threat based on unit's attack damage and health
    float ThreatLevel = 0.0f;
    ThreatLevel += TargetUnit->GetAttackDamage() * 0.01f; // Damage contribution
    ThreatLevel += TargetUnit->GetHealthPercentage() * 0.5f; // Health contribution

    float ClampedThreatLevel = FMath::Clamp(ThreatLevel, 0.0f, 1.0f);

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, VeryVerbose, TEXT("RTSAIController: Calculated threat level %.2f for %s (Damage: %.2f, Health: %.2f%%)"),
            ClampedThreatLevel, *Target->GetName(), TargetUnit->GetAttackDamage(), TargetUnit->GetHealthPercentage() * 100.0f);
    }

    return ClampedThreatLevel;
}

ERTSAIPriority ARTSAIController::CalculateTargetPriority(ARTSBaseActor* Target) const
{
    if (!Target)
    {
        return ERTSAIPriority::Low;
    }

    float Distance = FVector::Dist(GetPawn()->GetActorLocation(), Target->GetActorLocation());
    float ThreatLevel = CalculateThreatLevel(Target);

    // Closer and more threatening targets get higher priority
    if (Distance < 500.0f && ThreatLevel > 0.7f)
    {
        return ERTSAIPriority::Critical;
    }
    else if (Distance < 1000.0f && ThreatLevel > 0.5f)
    {
        return ERTSAIPriority::High;
    }
    else if (ThreatLevel > 0.3f)
    {
        return ERTSAIPriority::Normal;
    }

    return ERTSAIPriority::Low;
}

// Protected Functions
ARTSUnit* ARTSAIController::GetControlledUnit() const
{
    return Cast<ARTSUnit>(GetPawn());
}

// Advanced Movement Functions
void ARTSAIController::MoveToLocationWithPathfinding(const FVector& Destination)
{
    if (!GetPawn())
    {
        return;
    }

    CurrentDestination = Destination;

    if (bUsePathfinding)
    {
        // Find path to destination
        TArray<FVector> Path;
        if (FindPathToLocation(Destination, Path))
        {
            CurrentPath = Path;
            CurrentPathIndex = 0;

            if (bEnableDebugLogging)
            {
                UE_LOG(LogTemp, Log, TEXT("RTSAIController: Pathfinding successful - %d waypoints"), Path.Num());
            }
        }
        else
        {
            // Fallback to direct movement
            CurrentPath.Empty();
            CurrentPath.Add(Destination);
            CurrentPathIndex = 0;

            if (bEnableDebugLogging)
            {
                UE_LOG(LogTemp, Warning, TEXT("RTSAIController: Pathfinding failed - using direct movement"));
            }
        }
    }
    else
    {
        // Direct movement
        CurrentPath.Empty();
        CurrentPath.Add(Destination);
        CurrentPathIndex = 0;
    }

    // Start movement using the unit's movement component
    ARTSUnit* ControlledUnit = GetControlledUnit();
    if (ControlledUnit)
    {
        FVector NextTarget = CurrentPath.Num() > 0 ? CurrentPath[0] : Destination;
        ControlledUnit->MoveToLocation(NextTarget);
    }
}

void ARTSAIController::SetFormationMovement(bool bEnabled, ARTSAIController* NewFormationLeader, FVector NewFormationOffset)
{
    bInFormation = bEnabled;

    if (bEnabled && NewFormationLeader)
    {
        this->FormationLeader = NewFormationLeader;
        this->FormationOffset = NewFormationOffset;

        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSAIController: Formation movement enabled - Leader: %s, Offset: %s"),
                *NewFormationLeader->GetName(), *NewFormationOffset.ToString());
        }
    }
    else
    {
        this->FormationLeader = nullptr;
        this->FormationOffset = FVector::ZeroVector;

        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSAIController: Formation movement disabled"));
        }
    }
}

void ARTSAIController::EnableObstacleAvoidance(bool bEnabled)
{
    bUseObstacleAvoidance = bEnabled;

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSAIController: Obstacle avoidance %s"),
            bEnabled ? TEXT("enabled") : TEXT("disabled"));
    }
}

void ARTSAIController::SetMovementSpeed(float NewSpeed)
{
    MovementSpeed = FMath::Max(0.0f, NewSpeed);

    // Update movement component speed if available
    ARTSUnit* ControlledUnit = GetControlledUnit();
    if (ControlledUnit)
    {
        if (UPawnMovementComponent* MovementComp = ControlledUnit->GetMovementComponent())
        {
            // Set movement speed through the movement component's specific interface
            if (UFloatingPawnMovement* FloatingMovement = Cast<UFloatingPawnMovement>(MovementComp))
            {
                FloatingMovement->MaxSpeed = MovementSpeed;
            }
        }
    }

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSAIController: Movement speed set to %.2f"), MovementSpeed);
    }
}

bool ARTSAIController::IsMoving() const
{
    ARTSUnit* ControlledUnit = GetControlledUnit();
    return ControlledUnit ? ControlledUnit->IsMoving() : false;
}

bool ARTSAIController::HasReachedDestination() const
{
    if (!GetPawn() || CurrentDestination.IsZero())
    {
        return true;
    }

    float Distance = FVector::Dist(GetPawn()->GetActorLocation(), CurrentDestination);
    return Distance <= ArrivalTolerance;
}

void ARTSAIController::StopMovement()
{
    CurrentPath.Empty();
    CurrentPathIndex = 0;
    CurrentDestination = FVector::ZeroVector;

    ARTSUnit* ControlledUnit = GetControlledUnit();
    if (ControlledUnit)
    {
        ControlledUnit->StopMovement();
    }

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSAIController: Movement stopped"));
    }
}

// Group Coordination Functions
void ARTSAIController::JoinGroup(ARTSAIController* NewGroupLeader)
{
    if (!NewGroupLeader || NewGroupLeader == this)
    {
        return;
    }

    // Leave current group first
    LeaveGroup();

    // Join new group
    this->GroupLeader = NewGroupLeader;
    NewGroupLeader->GroupMembers.AddUnique(this);

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSAIController: %s joined group led by %s"),
            *GetName(), *NewGroupLeader->GetName());
    }
}

void ARTSAIController::LeaveGroup()
{
    if (GroupLeader.IsValid())
    {
        GroupLeader->GroupMembers.Remove(this);

        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSAIController: %s left group led by %s"),
                *GetName(), *GroupLeader->GetName());
        }

        GroupLeader = nullptr;
    }

    // If this was a group leader, disband the group
    if (bIsGroupLeader)
    {
        for (TWeakObjectPtr<ARTSAIController>& Member : GroupMembers)
        {
            if (Member.IsValid())
            {
                Member->GroupLeader = nullptr;
            }
        }
        GroupMembers.Empty();
        bIsGroupLeader = false;

        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSAIController: %s disbanded group"), *GetName());
        }
    }
}

void ARTSAIController::SetGroupFormation(ERTSFormationType Formation)
{
    FormationType = Formation;

    if (bIsGroupLeader)
    {
        // Update formation for all group members
        UpdateGroupFormation();
    }

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSAIController: Group formation set to %s"),
            *UEnum::GetValueAsString(Formation));
    }
}

bool ARTSAIController::IsInGroup() const
{
    return GroupLeader.IsValid() || bIsGroupLeader;
}

bool ARTSAIController::IsGroupLeader() const
{
    return bIsGroupLeader;
}

// Advanced Movement Helper Functions
void ARTSAIController::UpdateMovementAI(float DeltaTime)
{
    if (!IsMoving() || CurrentPath.Num() == 0)
    {
        return;
    }

    // Check if we need to recalculate path
    if (ShouldRecalculatePath())
    {
        TArray<FVector> NewPath;
        if (FindPathToLocation(CurrentDestination, NewPath))
        {
            CurrentPath = NewPath;
            CurrentPathIndex = 0;
            LastPathfindingTime = GetWorld()->GetTimeSeconds();
        }
    }

    // Check if we've reached the current waypoint
    if (CurrentPathIndex < CurrentPath.Num())
    {
        FVector CurrentWaypoint = CurrentPath[CurrentPathIndex];
        float DistanceToWaypoint = FVector::Dist(GetPawn()->GetActorLocation(), CurrentWaypoint);

        if (DistanceToWaypoint <= ArrivalTolerance)
        {
            CurrentPathIndex++;

            // Move to next waypoint or finish movement
            if (CurrentPathIndex < CurrentPath.Num())
            {
                ARTSUnit* ControlledUnit = GetControlledUnit();
                if (ControlledUnit)
                {
                    ControlledUnit->MoveToLocation(CurrentPath[CurrentPathIndex]);
                }
            }
            else
            {
                // Reached final destination
                CurrentPath.Empty();
                CurrentPathIndex = 0;
                CurrentDestination = FVector::ZeroVector;
            }
        }
    }
}

void ARTSAIController::UpdateFormationMovement(float DeltaTime)
{
    if (!bInFormation || !FormationLeader.IsValid())
    {
        return;
    }

    FVector FormationPosition = CalculateFormationPosition();
    if (!FormationPosition.IsZero())
    {
        MaintainFormationPosition(DeltaTime);
    }
}

void ARTSAIController::UpdateObstacleAvoidance(float DeltaTime)
{
    if (!IsMoving())
    {
        return;
    }

    FVector AvoidanceVector = CalculateAvoidanceVector();
    if (!AvoidanceVector.IsNearlyZero())
    {
        // Apply avoidance to movement
        ARTSUnit* ControlledUnit = GetControlledUnit();
        if (ControlledUnit && ControlledUnit->GetMovementComponent())
        {
            FVector CurrentVelocity = ControlledUnit->GetMovementComponent()->Velocity;
            FVector AdjustedVelocity = CurrentVelocity + (AvoidanceVector * ObstacleAvoidanceStrength);

            // Normalize and apply to movement component
            AdjustedVelocity = AdjustedVelocity.GetSafeNormal() * MovementSpeed;
            ControlledUnit->GetMovementComponent()->Velocity = AdjustedVelocity;
        }
    }
}

void ARTSAIController::UpdateGroupCoordination(float DeltaTime)
{
    if (bIsGroupLeader)
    {
        // Clean up invalid group members
        GroupMembers.RemoveAll([](const TWeakObjectPtr<ARTSAIController>& Member)
        {
            return !Member.IsValid();
        });

        // Update group formation if needed
        if (GroupMembers.Num() > 0)
        {
            UpdateGroupFormation();
        }
        else
        {
            bIsGroupLeader = false;
        }
    }
}

// Pathfinding helpers
bool ARTSAIController::FindPathToLocation(const FVector& Destination, TArray<FVector>& OutPath)
{
    OutPath.Empty();

    if (!GetPawn())
    {
        return false;
    }

    // Simple pathfinding implementation
    // In a real implementation, you would use UE's navigation system
    FVector Start = GetPawn()->GetActorLocation();

    // For now, just do a simple line-of-sight check and add waypoints if needed
    FHitResult HitResult;
    FCollisionQueryParams QueryParams;
    QueryParams.AddIgnoredActor(GetPawn());

    bool bDirectPath = !GetWorld()->LineTraceSingleByChannel(
        HitResult,
        Start,
        Destination,
        ECollisionChannel::ECC_WorldStatic,
        QueryParams
    );

    if (bDirectPath)
    {
        // Direct path available
        OutPath.Add(Destination);
        return true;
    }
    else
    {
        // Simple obstacle avoidance - try going around
        FVector ToDestination = (Destination - Start).GetSafeNormal();
        FVector RightVector = FVector::CrossProduct(ToDestination, FVector::UpVector);

        // Try right side first
        FVector RightWaypoint = Start + (ToDestination * 500.0f) + (RightVector * 300.0f);
        bool bRightClear = !GetWorld()->LineTraceSingleByChannel(
            HitResult, Start, RightWaypoint, ECollisionChannel::ECC_WorldStatic, QueryParams
        );

        if (bRightClear)
        {
            OutPath.Add(RightWaypoint);
            OutPath.Add(Destination);
            return true;
        }

        // Try left side
        FVector LeftWaypoint = Start + (ToDestination * 500.0f) - (RightVector * 300.0f);
        bool bLeftClear = !GetWorld()->LineTraceSingleByChannel(
            HitResult, Start, LeftWaypoint, ECollisionChannel::ECC_WorldStatic, QueryParams
        );

        if (bLeftClear)
        {
            OutPath.Add(LeftWaypoint);
            OutPath.Add(Destination);
            return true;
        }

        // Fallback to direct path
        OutPath.Add(Destination);
        return false;
    }
}

FVector ARTSAIController::GetNextPathPoint()
{
    if (CurrentPathIndex < CurrentPath.Num())
    {
        return CurrentPath[CurrentPathIndex];
    }
    return FVector::ZeroVector;
}

bool ARTSAIController::ShouldRecalculatePath() const
{
    float CurrentTime = GetWorld()->GetTimeSeconds();
    return (CurrentTime - LastPathfindingTime) >= PathfindingInterval;
}

// Formation movement helpers
FVector ARTSAIController::CalculateFormationPosition() const
{
    if (!FormationLeader.IsValid() || !GetPawn())
    {
        return FVector::ZeroVector;
    }

    FVector LeaderLocation = FormationLeader->GetPawn()->GetActorLocation();
    FRotator LeaderRotation = FormationLeader->GetPawn()->GetActorRotation();

    // Transform formation offset by leader's rotation
    FVector WorldOffset = LeaderRotation.RotateVector(FormationOffset);
    return LeaderLocation + WorldOffset;
}

void ARTSAIController::MaintainFormationPosition(float DeltaTime)
{
    FVector TargetPosition = CalculateFormationPosition();
    if (TargetPosition.IsZero())
    {
        return;
    }

    float DistanceToFormationPosition = FVector::Dist(GetPawn()->GetActorLocation(), TargetPosition);

    // Only move if we're too far from formation position
    if (DistanceToFormationPosition > ArrivalTolerance * 2.0f)
    {
        ARTSUnit* ControlledUnit = GetControlledUnit();
        if (ControlledUnit)
        {
            ControlledUnit->MoveToLocation(TargetPosition);
        }
    }
}

// Obstacle avoidance helpers
FVector ARTSAIController::CalculateAvoidanceVector() const
{
    if (!GetPawn())
    {
        return FVector::ZeroVector;
    }

    TArray<AActor*> NearbyObstacles = GetNearbyObstacles();
    FVector AvoidanceVector = FVector::ZeroVector;
    FVector MyLocation = GetPawn()->GetActorLocation();

    for (AActor* Obstacle : NearbyObstacles)
    {
        if (!Obstacle)
        {
            continue;
        }

        FVector ToObstacle = Obstacle->GetActorLocation() - MyLocation;
        float Distance = ToObstacle.Size();

        if (Distance > 0.0f && Distance < ObstacleAvoidanceRadius)
        {
            // Calculate avoidance force (stronger when closer)
            FVector AvoidanceDirection = -ToObstacle.GetSafeNormal();
            float AvoidanceStrength = (ObstacleAvoidanceRadius - Distance) / ObstacleAvoidanceRadius;
            AvoidanceVector += AvoidanceDirection * AvoidanceStrength;
        }
    }

    return AvoidanceVector.GetSafeNormal();
}

bool ARTSAIController::IsObstacleInPath(const FVector& Direction, float Distance) const
{
    if (!GetPawn())
    {
        return false;
    }

    FVector Start = GetPawn()->GetActorLocation();
    FVector End = Start + (Direction.GetSafeNormal() * Distance);

    FHitResult HitResult;
    FCollisionQueryParams QueryParams;
    QueryParams.AddIgnoredActor(GetPawn());

    return GetWorld()->LineTraceSingleByChannel(
        HitResult,
        Start,
        End,
        ECollisionChannel::ECC_WorldStatic,
        QueryParams
    );
}

TArray<AActor*> ARTSAIController::GetNearbyObstacles() const
{
    TArray<AActor*> NearbyObstacles;

    if (!GetPawn())
    {
        return NearbyObstacles;
    }

    // Find all actors within obstacle avoidance radius
    FVector MyLocation = GetPawn()->GetActorLocation();

    // Get all static mesh actors (potential obstacles)
    TArray<AActor*> AllActors;
    UGameplayStatics::GetAllActorsOfClass(GetWorld(), AActor::StaticClass(), AllActors);

    for (AActor* Actor : AllActors)
    {
        if (!Actor || Actor == GetPawn())
        {
            continue;
        }

        // Check if it's a potential obstacle (has static mesh component)
        if (Actor->FindComponentByClass<UStaticMeshComponent>())
        {
            float Distance = FVector::Dist(MyLocation, Actor->GetActorLocation());
            if (Distance <= ObstacleAvoidanceRadius)
            {
                NearbyObstacles.Add(Actor);
            }
        }
    }

    return NearbyObstacles;
}

// Group coordination helpers
void ARTSAIController::BroadcastToGroup(const FString& Message)
{
    if (!bIsGroupLeader)
    {
        return;
    }

    for (TWeakObjectPtr<ARTSAIController>& Member : GroupMembers)
    {
        if (Member.IsValid())
        {
            Member->ReceiveGroupMessage(Message, this);
        }
    }
}

void ARTSAIController::ReceiveGroupMessage(const FString& Message, ARTSAIController* Sender)
{
    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSAIController: %s received group message from %s: %s"),
            *GetName(), Sender ? *Sender->GetName() : TEXT("Unknown"), *Message);
    }

    // Handle different message types
    if (Message == TEXT("ATTACK"))
    {
        SetAIState(ERTSAIState::Attack);
    }
    else if (Message == TEXT("RETREAT"))
    {
        SetAIState(ERTSAIState::Retreat);
    }
    else if (Message == TEXT("DEFEND"))
    {
        SetAIState(ERTSAIState::Defend);
    }
}

void ARTSAIController::UpdateGroupFormation()
{
    if (!bIsGroupLeader || GroupMembers.Num() == 0)
    {
        return;
    }

    // Calculate formation positions based on formation type
    for (int32 i = 0; i < GroupMembers.Num(); i++)
    {
        if (!GroupMembers[i].IsValid())
        {
            continue;
        }

        FVector LocalFormationOffset = FVector::ZeroVector;

        switch (FormationType)
        {
            case ERTSFormationType::Line:
                FormationOffset = FVector(0.0f, (i + 1) * 200.0f, 0.0f); // Line formation
                break;
            case ERTSFormationType::Column:
                FormationOffset = FVector((i + 1) * -200.0f, 0.0f, 0.0f); // Column formation
                break;
            case ERTSFormationType::Wedge:
                {
                    float Side = (i % 2 == 0) ? 1.0f : -1.0f;
                    float Row = FMath::FloorToFloat((i + 1) / 2.0f);
                    FormationOffset = FVector(-Row * 150.0f, Side * Row * 100.0f, 0.0f);
                }
                break;
            default:
                FormationOffset = FVector(0.0f, (i + 1) * 200.0f, 0.0f);
                break;
        }

        GroupMembers[i]->SetFormationMovement(true, this, FormationOffset);
    }
}

// Advanced Combat Functions
void ARTSAIController::SetCombatBehavior(ERTSCombatBehavior Behavior)
{
    CombatBehavior = Behavior;

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSAIController: Combat behavior set to %s"),
            *UEnum::GetValueAsString(Behavior));
    }
}

void ARTSAIController::SetEngagementRange(float MinRange, float MaxRange)
{
    MinEngagementRange = FMath::Max(0.0f, MinRange);
    MaxEngagementRange = FMath::Max(MinEngagementRange, MaxRange);

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSAIController: Engagement range set to %.2f - %.2f"),
            MinEngagementRange, MaxEngagementRange);
    }
}

void ARTSAIController::EnableKitingBehavior(bool bEnabled, float NewKitingDistance)
{
    bUseKiting = bEnabled;
    this->KitingDistance = FMath::Max(100.0f, NewKitingDistance);

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSAIController: Kiting behavior %s (Distance: %.2f)"),
            bEnabled ? TEXT("enabled") : TEXT("disabled"), this->KitingDistance);
    }
}

void ARTSAIController::SetFlankingBehavior(bool bEnabled, float NewFlankingRadius)
{
    bUseFlanking = bEnabled;
    this->FlankingRadius = FMath::Max(200.0f, NewFlankingRadius);

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSAIController: Flanking behavior %s (Radius: %.2f)"),
            bEnabled ? TEXT("enabled") : TEXT("disabled"), this->FlankingRadius);
    }
}

void ARTSAIController::EnableCoverSeeking(bool bEnabled)
{
    bUseCoverSeeking = bEnabled;

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSAIController: Cover seeking %s"),
            bEnabled ? TEXT("enabled") : TEXT("disabled"));
    }
}

void ARTSAIController::SetRetreatThreshold(float HealthPercentage)
{
    RetreatHealthThreshold = FMath::Clamp(HealthPercentage, 0.0f, 1.0f);

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSAIController: Retreat threshold set to %.2f%%"),
            RetreatHealthThreshold * 100.0f);
    }
}

bool ARTSAIController::IsInOptimalRange() const
{
    if (!HasValidTarget())
    {
        return false;
    }

    float DistanceToTarget = FVector::Dist(GetPawn()->GetActorLocation(),
        CurrentTarget.Target->GetActorLocation());

    // Use weapon range if engagement ranges not set
    float MinRange = MinEngagementRange > 0.0f ? MinEngagementRange : 0.0f;
    float MaxRange = MaxEngagementRange > 0.0f ? MaxEngagementRange : AttackRange;

    return DistanceToTarget >= MinRange && DistanceToTarget <= MaxRange;
}

bool ARTSAIController::ShouldKite() const
{
    if (!bUseKiting || !HasValidTarget())
    {
        return false;
    }

    float DistanceToTarget = FVector::Dist(GetPawn()->GetActorLocation(),
        CurrentTarget.Target->GetActorLocation());

    // Kite if target is too close
    return DistanceToTarget < KitingDistance;
}

bool ARTSAIController::ShouldFlank() const
{
    if (!bUseFlanking || !HasValidTarget())
    {
        return false;
    }

    // Check if we have a clear shot at the target
    return !HasLineOfSightToTarget(CurrentTarget.Target.Get());
}

void ARTSAIController::CoordinateAttackWithGroup(ARTSBaseActor* Target)
{
    if (!Target || !bIsGroupLeader)
    {
        return;
    }

    // Broadcast attack command to group
    BroadcastToGroup(TEXT("ATTACK"));

    // Set target for all group members
    for (TWeakObjectPtr<ARTSAIController>& Member : GroupMembers)
    {
        if (Member.IsValid())
        {
            Member->AddKnownTarget(Target, ERTSAIPriority::High);
            Member->SetAIState(ERTSAIState::Attack);
        }
    }

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSAIController: Coordinated attack on %s with %d group members"),
            *Target->GetName(), GroupMembers.Num());
    }
}

// Combat AI Helper Functions
void ARTSAIController::UpdateCombatAI(float DeltaTime)
{
    if (!GetPawn() || !IsInCombat())
    {
        return;
    }

    // Check retreat conditions
    ARTSUnit* ControlledUnit = GetControlledUnit();
    if (ControlledUnit && ControlledUnit->GetHealthPercentage() <= RetreatHealthThreshold)
    {
        if (!bIsRetreating)
        {
            bIsRetreating = true;
            SetAIState(ERTSAIState::Retreat);

            if (bEnableDebugLogging)
            {
                UE_LOG(LogTemp, Log, TEXT("RTSAIController: Initiating retreat - Health: %.2f%%"),
                    ControlledUnit->GetHealthPercentage() * 100.0f);
            }
        }
        return;
    }
    else
    {
        bIsRetreating = false;
    }

    // Execute current combat behavior
    ExecuteCombatBehavior(DeltaTime);
}

void ARTSAIController::ExecuteCombatBehavior(float DeltaTime)
{
    switch (CombatBehavior)
    {
        case ERTSCombatBehavior::Aggressive:
            HandleAggressiveBehavior(DeltaTime);
            break;
        case ERTSCombatBehavior::Defensive:
            HandleDefensiveBehavior(DeltaTime);
            break;
        case ERTSCombatBehavior::Kiting:
            HandleKitingBehavior(DeltaTime);
            break;
        case ERTSCombatBehavior::Flanking:
            HandleFlankingBehavior(DeltaTime);
            break;
        case ERTSCombatBehavior::CoverSeeking:
            HandleCoverSeekingBehavior(DeltaTime);
            break;
        case ERTSCombatBehavior::SupportFire:
            HandleSupportFireBehavior(DeltaTime);
            break;
        case ERTSCombatBehavior::HitAndRun:
            HandleHitAndRunBehavior(DeltaTime);
            break;
    }
}

void ARTSAIController::HandleAggressiveBehavior(float DeltaTime)
{
    if (!HasValidTarget())
    {
        return;
    }

    ARTSBaseActor* Target = CurrentTarget.Target.Get();

    // Move directly towards target if not in range
    if (!IsInOptimalRange())
    {
        MoveToLocationWithPathfinding(Target->GetActorLocation());
    }
    else
    {
        // Stop and attack
        StopMovement();
    }
}

void ARTSAIController::HandleDefensiveBehavior(float DeltaTime)
{
    if (!HasValidTarget())
    {
        return;
    }

    // Stay near defend position and only engage if target comes close
    float DistanceToDefendPosition = GetDistanceToDefendPosition();

    if (DistanceToDefendPosition > DefendRadius * 0.5f)
    {
        // Return to defend position
        MoveToLocationWithPathfinding(DefendPosition);
    }
    else if (IsInOptimalRange())
    {
        // Stop and attack from defensive position
        StopMovement();
    }
}

void ARTSAIController::HandleKitingBehavior(float DeltaTime)
{
    if (!HasValidTarget())
    {
        return;
    }

    ARTSBaseActor* Target = CurrentTarget.Target.Get();

    if (ShouldKite())
    {
        // Move away from target while maintaining line of sight
        FVector KitingPosition = FindKitingPosition(Target);
        if (!KitingPosition.IsZero())
        {
            MoveToLocationWithPathfinding(KitingPosition);
        }
    }
    else if (!IsInOptimalRange())
    {
        // Move to optimal range
        FVector OptimalPosition = FindOptimalCombatPosition();
        if (!OptimalPosition.IsZero())
        {
            MoveToLocationWithPathfinding(OptimalPosition);
        }
    }
    else
    {
        // In optimal range, stop and attack
        StopMovement();
    }
}

void ARTSAIController::HandleFlankingBehavior(float DeltaTime)
{
    if (!HasValidTarget())
    {
        return;
    }

    ARTSBaseActor* Target = CurrentTarget.Target.Get();

    if (ShouldFlank())
    {
        // Find flanking position
        FVector FlankingPosition = FindFlankingPosition(Target);
        if (!FlankingPosition.IsZero())
        {
            MoveToLocationWithPathfinding(FlankingPosition);
        }
    }
    else if (!IsInOptimalRange())
    {
        // Move to optimal range
        FVector OptimalPosition = FindOptimalCombatPosition();
        if (!OptimalPosition.IsZero())
        {
            MoveToLocationWithPathfinding(OptimalPosition);
        }
    }
    else
    {
        // In optimal range with line of sight, stop and attack
        StopMovement();
    }
}

void ARTSAIController::HandleCoverSeekingBehavior(float DeltaTime)
{
    if (!HasValidTarget())
    {
        return;
    }

    if (!IsInCover() && bUseCoverSeeking)
    {
        // Find cover position
        FVector CoverPosition = FindCoverPosition();
        if (!CoverPosition.IsZero())
        {
            MoveToLocationWithPathfinding(CoverPosition);
        }
    }
    else if (IsInOptimalRange() && HasLineOfSightToTarget(CurrentTarget.Target.Get()))
    {
        // In cover with line of sight, stop and attack
        StopMovement();
    }
}

void ARTSAIController::HandleSupportFireBehavior(float DeltaTime)
{
    if (!HasValidTarget())
    {
        return;
    }

    // Stay at maximum range and provide support fire
    float MaxRange = MaxEngagementRange > 0.0f ? MaxEngagementRange : AttackRange;
    ARTSBaseActor* Target = CurrentTarget.Target.Get();
    float DistanceToTarget = FVector::Dist(GetPawn()->GetActorLocation(), Target->GetActorLocation());

    if (DistanceToTarget < MaxRange * 0.8f)
    {
        // Move to support position (further back)
        FVector ToTarget = (Target->GetActorLocation() - GetPawn()->GetActorLocation()).GetSafeNormal();
        FVector SupportPosition = Target->GetActorLocation() - (ToTarget * MaxRange * 0.9f);
        MoveToLocationWithPathfinding(SupportPosition);
    }
    else if (DistanceToTarget <= MaxRange)
    {
        // In support range, stop and provide covering fire
        StopMovement();
    }
}

void ARTSAIController::HandleHitAndRunBehavior(float DeltaTime)
{
    if (!HasValidTarget())
    {
        return;
    }

    ARTSBaseActor* Target = CurrentTarget.Target.Get();
    float CurrentTime = GetWorld()->GetTimeSeconds();

    // Engage for a short time, then retreat
    if (CurrentTime - LastEngagementTime > 3.0f) // Engage for 3 seconds
    {
        // Time to retreat
        FVector RetreatPosition = GetPawn()->GetActorLocation() +
            (GetPawn()->GetActorLocation() - Target->GetActorLocation()).GetSafeNormal() * 500.0f;
        MoveToLocationWithPathfinding(RetreatPosition);

        // Reset engagement timer after retreating
        if (FVector::Dist(GetPawn()->GetActorLocation(), RetreatPosition) < 100.0f)
        {
            LastEngagementTime = CurrentTime;
        }
    }
    else if (IsInOptimalRange())
    {
        // Engage target
        StopMovement();
        if (LastEngagementTime == 0.0f)
        {
            LastEngagementTime = CurrentTime;
        }
    }
    else
    {
        // Move to engagement range
        MoveToLocationWithPathfinding(Target->GetActorLocation());
    }
}

// Combat positioning helpers
FVector ARTSAIController::FindOptimalCombatPosition() const
{
    if (!HasValidTarget())
    {
        return FVector::ZeroVector;
    }

    ARTSBaseActor* Target = CurrentTarget.Target.Get();
    FVector MyLocation = GetPawn()->GetActorLocation();
    FVector TargetLocation = Target->GetActorLocation();

    // Calculate optimal range
    float OptimalRange = MaxEngagementRange > 0.0f ? MaxEngagementRange * 0.7f : AttackRange * 0.7f;

    // Find position at optimal range
    FVector ToTarget = (TargetLocation - MyLocation).GetSafeNormal();
    FVector OptimalPosition = TargetLocation - (ToTarget * OptimalRange);

    return OptimalPosition;
}

FVector ARTSAIController::FindFlankingPosition(ARTSBaseActor* Target) const
{
    if (!Target)
    {
        return FVector::ZeroVector;
    }

    FVector MyLocation = GetPawn()->GetActorLocation();
    FVector TargetLocation = Target->GetActorLocation();
    FVector ToTarget = (TargetLocation - MyLocation).GetSafeNormal();

    // Calculate flanking positions (left and right)
    FVector RightVector = FVector::CrossProduct(ToTarget, FVector::UpVector);
    FVector LeftFlankPosition = TargetLocation + (RightVector * FlankingRadius);
    FVector RightFlankPosition = TargetLocation - (RightVector * FlankingRadius);

    // Choose the flanking position that's closer
    float LeftDistance = FVector::Dist(MyLocation, LeftFlankPosition);
    float RightDistance = FVector::Dist(MyLocation, RightFlankPosition);

    return LeftDistance < RightDistance ? LeftFlankPosition : RightFlankPosition;
}

FVector ARTSAIController::FindCoverPosition() const
{
    if (!GetPawn())
    {
        return FVector::ZeroVector;
    }

    // Simple cover finding - look for nearby obstacles
    TArray<AActor*> NearbyObstacles = GetNearbyObstacles();
    FVector MyLocation = GetPawn()->GetActorLocation();
    FVector BestCoverPosition = FVector::ZeroVector;
    float BestCoverScore = -1.0f;

    for (AActor* Obstacle : NearbyObstacles)
    {
        if (!Obstacle)
        {
            continue;
        }

        // Find position behind obstacle
        FVector ObstacleLocation = Obstacle->GetActorLocation();
        FVector ToObstacle = (ObstacleLocation - MyLocation).GetSafeNormal();
        FVector CoverPosition = ObstacleLocation + (ToObstacle * 200.0f); // 2 meters behind obstacle

        // Score based on distance and cover quality
        float Distance = FVector::Dist(MyLocation, CoverPosition);
        float CoverScore = 1000.0f / (Distance + 1.0f); // Closer is better

        if (CoverScore > BestCoverScore)
        {
            BestCoverScore = CoverScore;
            BestCoverPosition = CoverPosition;
        }
    }

    return BestCoverPosition;
}

FVector ARTSAIController::FindKitingPosition(ARTSBaseActor* Target) const
{
    if (!Target)
    {
        return FVector::ZeroVector;
    }

    FVector MyLocation = GetPawn()->GetActorLocation();
    FVector TargetLocation = Target->GetActorLocation();

    // Move away from target while maintaining optimal range
    FVector AwayFromTarget = (MyLocation - TargetLocation).GetSafeNormal();
    float OptimalRange = MaxEngagementRange > 0.0f ? MaxEngagementRange * 0.8f : AttackRange * 0.8f;

    FVector KitingPosition = TargetLocation + (AwayFromTarget * OptimalRange);

    return KitingPosition;
}

bool ARTSAIController::IsInCover() const
{
    if (!GetPawn() || !HasValidTarget())
    {
        return false;
    }

    // Check if there's an obstacle between us and the target
    FVector MyLocation = GetPawn()->GetActorLocation();
    FVector TargetLocation = CurrentTarget.Target->GetActorLocation();

    FHitResult HitResult;
    FCollisionQueryParams QueryParams;
    QueryParams.AddIgnoredActor(GetPawn());
    QueryParams.AddIgnoredActor(CurrentTarget.Target.Get());

    bool bHitObstacle = GetWorld()->LineTraceSingleByChannel(
        HitResult,
        MyLocation,
        TargetLocation,
        ECollisionChannel::ECC_WorldStatic,
        QueryParams
    );

    return bHitObstacle;
}

bool ARTSAIController::HasLineOfSightToTarget(ARTSBaseActor* Target) const
{
    if (!Target || !GetPawn())
    {
        return false;
    }

    FVector MyLocation = GetPawn()->GetActorLocation();
    FVector TargetLocation = Target->GetActorLocation();

    FHitResult HitResult;
    FCollisionQueryParams QueryParams;
    QueryParams.AddIgnoredActor(GetPawn());
    QueryParams.AddIgnoredActor(Target);

    bool bHitObstacle = GetWorld()->LineTraceSingleByChannel(
        HitResult,
        MyLocation,
        TargetLocation,
        ECollisionChannel::ECC_Visibility,
        QueryParams
    );

    return !bHitObstacle; // Has line of sight if no obstacle hit
}

// Target analysis
float ARTSAIController::CalculateTargetThreat(ARTSBaseActor* Target) const
{
    if (!Target)
    {
        return 0.0f;
    }

    float ThreatLevel = 0.5f; // Base threat

    // Factor in target's health (lower health = lower threat)
    ThreatLevel *= Target->GetHealthPercentage();

    // Factor in distance (closer = higher threat)
    float Distance = FVector::Dist(GetPawn()->GetActorLocation(), Target->GetActorLocation());
    float DistanceFactor = FMath::Clamp(1000.0f / (Distance + 1.0f), 0.1f, 2.0f);
    ThreatLevel *= DistanceFactor;

    // Factor in target type (could be expanded based on unit types)
    // For now, all targets have equal base threat

    return FMath::Clamp(ThreatLevel, 0.0f, 1.0f);
}

bool ARTSAIController::IsTargetInRange(ARTSBaseActor* Target) const
{
    if (!Target)
    {
        return false;
    }

    float Distance = FVector::Dist(GetPawn()->GetActorLocation(), Target->GetActorLocation());
    float MaxRange = MaxEngagementRange > 0.0f ? MaxEngagementRange : AttackRange;

    return Distance <= MaxRange;
}

bool ARTSAIController::ShouldEngageTarget(ARTSBaseActor* Target) const
{
    if (!Target || !Target->IsAlive())
    {
        return false;
    }

    // Check if target is in range
    if (!IsTargetInRange(Target))
    {
        return false;
    }

    // Check if we have line of sight (unless using flanking behavior)
    if (CombatBehavior != ERTSCombatBehavior::Flanking && !HasLineOfSightToTarget(Target))
    {
        return false;
    }

    // Check if target is an enemy
    ARTSUnit* MyUnit = GetControlledUnit();
    if (MyUnit && MyUnit->IsOnSameTeam(Target))
    {
        return false;
    }

    return true;
}

ARTSBaseActor* ARTSAIController::FindBestCombatTarget() const
{
    ARTSBaseActor* BestTarget = nullptr;
    float BestScore = -1.0f;

    for (const FRTSAITargetInfo& TargetInfo : KnownTargets)
    {
        if (!TargetInfo.Target.IsValid() || !ShouldEngageTarget(TargetInfo.Target.Get()))
        {
            continue;
        }

        // Calculate combat score
        float Score = 0.0f;

        // Priority weight
        switch (TargetInfo.Priority)
        {
            case ERTSAIPriority::Critical: Score += 100.0f; break;
            case ERTSAIPriority::High: Score += 75.0f; break;
            case ERTSAIPriority::Normal: Score += 50.0f; break;
            case ERTSAIPriority::Low: Score += 25.0f; break;
        }

        // Threat level
        Score += CalculateTargetThreat(TargetInfo.Target.Get()) * 50.0f;

        // Distance factor (closer targets preferred for most behaviors)
        if (CombatBehavior != ERTSCombatBehavior::SupportFire)
        {
            Score -= TargetInfo.Distance * 0.01f;
        }
        else
        {
            // For support fire, prefer targets at medium range
            float OptimalSupportRange = AttackRange * 0.7f;
            float RangeDifference = FMath::Abs(TargetInfo.Distance - OptimalSupportRange);
            Score -= RangeDifference * 0.02f;
        }

        if (Score > BestScore)
        {
            BestScore = Score;
            BestTarget = TargetInfo.Target.Get();
        }
    }

    return BestTarget;
}

// Behavior Tree Functions
void ARTSAIController::StartBehaviorTree(URTSBehaviorNode* RootNode)
{
    if (!BehaviorTreeComponent || !RootNode)
    {
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Warning, TEXT("RTSAIController: Cannot start behavior tree - missing component or root node"));
        }
        return;
    }

    BehaviorTreeComponent->StartBehaviorTree(RootNode);
    bUseBehaviorTree = true;

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSAIController: Started behavior tree %s"), *RootNode->NodeName);
    }
}

void ARTSAIController::StopBehaviorTree()
{
    if (BehaviorTreeComponent)
    {
        BehaviorTreeComponent->StopBehaviorTree();
    }

    bUseBehaviorTree = false;

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSAIController: Stopped behavior tree"));
    }
}

void ARTSAIController::PauseBehaviorTree()
{
    if (BehaviorTreeComponent)
    {
        BehaviorTreeComponent->PauseBehaviorTree();
    }

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSAIController: Paused behavior tree"));
    }
}

void ARTSAIController::ResumeBehaviorTree()
{
    if (BehaviorTreeComponent)
    {
        BehaviorTreeComponent->ResumeBehaviorTree();
    }

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSAIController: Resumed behavior tree"));
    }
}

bool ARTSAIController::IsBehaviorTreeRunning() const
{
    return BehaviorTreeComponent ? BehaviorTreeComponent->IsRunning() : false;
}

URTSBehaviorNode* ARTSAIController::CreateDefaultBehaviorTree()
{
    // Create a default behavior tree: Patrol or Guard behavior
    URTSCompositeNode* RootSelector = NewObject<URTSCompositeNode>(this);
    RootSelector->NodeName = TEXT("Default AI Root");
    RootSelector->CompositeType = ERTSCompositeType::Selector;

    // Branch 1: Combat behavior (if enemy nearby)
    URTSCompositeNode* CombatSequence = NewObject<URTSCompositeNode>(this);
    CombatSequence->NodeName = TEXT("Combat Sequence");
    CombatSequence->CompositeType = ERTSCompositeType::Sequence;

    URTSEnemyNearbyCondition* EnemyNearby = NewObject<URTSEnemyNearbyCondition>(this);
    URTSFindEnemyTask* FindEnemy = NewObject<URTSFindEnemyTask>(this);
    URTSAttackTargetTask* AttackTarget = NewObject<URTSAttackTargetTask>(this);

    CombatSequence->AddChildNode(EnemyNearby);
    CombatSequence->AddChildNode(FindEnemy);
    CombatSequence->AddChildNode(AttackTarget);

    // Branch 2: Patrol behavior (if no enemies)
    URTSPatrolTask* PatrolTask = NewObject<URTSPatrolTask>(this);
    if (PatrolPoints.Num() > 0)
    {
        PatrolTask->PatrolPoints = PatrolPoints;
    }
    else
    {
        // Default patrol around defend position
        PatrolTask->PatrolPoints.Add(DefendPosition + FVector(500, 0, 0));
        PatrolTask->PatrolPoints.Add(DefendPosition + FVector(0, 500, 0));
        PatrolTask->PatrolPoints.Add(DefendPosition + FVector(-500, 0, 0));
        PatrolTask->PatrolPoints.Add(DefendPosition + FVector(0, -500, 0));
    }

    // Add branches to root selector
    RootSelector->AddChildNode(CombatSequence);
    RootSelector->AddChildNode(PatrolTask);

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSAIController: Created default behavior tree"));
    }

    return RootSelector;
}

URTSBehaviorNode* ARTSAIController::CreateCombatBehaviorTree()
{
    // Create a combat-focused behavior tree
    URTSCompositeNode* RootSelector = NewObject<URTSCompositeNode>(this);
    RootSelector->NodeName = TEXT("Combat AI Root");
    RootSelector->CompositeType = ERTSCompositeType::Selector;

    // Branch 1: Retreat if health is low
    URTSCompositeNode* RetreatSequence = NewObject<URTSCompositeNode>(this);
    RetreatSequence->NodeName = TEXT("Retreat Sequence");
    RetreatSequence->CompositeType = ERTSCompositeType::Sequence;

    URTSHealthLowCondition* HealthLow = NewObject<URTSHealthLowCondition>(this);
    URTSMoveToLocationTask* RetreatMove = NewObject<URTSMoveToLocationTask>(this);
    RetreatMove->TargetLocationKey = TEXT("RetreatLocation");

    RetreatSequence->AddChildNode(HealthLow);
    RetreatSequence->AddChildNode(RetreatMove);

    // Branch 2: Attack enemies
    URTSCompositeNode* AttackSequence = NewObject<URTSCompositeNode>(this);
    AttackSequence->NodeName = TEXT("Attack Sequence");
    AttackSequence->CompositeType = ERTSCompositeType::Sequence;

    URTSFindEnemyTask* FindEnemy = NewObject<URTSFindEnemyTask>(this);
    URTSAttackTargetTask* AttackTarget = NewObject<URTSAttackTargetTask>(this);

    AttackSequence->AddChildNode(FindEnemy);
    AttackSequence->AddChildNode(AttackTarget);

    // Branch 3: Defend position
    URTSMoveToLocationTask* DefendMove = NewObject<URTSMoveToLocationTask>(this);
    DefendMove->TargetLocationKey = TEXT("DefendPosition");

    // Set defend position in blackboard
    if (BehaviorTreeComponent)
    {
        BehaviorTreeComponent->SetBlackboardVector(TEXT("DefendPosition"), DefendPosition);
        BehaviorTreeComponent->SetBlackboardVector(TEXT("RetreatLocation"), DefendPosition + FVector(-1000, 0, 0));
    }

    // Add branches to root selector
    RootSelector->AddChildNode(RetreatSequence);
    RootSelector->AddChildNode(AttackSequence);
    RootSelector->AddChildNode(DefendMove);

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSAIController: Created combat behavior tree"));
    }

    return RootSelector;
}

URTSBehaviorNode* ARTSAIController::CreatePatrolBehaviorTree(const TArray<FVector>& PatrolWaypoints)
{
    // Create a patrol-focused behavior tree
    URTSCompositeNode* RootSelector = NewObject<URTSCompositeNode>(this);
    RootSelector->NodeName = TEXT("Patrol AI Root");
    RootSelector->CompositeType = ERTSCompositeType::Selector;

    // Branch 1: Combat if enemy nearby
    URTSCompositeNode* CombatSequence = NewObject<URTSCompositeNode>(this);
    CombatSequence->NodeName = TEXT("Combat Sequence");
    CombatSequence->CompositeType = ERTSCompositeType::Sequence;

    URTSEnemyNearbyCondition* EnemyNearby = NewObject<URTSEnemyNearbyCondition>(this);
    URTSFindEnemyTask* FindEnemy = NewObject<URTSFindEnemyTask>(this);
    URTSAttackTargetTask* AttackTarget = NewObject<URTSAttackTargetTask>(this);

    CombatSequence->AddChildNode(EnemyNearby);
    CombatSequence->AddChildNode(FindEnemy);
    CombatSequence->AddChildNode(AttackTarget);

    // Branch 2: Patrol
    URTSPatrolTask* PatrolTask = NewObject<URTSPatrolTask>(this);
    PatrolTask->PatrolPoints = PatrolWaypoints;
    PatrolTask->bLoopPatrol = true;

    // Add branches to root selector
    RootSelector->AddChildNode(CombatSequence);
    RootSelector->AddChildNode(PatrolTask);

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSAIController: Created patrol behavior tree with %d points"), PatrolWaypoints.Num());
    }

    return RootSelector;
}

void ARTSAIController::OnUnitDeath()
{
    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Warning, TEXT("RTSAIController: OnUnitDeath called - unit has died"));
    }

    // Stop all AI behavior
    SetAIState(ERTSAIState::Idle);

    // Clear all targets
    CurrentTarget = FRTSAITargetInfo();
    KnownTargets.Empty();

    // Stop behavior tree
    if (BehaviorTreeComponent)
    {
        BehaviorTreeComponent->StopBehaviorTree();
    }

    // Clear movement
    StopMovement();

    // Leave any groups
    LeaveGroup();

    // Disable AI updates
    SetActorTickEnabled(false);
}
