#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "RTSBehaviorNode.h"
#include "RTSBehaviorNodes.h"
#include "RTSMovementBehaviorTree.generated.h"

class ARTSUnit;
class URTSBehaviorTreeComponent;

/**
 * Factory class for creating movement-focused behavior trees
 * Handles complex movement scenarios including formation movement,
 * return fire while moving, and collision avoidance
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API URTSMovementBehaviorTreeFactory : public UObject
{
    GENERATED_BODY()

public:
    URTSMovementBehaviorTreeFactory();

    // Factory methods for creating different movement behavior trees
    UFUNCTION(BlueprintCallable, Category = "Movement Behavior Tree")
    static URTSBehaviorNode* CreateBasicMovementTree();

    UFUNCTION(BlueprintCallable, Category = "Movement Behavior Tree")
    static URTSBehaviorNode* CreateFormationMovementTree();

    UFUNCTION(BlueprintCallable, Category = "Movement Behavior Tree")
    static URTSBehaviorNode* CreateCombatMovementTree();

    UFUNCTION(BlueprintCallable, Category = "Movement Behavior Tree")
    static URTSBehaviorNode* CreateAdvancedUnitBehaviorTree();

    UFUNCTION(BlueprintCallable, Category = "Movement Behavior Tree")
    static URTSBehaviorNode* CreateCommandResponsiveBehaviorTree();

protected:
    // Helper methods for creating common node combinations
    static URTSCompositeNode* CreateCommandPrioritySelector();
    static URTSCompositeNode* CreateMovementSequence();
    static URTSCompositeNode* CreateCombatSequence();
    static URTSCompositeNode* CreateFormationSequence();
    static URTSCompositeNode* CreateCollisionAvoidanceSequence();
};

/**
 * Specialized movement task that handles collision avoidance
 * Prevents units from bumping into each other during movement
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API URTSCollisionAvoidanceMoveTask : public URTSTaskNode
{
    GENERATED_BODY()

public:
    URTSCollisionAvoidanceMoveTask();

    virtual ERTSBehaviorNodeStatus ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime) override;
    virtual void InitializeNode(URTSBehaviorTreeComponent* BehaviorTreeComponent) override;

    // Collision avoidance parameters
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Avoidance")
    float AvoidanceRadius = 150.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Avoidance")
    float AvoidanceStrength = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Avoidance")
    float LookAheadDistance = 300.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Avoidance")
    int32 MaxAvoidanceUnits = 10;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    FString TargetLocationKey = TEXT("TargetLocation");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    float AcceptanceRadius = 100.0f;

protected:

    // Internal state
    FVector AvoidanceVector = FVector::ZeroVector;
    TArray<TWeakObjectPtr<ARTSUnit>> NearbyUnits;
    float LastAvoidanceUpdate = 0.0f;
    float AvoidanceUpdateInterval = 0.1f;

    // Helper functions
    virtual FVector CalculateAvoidanceVector(ARTSUnit* Unit);
    virtual TArray<ARTSUnit*> FindNearbyUnits(ARTSUnit* Unit);
    virtual FVector GetSeparationVector(ARTSUnit* Unit, const TArray<ARTSUnit*>& LocalNearbyUnits);
    virtual FVector GetAlignmentVector(ARTSUnit* Unit, const TArray<ARTSUnit*>& LocalNearbyUnits);
    virtual FVector GetCohesionVector(ARTSUnit* Unit, const TArray<ARTSUnit*>& LocalNearbyUnits);
};

/**
 * Condition node for checking if path is blocked
 * Used to trigger alternative movement behaviors
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API URTSPathBlockedCondition : public URTSConditionNode
{
    GENERATED_BODY()

public:
    URTSPathBlockedCondition();

    virtual ERTSBehaviorNodeStatus ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime) override;

protected:
    // Path checking parameters
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Path Checking")
    float CheckDistance = 200.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Path Checking")
    float CheckWidth = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Path Checking")
    int32 CheckPoints = 5;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Path Checking")
    FString TargetLocationKey = TEXT("TargetLocation");
};

/**
 * Task node for finding alternative paths when blocked
 * Implements simple pathfinding around obstacles
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API URTSFindAlternativePathTask : public URTSTaskNode
{
    GENERATED_BODY()

public:
    URTSFindAlternativePathTask();

    virtual ERTSBehaviorNodeStatus ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime) override;
    virtual void InitializeNode(URTSBehaviorTreeComponent* BehaviorTreeComponent) override;

protected:
    // Pathfinding parameters
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding")
    float SearchRadius = 300.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding")
    int32 SearchAngles = 8; // Number of directions to try

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding")
    float MinClearDistance = 150.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding")
    FString TargetLocationKey = TEXT("TargetLocation");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding")
    FString AlternativePathKey = TEXT("AlternativePath");

    // Internal state
    TArray<FVector> CurrentPath;
    int32 CurrentPathIndex = 0;

    // Helper functions
    virtual TArray<FVector> FindPath(ARTSUnit* Unit, const FVector& Start, const FVector& End);
    virtual bool IsPathClear(ARTSUnit* Unit, const FVector& Start, const FVector& End);
    virtual FVector FindClearDirection(ARTSUnit* Unit, const FVector& PreferredDirection);
};

/**
 * Decorator that modifies movement speed based on formation status
 * Ensures formation units move at synchronized speeds
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API URTSFormationSpeedDecorator : public URTSDecoratorNode
{
    GENERATED_BODY()

public:
    URTSFormationSpeedDecorator();

    virtual ERTSBehaviorNodeStatus ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime) override;

protected:
    // Speed modification parameters
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation Speed")
    float FormationSpeedMultiplier = 0.8f; // Slightly slower in formation

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation Speed")
    bool bSynchronizeWithSlowestUnit = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation Speed")
    float MinSpeedMultiplier = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation Speed")
    float MaxSpeedMultiplier = 1.2f;

    // Internal state
    float CurrentSpeedMultiplier = 1.0f;
    float LastSpeedUpdate = 0.0f;
    float SpeedUpdateInterval = 0.5f;

    // Helper functions
    virtual float CalculateFormationSpeed(ARTSUnit* Unit);
    virtual float GetSlowestFormationUnitSpeed(ARTSUnit* Unit);
};

/**
 * Task node for maintaining formation spacing
 * Adjusts unit position to maintain proper formation spacing
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API URTSMaintainFormationSpacingTask : public URTSTaskNode
{
    GENERATED_BODY()

public:
    URTSMaintainFormationSpacingTask();

    virtual ERTSBehaviorNodeStatus ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime) override;
    virtual void InitializeNode(URTSBehaviorTreeComponent* BehaviorTreeComponent) override;

protected:
    // Spacing parameters
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation Spacing")
    float DesiredSpacing = 200.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation Spacing")
    float SpacingTolerance = 50.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation Spacing")
    float MaxSpacingAdjustment = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation Spacing")
    float SpacingCheckInterval = 0.2f;

    // Internal state
    float LastSpacingCheck = 0.0f;
    FVector LastAdjustment = FVector::ZeroVector;

    // Helper functions
    virtual FVector CalculateSpacingAdjustment(ARTSUnit* Unit);
    virtual TArray<ARTSUnit*> GetNearbyFormationUnits(ARTSUnit* Unit);
    virtual bool IsSpacingCorrect(ARTSUnit* Unit);
};
