// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RTSCombatBehaviorTree.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeRTSCombatBehaviorTree() {}

// ********** Begin Cross Module References ********************************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSAdvancedTargetSelectionTask();
ARMORWARS_API UClass* Z_Construct_UClass_URTSAdvancedTargetSelectionTask_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSBehaviorNode_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSCombatBehaviorTreeFactory();
ARMORWARS_API UClass* Z_Construct_UClass_URTSCombatBehaviorTreeFactory_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSConditionNode();
ARMORWARS_API UClass* Z_Construct_UClass_URTSCoordinatedAttackTask();
ARMORWARS_API UClass* Z_Construct_UClass_URTSCoordinatedAttackTask_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSReturnFireTask();
ARMORWARS_API UClass* Z_Construct_UClass_URTSReturnFireTask_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSTacticalAdvantageCondition();
ARMORWARS_API UClass* Z_Construct_UClass_URTSTacticalAdvantageCondition_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSTacticalPositioningTask();
ARMORWARS_API UClass* Z_Construct_UClass_URTSTacticalPositioningTask_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSTaskNode();
ARMORWARS_API UClass* Z_Construct_UClass_URTSUnderAttackCondition();
ARMORWARS_API UClass* Z_Construct_UClass_URTSUnderAttackCondition_NoRegister();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
UPackage* Z_Construct_UPackage__Script_ArmorWars();
// ********** End Cross Module References **********************************************************

// ********** Begin Class URTSCombatBehaviorTreeFactory Function CreateAggressiveCombatTree ********
struct Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateAggressiveCombatTree_Statics
{
	struct RTSCombatBehaviorTreeFactory_eventCreateAggressiveCombatTree_Parms
	{
		URTSBehaviorNode* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Combat Behavior Tree" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateAggressiveCombatTree_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCombatBehaviorTreeFactory_eventCreateAggressiveCombatTree_Parms, ReturnValue), Z_Construct_UClass_URTSBehaviorNode_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateAggressiveCombatTree_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateAggressiveCombatTree_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateAggressiveCombatTree_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateAggressiveCombatTree_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCombatBehaviorTreeFactory, nullptr, "CreateAggressiveCombatTree", Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateAggressiveCombatTree_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateAggressiveCombatTree_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateAggressiveCombatTree_Statics::RTSCombatBehaviorTreeFactory_eventCreateAggressiveCombatTree_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateAggressiveCombatTree_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateAggressiveCombatTree_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateAggressiveCombatTree_Statics::RTSCombatBehaviorTreeFactory_eventCreateAggressiveCombatTree_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateAggressiveCombatTree()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateAggressiveCombatTree_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCombatBehaviorTreeFactory::execCreateAggressiveCombatTree)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(URTSBehaviorNode**)Z_Param__Result=URTSCombatBehaviorTreeFactory::CreateAggressiveCombatTree();
	P_NATIVE_END;
}
// ********** End Class URTSCombatBehaviorTreeFactory Function CreateAggressiveCombatTree **********

// ********** Begin Class URTSCombatBehaviorTreeFactory Function CreateBasicCombatTree *************
struct Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateBasicCombatTree_Statics
{
	struct RTSCombatBehaviorTreeFactory_eventCreateBasicCombatTree_Parms
	{
		URTSBehaviorNode* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Combat Behavior Tree" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Factory methods for creating different combat behavior trees\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Factory methods for creating different combat behavior trees" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateBasicCombatTree_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCombatBehaviorTreeFactory_eventCreateBasicCombatTree_Parms, ReturnValue), Z_Construct_UClass_URTSBehaviorNode_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateBasicCombatTree_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateBasicCombatTree_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateBasicCombatTree_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateBasicCombatTree_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCombatBehaviorTreeFactory, nullptr, "CreateBasicCombatTree", Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateBasicCombatTree_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateBasicCombatTree_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateBasicCombatTree_Statics::RTSCombatBehaviorTreeFactory_eventCreateBasicCombatTree_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateBasicCombatTree_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateBasicCombatTree_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateBasicCombatTree_Statics::RTSCombatBehaviorTreeFactory_eventCreateBasicCombatTree_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateBasicCombatTree()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateBasicCombatTree_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCombatBehaviorTreeFactory::execCreateBasicCombatTree)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(URTSBehaviorNode**)Z_Param__Result=URTSCombatBehaviorTreeFactory::CreateBasicCombatTree();
	P_NATIVE_END;
}
// ********** End Class URTSCombatBehaviorTreeFactory Function CreateBasicCombatTree ***************

// ********** Begin Class URTSCombatBehaviorTreeFactory Function CreateDefensiveCombatTree *********
struct Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateDefensiveCombatTree_Statics
{
	struct RTSCombatBehaviorTreeFactory_eventCreateDefensiveCombatTree_Parms
	{
		URTSBehaviorNode* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Combat Behavior Tree" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateDefensiveCombatTree_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCombatBehaviorTreeFactory_eventCreateDefensiveCombatTree_Parms, ReturnValue), Z_Construct_UClass_URTSBehaviorNode_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateDefensiveCombatTree_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateDefensiveCombatTree_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateDefensiveCombatTree_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateDefensiveCombatTree_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCombatBehaviorTreeFactory, nullptr, "CreateDefensiveCombatTree", Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateDefensiveCombatTree_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateDefensiveCombatTree_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateDefensiveCombatTree_Statics::RTSCombatBehaviorTreeFactory_eventCreateDefensiveCombatTree_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateDefensiveCombatTree_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateDefensiveCombatTree_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateDefensiveCombatTree_Statics::RTSCombatBehaviorTreeFactory_eventCreateDefensiveCombatTree_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateDefensiveCombatTree()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateDefensiveCombatTree_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCombatBehaviorTreeFactory::execCreateDefensiveCombatTree)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(URTSBehaviorNode**)Z_Param__Result=URTSCombatBehaviorTreeFactory::CreateDefensiveCombatTree();
	P_NATIVE_END;
}
// ********** End Class URTSCombatBehaviorTreeFactory Function CreateDefensiveCombatTree ***********

// ********** Begin Class URTSCombatBehaviorTreeFactory Function CreateSupportCombatTree ***********
struct Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateSupportCombatTree_Statics
{
	struct RTSCombatBehaviorTreeFactory_eventCreateSupportCombatTree_Parms
	{
		URTSBehaviorNode* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Combat Behavior Tree" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateSupportCombatTree_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCombatBehaviorTreeFactory_eventCreateSupportCombatTree_Parms, ReturnValue), Z_Construct_UClass_URTSBehaviorNode_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateSupportCombatTree_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateSupportCombatTree_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateSupportCombatTree_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateSupportCombatTree_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCombatBehaviorTreeFactory, nullptr, "CreateSupportCombatTree", Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateSupportCombatTree_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateSupportCombatTree_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateSupportCombatTree_Statics::RTSCombatBehaviorTreeFactory_eventCreateSupportCombatTree_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateSupportCombatTree_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateSupportCombatTree_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateSupportCombatTree_Statics::RTSCombatBehaviorTreeFactory_eventCreateSupportCombatTree_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateSupportCombatTree()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateSupportCombatTree_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCombatBehaviorTreeFactory::execCreateSupportCombatTree)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(URTSBehaviorNode**)Z_Param__Result=URTSCombatBehaviorTreeFactory::CreateSupportCombatTree();
	P_NATIVE_END;
}
// ********** End Class URTSCombatBehaviorTreeFactory Function CreateSupportCombatTree *************

// ********** Begin Class URTSCombatBehaviorTreeFactory Function CreateTacticalCombatTree **********
struct Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateTacticalCombatTree_Statics
{
	struct RTSCombatBehaviorTreeFactory_eventCreateTacticalCombatTree_Parms
	{
		URTSBehaviorNode* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Combat Behavior Tree" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateTacticalCombatTree_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCombatBehaviorTreeFactory_eventCreateTacticalCombatTree_Parms, ReturnValue), Z_Construct_UClass_URTSBehaviorNode_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateTacticalCombatTree_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateTacticalCombatTree_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateTacticalCombatTree_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateTacticalCombatTree_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCombatBehaviorTreeFactory, nullptr, "CreateTacticalCombatTree", Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateTacticalCombatTree_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateTacticalCombatTree_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateTacticalCombatTree_Statics::RTSCombatBehaviorTreeFactory_eventCreateTacticalCombatTree_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateTacticalCombatTree_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateTacticalCombatTree_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateTacticalCombatTree_Statics::RTSCombatBehaviorTreeFactory_eventCreateTacticalCombatTree_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateTacticalCombatTree()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateTacticalCombatTree_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCombatBehaviorTreeFactory::execCreateTacticalCombatTree)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(URTSBehaviorNode**)Z_Param__Result=URTSCombatBehaviorTreeFactory::CreateTacticalCombatTree();
	P_NATIVE_END;
}
// ********** End Class URTSCombatBehaviorTreeFactory Function CreateTacticalCombatTree ************

// ********** Begin Class URTSCombatBehaviorTreeFactory ********************************************
void URTSCombatBehaviorTreeFactory::StaticRegisterNativesURTSCombatBehaviorTreeFactory()
{
	UClass* Class = URTSCombatBehaviorTreeFactory::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CreateAggressiveCombatTree", &URTSCombatBehaviorTreeFactory::execCreateAggressiveCombatTree },
		{ "CreateBasicCombatTree", &URTSCombatBehaviorTreeFactory::execCreateBasicCombatTree },
		{ "CreateDefensiveCombatTree", &URTSCombatBehaviorTreeFactory::execCreateDefensiveCombatTree },
		{ "CreateSupportCombatTree", &URTSCombatBehaviorTreeFactory::execCreateSupportCombatTree },
		{ "CreateTacticalCombatTree", &URTSCombatBehaviorTreeFactory::execCreateTacticalCombatTree },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSCombatBehaviorTreeFactory;
UClass* URTSCombatBehaviorTreeFactory::GetPrivateStaticClass()
{
	using TClass = URTSCombatBehaviorTreeFactory;
	if (!Z_Registration_Info_UClass_URTSCombatBehaviorTreeFactory.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSCombatBehaviorTreeFactory"),
			Z_Registration_Info_UClass_URTSCombatBehaviorTreeFactory.InnerSingleton,
			StaticRegisterNativesURTSCombatBehaviorTreeFactory,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSCombatBehaviorTreeFactory.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSCombatBehaviorTreeFactory_NoRegister()
{
	return URTSCombatBehaviorTreeFactory::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSCombatBehaviorTreeFactory_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Factory class for creating combat-focused behavior trees\n * Handles complex combat scenarios including target prioritization,\n * tactical positioning, and coordinated attacks\n */" },
#endif
		{ "IncludePath", "RTSCombatBehaviorTree.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Factory class for creating combat-focused behavior trees\nHandles complex combat scenarios including target prioritization,\ntactical positioning, and coordinated attacks" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateAggressiveCombatTree, "CreateAggressiveCombatTree" }, // 468289686
		{ &Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateBasicCombatTree, "CreateBasicCombatTree" }, // 1166873641
		{ &Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateDefensiveCombatTree, "CreateDefensiveCombatTree" }, // 3257717139
		{ &Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateSupportCombatTree, "CreateSupportCombatTree" }, // 4269864202
		{ &Z_Construct_UFunction_URTSCombatBehaviorTreeFactory_CreateTacticalCombatTree, "CreateTacticalCombatTree" }, // 3974931618
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSCombatBehaviorTreeFactory>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_URTSCombatBehaviorTreeFactory_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSCombatBehaviorTreeFactory_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSCombatBehaviorTreeFactory_Statics::ClassParams = {
	&URTSCombatBehaviorTreeFactory::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSCombatBehaviorTreeFactory_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSCombatBehaviorTreeFactory_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSCombatBehaviorTreeFactory()
{
	if (!Z_Registration_Info_UClass_URTSCombatBehaviorTreeFactory.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSCombatBehaviorTreeFactory.OuterSingleton, Z_Construct_UClass_URTSCombatBehaviorTreeFactory_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSCombatBehaviorTreeFactory.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSCombatBehaviorTreeFactory);
URTSCombatBehaviorTreeFactory::~URTSCombatBehaviorTreeFactory() {}
// ********** End Class URTSCombatBehaviorTreeFactory **********************************************

// ********** Begin Class URTSAdvancedTargetSelectionTask ******************************************
void URTSAdvancedTargetSelectionTask::StaticRegisterNativesURTSAdvancedTargetSelectionTask()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSAdvancedTargetSelectionTask;
UClass* URTSAdvancedTargetSelectionTask::GetPrivateStaticClass()
{
	using TClass = URTSAdvancedTargetSelectionTask;
	if (!Z_Registration_Info_UClass_URTSAdvancedTargetSelectionTask.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSAdvancedTargetSelectionTask"),
			Z_Registration_Info_UClass_URTSAdvancedTargetSelectionTask.InnerSingleton,
			StaticRegisterNativesURTSAdvancedTargetSelectionTask,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSAdvancedTargetSelectionTask.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSAdvancedTargetSelectionTask_NoRegister()
{
	return URTSAdvancedTargetSelectionTask::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSAdvancedTargetSelectionTask_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Advanced target selection task with priority system\n * Selects targets based on threat level, distance, and tactical value\n */" },
#endif
		{ "IncludePath", "RTSCombatBehaviorTree.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Advanced target selection task with priority system\nSelects targets based on threat level, distance, and tactical value" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxTargetRange_MetaData[] = {
		{ "Category", "Target Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Target selection parameters\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Target selection parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseUnitWeaponRange_MetaData[] = {
		{ "Category", "Target Selection" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealthPriorityWeight_MetaData[] = {
		{ "Category", "Target Selection" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DistancePriorityWeight_MetaData[] = {
		{ "Category", "Target Selection" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ThreatPriorityWeight_MetaData[] = {
		{ "Category", "Target Selection" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SelectedTargetKey_MetaData[] = {
		{ "Category", "Target Selection" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetSwitchCooldown_MetaData[] = {
		{ "Category", "Target Selection" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxTargetRange;
	static void NewProp_bUseUnitWeaponRange_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseUnitWeaponRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HealthPriorityWeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DistancePriorityWeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ThreatPriorityWeight;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SelectedTargetKey;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TargetSwitchCooldown;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSAdvancedTargetSelectionTask>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSAdvancedTargetSelectionTask_Statics::NewProp_MaxTargetRange = { "MaxTargetRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSAdvancedTargetSelectionTask, MaxTargetRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxTargetRange_MetaData), NewProp_MaxTargetRange_MetaData) };
void Z_Construct_UClass_URTSAdvancedTargetSelectionTask_Statics::NewProp_bUseUnitWeaponRange_SetBit(void* Obj)
{
	((URTSAdvancedTargetSelectionTask*)Obj)->bUseUnitWeaponRange = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSAdvancedTargetSelectionTask_Statics::NewProp_bUseUnitWeaponRange = { "bUseUnitWeaponRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSAdvancedTargetSelectionTask), &Z_Construct_UClass_URTSAdvancedTargetSelectionTask_Statics::NewProp_bUseUnitWeaponRange_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseUnitWeaponRange_MetaData), NewProp_bUseUnitWeaponRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSAdvancedTargetSelectionTask_Statics::NewProp_HealthPriorityWeight = { "HealthPriorityWeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSAdvancedTargetSelectionTask, HealthPriorityWeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealthPriorityWeight_MetaData), NewProp_HealthPriorityWeight_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSAdvancedTargetSelectionTask_Statics::NewProp_DistancePriorityWeight = { "DistancePriorityWeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSAdvancedTargetSelectionTask, DistancePriorityWeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DistancePriorityWeight_MetaData), NewProp_DistancePriorityWeight_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSAdvancedTargetSelectionTask_Statics::NewProp_ThreatPriorityWeight = { "ThreatPriorityWeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSAdvancedTargetSelectionTask, ThreatPriorityWeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ThreatPriorityWeight_MetaData), NewProp_ThreatPriorityWeight_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_URTSAdvancedTargetSelectionTask_Statics::NewProp_SelectedTargetKey = { "SelectedTargetKey", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSAdvancedTargetSelectionTask, SelectedTargetKey), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SelectedTargetKey_MetaData), NewProp_SelectedTargetKey_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSAdvancedTargetSelectionTask_Statics::NewProp_TargetSwitchCooldown = { "TargetSwitchCooldown", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSAdvancedTargetSelectionTask, TargetSwitchCooldown), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetSwitchCooldown_MetaData), NewProp_TargetSwitchCooldown_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSAdvancedTargetSelectionTask_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSAdvancedTargetSelectionTask_Statics::NewProp_MaxTargetRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSAdvancedTargetSelectionTask_Statics::NewProp_bUseUnitWeaponRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSAdvancedTargetSelectionTask_Statics::NewProp_HealthPriorityWeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSAdvancedTargetSelectionTask_Statics::NewProp_DistancePriorityWeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSAdvancedTargetSelectionTask_Statics::NewProp_ThreatPriorityWeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSAdvancedTargetSelectionTask_Statics::NewProp_SelectedTargetKey,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSAdvancedTargetSelectionTask_Statics::NewProp_TargetSwitchCooldown,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSAdvancedTargetSelectionTask_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSAdvancedTargetSelectionTask_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_URTSTaskNode,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSAdvancedTargetSelectionTask_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSAdvancedTargetSelectionTask_Statics::ClassParams = {
	&URTSAdvancedTargetSelectionTask::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_URTSAdvancedTargetSelectionTask_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSAdvancedTargetSelectionTask_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSAdvancedTargetSelectionTask_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSAdvancedTargetSelectionTask_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSAdvancedTargetSelectionTask()
{
	if (!Z_Registration_Info_UClass_URTSAdvancedTargetSelectionTask.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSAdvancedTargetSelectionTask.OuterSingleton, Z_Construct_UClass_URTSAdvancedTargetSelectionTask_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSAdvancedTargetSelectionTask.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSAdvancedTargetSelectionTask);
URTSAdvancedTargetSelectionTask::~URTSAdvancedTargetSelectionTask() {}
// ********** End Class URTSAdvancedTargetSelectionTask ********************************************

// ********** Begin Class URTSTacticalPositioningTask **********************************************
void URTSTacticalPositioningTask::StaticRegisterNativesURTSTacticalPositioningTask()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSTacticalPositioningTask;
UClass* URTSTacticalPositioningTask::GetPrivateStaticClass()
{
	using TClass = URTSTacticalPositioningTask;
	if (!Z_Registration_Info_UClass_URTSTacticalPositioningTask.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSTacticalPositioningTask"),
			Z_Registration_Info_UClass_URTSTacticalPositioningTask.InnerSingleton,
			StaticRegisterNativesURTSTacticalPositioningTask,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSTacticalPositioningTask.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSTacticalPositioningTask_NoRegister()
{
	return URTSTacticalPositioningTask::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSTacticalPositioningTask_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Tactical positioning task for optimal combat positioning\n * Considers cover, range, and formation positioning\n */" },
#endif
		{ "IncludePath", "RTSCombatBehaviorTree.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tactical positioning task for optimal combat positioning\nConsiders cover, range, and formation positioning" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OptimalRange_MetaData[] = {
		{ "Category", "Tactical Positioning" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Positioning parameters\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Positioning parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinRange_MetaData[] = {
		{ "Category", "Tactical Positioning" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxRange_MetaData[] = {
		{ "Category", "Tactical Positioning" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bMaintainFormationPosition_MetaData[] = {
		{ "Category", "Tactical Positioning" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSeekCover_MetaData[] = {
		{ "Category", "Tactical Positioning" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetActorKey_MetaData[] = {
		{ "Category", "Tactical Positioning" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TacticalPositionKey_MetaData[] = {
		{ "Category", "Tactical Positioning" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_OptimalRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxRange;
	static void NewProp_bMaintainFormationPosition_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bMaintainFormationPosition;
	static void NewProp_bSeekCover_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSeekCover;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TargetActorKey;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TacticalPositionKey;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSTacticalPositioningTask>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSTacticalPositioningTask_Statics::NewProp_OptimalRange = { "OptimalRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSTacticalPositioningTask, OptimalRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OptimalRange_MetaData), NewProp_OptimalRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSTacticalPositioningTask_Statics::NewProp_MinRange = { "MinRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSTacticalPositioningTask, MinRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinRange_MetaData), NewProp_MinRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSTacticalPositioningTask_Statics::NewProp_MaxRange = { "MaxRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSTacticalPositioningTask, MaxRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxRange_MetaData), NewProp_MaxRange_MetaData) };
void Z_Construct_UClass_URTSTacticalPositioningTask_Statics::NewProp_bMaintainFormationPosition_SetBit(void* Obj)
{
	((URTSTacticalPositioningTask*)Obj)->bMaintainFormationPosition = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSTacticalPositioningTask_Statics::NewProp_bMaintainFormationPosition = { "bMaintainFormationPosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSTacticalPositioningTask), &Z_Construct_UClass_URTSTacticalPositioningTask_Statics::NewProp_bMaintainFormationPosition_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bMaintainFormationPosition_MetaData), NewProp_bMaintainFormationPosition_MetaData) };
void Z_Construct_UClass_URTSTacticalPositioningTask_Statics::NewProp_bSeekCover_SetBit(void* Obj)
{
	((URTSTacticalPositioningTask*)Obj)->bSeekCover = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSTacticalPositioningTask_Statics::NewProp_bSeekCover = { "bSeekCover", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSTacticalPositioningTask), &Z_Construct_UClass_URTSTacticalPositioningTask_Statics::NewProp_bSeekCover_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSeekCover_MetaData), NewProp_bSeekCover_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_URTSTacticalPositioningTask_Statics::NewProp_TargetActorKey = { "TargetActorKey", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSTacticalPositioningTask, TargetActorKey), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetActorKey_MetaData), NewProp_TargetActorKey_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_URTSTacticalPositioningTask_Statics::NewProp_TacticalPositionKey = { "TacticalPositionKey", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSTacticalPositioningTask, TacticalPositionKey), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TacticalPositionKey_MetaData), NewProp_TacticalPositionKey_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSTacticalPositioningTask_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSTacticalPositioningTask_Statics::NewProp_OptimalRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSTacticalPositioningTask_Statics::NewProp_MinRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSTacticalPositioningTask_Statics::NewProp_MaxRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSTacticalPositioningTask_Statics::NewProp_bMaintainFormationPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSTacticalPositioningTask_Statics::NewProp_bSeekCover,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSTacticalPositioningTask_Statics::NewProp_TargetActorKey,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSTacticalPositioningTask_Statics::NewProp_TacticalPositionKey,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSTacticalPositioningTask_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSTacticalPositioningTask_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_URTSTaskNode,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSTacticalPositioningTask_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSTacticalPositioningTask_Statics::ClassParams = {
	&URTSTacticalPositioningTask::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_URTSTacticalPositioningTask_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSTacticalPositioningTask_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSTacticalPositioningTask_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSTacticalPositioningTask_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSTacticalPositioningTask()
{
	if (!Z_Registration_Info_UClass_URTSTacticalPositioningTask.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSTacticalPositioningTask.OuterSingleton, Z_Construct_UClass_URTSTacticalPositioningTask_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSTacticalPositioningTask.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSTacticalPositioningTask);
URTSTacticalPositioningTask::~URTSTacticalPositioningTask() {}
// ********** End Class URTSTacticalPositioningTask ************************************************

// ********** Begin Class URTSCoordinatedAttackTask ************************************************
void URTSCoordinatedAttackTask::StaticRegisterNativesURTSCoordinatedAttackTask()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSCoordinatedAttackTask;
UClass* URTSCoordinatedAttackTask::GetPrivateStaticClass()
{
	using TClass = URTSCoordinatedAttackTask;
	if (!Z_Registration_Info_UClass_URTSCoordinatedAttackTask.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSCoordinatedAttackTask"),
			Z_Registration_Info_UClass_URTSCoordinatedAttackTask.InnerSingleton,
			StaticRegisterNativesURTSCoordinatedAttackTask,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSCoordinatedAttackTask.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSCoordinatedAttackTask_NoRegister()
{
	return URTSCoordinatedAttackTask::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSCoordinatedAttackTask_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Coordinated attack task for group combat\n * Synchronizes attacks with nearby friendly units\n */" },
#endif
		{ "IncludePath", "RTSCombatBehaviorTree.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Coordinated attack task for group combat\nSynchronizes attacks with nearby friendly units" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CoordinationRadius_MetaData[] = {
		{ "Category", "Coordinated Attack" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Coordination parameters\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Coordination parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinCoordinatedUnits_MetaData[] = {
		{ "Category", "Coordinated Attack" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackSynchronizationDelay_MetaData[] = {
		{ "Category", "Coordinated Attack" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bWaitForAllies_MetaData[] = {
		{ "Category", "Coordinated Attack" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetActorKey_MetaData[] = {
		{ "Category", "Coordinated Attack" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CoordinationRadius;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MinCoordinatedUnits;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AttackSynchronizationDelay;
	static void NewProp_bWaitForAllies_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bWaitForAllies;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TargetActorKey;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSCoordinatedAttackTask>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSCoordinatedAttackTask_Statics::NewProp_CoordinationRadius = { "CoordinationRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCoordinatedAttackTask, CoordinationRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CoordinationRadius_MetaData), NewProp_CoordinationRadius_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_URTSCoordinatedAttackTask_Statics::NewProp_MinCoordinatedUnits = { "MinCoordinatedUnits", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCoordinatedAttackTask, MinCoordinatedUnits), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinCoordinatedUnits_MetaData), NewProp_MinCoordinatedUnits_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSCoordinatedAttackTask_Statics::NewProp_AttackSynchronizationDelay = { "AttackSynchronizationDelay", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCoordinatedAttackTask, AttackSynchronizationDelay), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackSynchronizationDelay_MetaData), NewProp_AttackSynchronizationDelay_MetaData) };
void Z_Construct_UClass_URTSCoordinatedAttackTask_Statics::NewProp_bWaitForAllies_SetBit(void* Obj)
{
	((URTSCoordinatedAttackTask*)Obj)->bWaitForAllies = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSCoordinatedAttackTask_Statics::NewProp_bWaitForAllies = { "bWaitForAllies", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSCoordinatedAttackTask), &Z_Construct_UClass_URTSCoordinatedAttackTask_Statics::NewProp_bWaitForAllies_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bWaitForAllies_MetaData), NewProp_bWaitForAllies_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_URTSCoordinatedAttackTask_Statics::NewProp_TargetActorKey = { "TargetActorKey", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCoordinatedAttackTask, TargetActorKey), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetActorKey_MetaData), NewProp_TargetActorKey_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSCoordinatedAttackTask_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCoordinatedAttackTask_Statics::NewProp_CoordinationRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCoordinatedAttackTask_Statics::NewProp_MinCoordinatedUnits,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCoordinatedAttackTask_Statics::NewProp_AttackSynchronizationDelay,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCoordinatedAttackTask_Statics::NewProp_bWaitForAllies,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCoordinatedAttackTask_Statics::NewProp_TargetActorKey,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSCoordinatedAttackTask_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSCoordinatedAttackTask_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_URTSTaskNode,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSCoordinatedAttackTask_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSCoordinatedAttackTask_Statics::ClassParams = {
	&URTSCoordinatedAttackTask::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_URTSCoordinatedAttackTask_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSCoordinatedAttackTask_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSCoordinatedAttackTask_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSCoordinatedAttackTask_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSCoordinatedAttackTask()
{
	if (!Z_Registration_Info_UClass_URTSCoordinatedAttackTask.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSCoordinatedAttackTask.OuterSingleton, Z_Construct_UClass_URTSCoordinatedAttackTask_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSCoordinatedAttackTask.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSCoordinatedAttackTask);
URTSCoordinatedAttackTask::~URTSCoordinatedAttackTask() {}
// ********** End Class URTSCoordinatedAttackTask **************************************************

// ********** Begin Class URTSReturnFireTask *******************************************************
void URTSReturnFireTask::StaticRegisterNativesURTSReturnFireTask()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSReturnFireTask;
UClass* URTSReturnFireTask::GetPrivateStaticClass()
{
	using TClass = URTSReturnFireTask;
	if (!Z_Registration_Info_UClass_URTSReturnFireTask.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSReturnFireTask"),
			Z_Registration_Info_UClass_URTSReturnFireTask.InnerSingleton,
			StaticRegisterNativesURTSReturnFireTask,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSReturnFireTask.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSReturnFireTask_NoRegister()
{
	return URTSReturnFireTask::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSReturnFireTask_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Return fire task for defensive combat\n * Automatically returns fire while maintaining primary objectives\n */" },
#endif
		{ "IncludePath", "RTSCombatBehaviorTree.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Return fire task for defensive combat\nAutomatically returns fire while maintaining primary objectives" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnFireRange_MetaData[] = {
		{ "Category", "Return Fire" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Return fire parameters\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Return fire parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnFireDuration_MetaData[] = {
		{ "Category", "Return Fire" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPrioritizeAttackers_MetaData[] = {
		{ "Category", "Return Fire" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bInterruptMovement_MetaData[] = {
		{ "Category", "Return Fire" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackerKey_MetaData[] = {
		{ "Category", "Return Fire" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnFireTargetKey_MetaData[] = {
		{ "Category", "Return Fire" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnFireRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnFireDuration;
	static void NewProp_bPrioritizeAttackers_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPrioritizeAttackers;
	static void NewProp_bInterruptMovement_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bInterruptMovement;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AttackerKey;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnFireTargetKey;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSReturnFireTask>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSReturnFireTask_Statics::NewProp_ReturnFireRange = { "ReturnFireRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSReturnFireTask, ReturnFireRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnFireRange_MetaData), NewProp_ReturnFireRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSReturnFireTask_Statics::NewProp_ReturnFireDuration = { "ReturnFireDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSReturnFireTask, ReturnFireDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnFireDuration_MetaData), NewProp_ReturnFireDuration_MetaData) };
void Z_Construct_UClass_URTSReturnFireTask_Statics::NewProp_bPrioritizeAttackers_SetBit(void* Obj)
{
	((URTSReturnFireTask*)Obj)->bPrioritizeAttackers = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSReturnFireTask_Statics::NewProp_bPrioritizeAttackers = { "bPrioritizeAttackers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSReturnFireTask), &Z_Construct_UClass_URTSReturnFireTask_Statics::NewProp_bPrioritizeAttackers_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPrioritizeAttackers_MetaData), NewProp_bPrioritizeAttackers_MetaData) };
void Z_Construct_UClass_URTSReturnFireTask_Statics::NewProp_bInterruptMovement_SetBit(void* Obj)
{
	((URTSReturnFireTask*)Obj)->bInterruptMovement = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSReturnFireTask_Statics::NewProp_bInterruptMovement = { "bInterruptMovement", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSReturnFireTask), &Z_Construct_UClass_URTSReturnFireTask_Statics::NewProp_bInterruptMovement_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bInterruptMovement_MetaData), NewProp_bInterruptMovement_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_URTSReturnFireTask_Statics::NewProp_AttackerKey = { "AttackerKey", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSReturnFireTask, AttackerKey), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackerKey_MetaData), NewProp_AttackerKey_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_URTSReturnFireTask_Statics::NewProp_ReturnFireTargetKey = { "ReturnFireTargetKey", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSReturnFireTask, ReturnFireTargetKey), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnFireTargetKey_MetaData), NewProp_ReturnFireTargetKey_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSReturnFireTask_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSReturnFireTask_Statics::NewProp_ReturnFireRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSReturnFireTask_Statics::NewProp_ReturnFireDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSReturnFireTask_Statics::NewProp_bPrioritizeAttackers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSReturnFireTask_Statics::NewProp_bInterruptMovement,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSReturnFireTask_Statics::NewProp_AttackerKey,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSReturnFireTask_Statics::NewProp_ReturnFireTargetKey,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSReturnFireTask_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSReturnFireTask_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_URTSTaskNode,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSReturnFireTask_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSReturnFireTask_Statics::ClassParams = {
	&URTSReturnFireTask::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_URTSReturnFireTask_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSReturnFireTask_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSReturnFireTask_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSReturnFireTask_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSReturnFireTask()
{
	if (!Z_Registration_Info_UClass_URTSReturnFireTask.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSReturnFireTask.OuterSingleton, Z_Construct_UClass_URTSReturnFireTask_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSReturnFireTask.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSReturnFireTask);
URTSReturnFireTask::~URTSReturnFireTask() {}
// ********** End Class URTSReturnFireTask *********************************************************

// ********** Begin Class URTSUnderAttackCondition *************************************************
void URTSUnderAttackCondition::StaticRegisterNativesURTSUnderAttackCondition()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSUnderAttackCondition;
UClass* URTSUnderAttackCondition::GetPrivateStaticClass()
{
	using TClass = URTSUnderAttackCondition;
	if (!Z_Registration_Info_UClass_URTSUnderAttackCondition.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSUnderAttackCondition"),
			Z_Registration_Info_UClass_URTSUnderAttackCondition.InnerSingleton,
			StaticRegisterNativesURTSUnderAttackCondition,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSUnderAttackCondition.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSUnderAttackCondition_NoRegister()
{
	return URTSUnderAttackCondition::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSUnderAttackCondition_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Condition node for checking if unit is under attack\n * Used to trigger defensive behaviors\n */" },
#endif
		{ "IncludePath", "RTSCombatBehaviorTree.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Condition node for checking if unit is under attack\nUsed to trigger defensive behaviors" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackDetectionTime_MetaData[] = {
		{ "Category", "Attack Detection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Attack detection parameters\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Attack detection parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bStoreAttacker_MetaData[] = {
		{ "Category", "Attack Detection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// How long to remember being attacked\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "How long to remember being attacked" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackerKey_MetaData[] = {
		{ "Category", "Attack Detection" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AttackDetectionTime;
	static void NewProp_bStoreAttacker_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bStoreAttacker;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AttackerKey;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSUnderAttackCondition>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSUnderAttackCondition_Statics::NewProp_AttackDetectionTime = { "AttackDetectionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSUnderAttackCondition, AttackDetectionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackDetectionTime_MetaData), NewProp_AttackDetectionTime_MetaData) };
void Z_Construct_UClass_URTSUnderAttackCondition_Statics::NewProp_bStoreAttacker_SetBit(void* Obj)
{
	((URTSUnderAttackCondition*)Obj)->bStoreAttacker = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSUnderAttackCondition_Statics::NewProp_bStoreAttacker = { "bStoreAttacker", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSUnderAttackCondition), &Z_Construct_UClass_URTSUnderAttackCondition_Statics::NewProp_bStoreAttacker_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bStoreAttacker_MetaData), NewProp_bStoreAttacker_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_URTSUnderAttackCondition_Statics::NewProp_AttackerKey = { "AttackerKey", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSUnderAttackCondition, AttackerKey), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackerKey_MetaData), NewProp_AttackerKey_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSUnderAttackCondition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSUnderAttackCondition_Statics::NewProp_AttackDetectionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSUnderAttackCondition_Statics::NewProp_bStoreAttacker,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSUnderAttackCondition_Statics::NewProp_AttackerKey,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSUnderAttackCondition_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSUnderAttackCondition_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_URTSConditionNode,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSUnderAttackCondition_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSUnderAttackCondition_Statics::ClassParams = {
	&URTSUnderAttackCondition::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_URTSUnderAttackCondition_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSUnderAttackCondition_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSUnderAttackCondition_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSUnderAttackCondition_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSUnderAttackCondition()
{
	if (!Z_Registration_Info_UClass_URTSUnderAttackCondition.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSUnderAttackCondition.OuterSingleton, Z_Construct_UClass_URTSUnderAttackCondition_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSUnderAttackCondition.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSUnderAttackCondition);
URTSUnderAttackCondition::~URTSUnderAttackCondition() {}
// ********** End Class URTSUnderAttackCondition ***************************************************

// ********** Begin Class URTSTacticalAdvantageCondition *******************************************
void URTSTacticalAdvantageCondition::StaticRegisterNativesURTSTacticalAdvantageCondition()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSTacticalAdvantageCondition;
UClass* URTSTacticalAdvantageCondition::GetPrivateStaticClass()
{
	using TClass = URTSTacticalAdvantageCondition;
	if (!Z_Registration_Info_UClass_URTSTacticalAdvantageCondition.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSTacticalAdvantageCondition"),
			Z_Registration_Info_UClass_URTSTacticalAdvantageCondition.InnerSingleton,
			StaticRegisterNativesURTSTacticalAdvantageCondition,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSTacticalAdvantageCondition.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSTacticalAdvantageCondition_NoRegister()
{
	return URTSTacticalAdvantageCondition::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSTacticalAdvantageCondition_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Condition node for checking if unit has tactical advantage\n * Considers positioning, numbers, and unit capabilities\n */" },
#endif
		{ "IncludePath", "RTSCombatBehaviorTree.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Condition node for checking if unit has tactical advantage\nConsiders positioning, numbers, and unit capabilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AnalysisRadius_MetaData[] = {
		{ "Category", "Tactical Advantage" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Advantage calculation parameters\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Advantage calculation parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NumbersAdvantageWeight_MetaData[] = {
		{ "Category", "Tactical Advantage" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealthAdvantageWeight_MetaData[] = {
		{ "Category", "Tactical Advantage" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RangeAdvantageWeight_MetaData[] = {
		{ "Category", "Tactical Advantage" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinAdvantageThreshold_MetaData[] = {
		{ "Category", "Tactical Advantage" },
		{ "ModuleRelativePath", "Public/RTSCombatBehaviorTree.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AnalysisRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NumbersAdvantageWeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HealthAdvantageWeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RangeAdvantageWeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinAdvantageThreshold;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSTacticalAdvantageCondition>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSTacticalAdvantageCondition_Statics::NewProp_AnalysisRadius = { "AnalysisRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSTacticalAdvantageCondition, AnalysisRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AnalysisRadius_MetaData), NewProp_AnalysisRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSTacticalAdvantageCondition_Statics::NewProp_NumbersAdvantageWeight = { "NumbersAdvantageWeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSTacticalAdvantageCondition, NumbersAdvantageWeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NumbersAdvantageWeight_MetaData), NewProp_NumbersAdvantageWeight_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSTacticalAdvantageCondition_Statics::NewProp_HealthAdvantageWeight = { "HealthAdvantageWeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSTacticalAdvantageCondition, HealthAdvantageWeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealthAdvantageWeight_MetaData), NewProp_HealthAdvantageWeight_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSTacticalAdvantageCondition_Statics::NewProp_RangeAdvantageWeight = { "RangeAdvantageWeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSTacticalAdvantageCondition, RangeAdvantageWeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RangeAdvantageWeight_MetaData), NewProp_RangeAdvantageWeight_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSTacticalAdvantageCondition_Statics::NewProp_MinAdvantageThreshold = { "MinAdvantageThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSTacticalAdvantageCondition, MinAdvantageThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinAdvantageThreshold_MetaData), NewProp_MinAdvantageThreshold_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSTacticalAdvantageCondition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSTacticalAdvantageCondition_Statics::NewProp_AnalysisRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSTacticalAdvantageCondition_Statics::NewProp_NumbersAdvantageWeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSTacticalAdvantageCondition_Statics::NewProp_HealthAdvantageWeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSTacticalAdvantageCondition_Statics::NewProp_RangeAdvantageWeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSTacticalAdvantageCondition_Statics::NewProp_MinAdvantageThreshold,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSTacticalAdvantageCondition_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSTacticalAdvantageCondition_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_URTSConditionNode,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSTacticalAdvantageCondition_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSTacticalAdvantageCondition_Statics::ClassParams = {
	&URTSTacticalAdvantageCondition::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_URTSTacticalAdvantageCondition_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSTacticalAdvantageCondition_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSTacticalAdvantageCondition_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSTacticalAdvantageCondition_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSTacticalAdvantageCondition()
{
	if (!Z_Registration_Info_UClass_URTSTacticalAdvantageCondition.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSTacticalAdvantageCondition.OuterSingleton, Z_Construct_UClass_URTSTacticalAdvantageCondition_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSTacticalAdvantageCondition.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSTacticalAdvantageCondition);
URTSTacticalAdvantageCondition::~URTSTacticalAdvantageCondition() {}
// ********** End Class URTSTacticalAdvantageCondition *********************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h__Script_ArmorWars_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_URTSCombatBehaviorTreeFactory, URTSCombatBehaviorTreeFactory::StaticClass, TEXT("URTSCombatBehaviorTreeFactory"), &Z_Registration_Info_UClass_URTSCombatBehaviorTreeFactory, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSCombatBehaviorTreeFactory), 750060397U) },
		{ Z_Construct_UClass_URTSAdvancedTargetSelectionTask, URTSAdvancedTargetSelectionTask::StaticClass, TEXT("URTSAdvancedTargetSelectionTask"), &Z_Registration_Info_UClass_URTSAdvancedTargetSelectionTask, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSAdvancedTargetSelectionTask), 421208437U) },
		{ Z_Construct_UClass_URTSTacticalPositioningTask, URTSTacticalPositioningTask::StaticClass, TEXT("URTSTacticalPositioningTask"), &Z_Registration_Info_UClass_URTSTacticalPositioningTask, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSTacticalPositioningTask), 3895185494U) },
		{ Z_Construct_UClass_URTSCoordinatedAttackTask, URTSCoordinatedAttackTask::StaticClass, TEXT("URTSCoordinatedAttackTask"), &Z_Registration_Info_UClass_URTSCoordinatedAttackTask, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSCoordinatedAttackTask), 223352020U) },
		{ Z_Construct_UClass_URTSReturnFireTask, URTSReturnFireTask::StaticClass, TEXT("URTSReturnFireTask"), &Z_Registration_Info_UClass_URTSReturnFireTask, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSReturnFireTask), 135578905U) },
		{ Z_Construct_UClass_URTSUnderAttackCondition, URTSUnderAttackCondition::StaticClass, TEXT("URTSUnderAttackCondition"), &Z_Registration_Info_UClass_URTSUnderAttackCondition, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSUnderAttackCondition), 3976337787U) },
		{ Z_Construct_UClass_URTSTacticalAdvantageCondition, URTSTacticalAdvantageCondition::StaticClass, TEXT("URTSTacticalAdvantageCondition"), &Z_Registration_Info_UClass_URTSTacticalAdvantageCondition, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSTacticalAdvantageCondition), 2557908685U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h__Script_ArmorWars_137932706(TEXT("/Script/ArmorWars"),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h__Script_ArmorWars_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h__Script_ArmorWars_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
