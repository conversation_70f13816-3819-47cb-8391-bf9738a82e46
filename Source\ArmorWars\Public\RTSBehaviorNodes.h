#pragma once

#include "CoreMinimal.h"
#include "RTSBehaviorNode.h"
#include "RTSCommand.h"
#include "RTSBehaviorNodes.generated.h"

class ARTSBaseActor;
class ARTSUnit;
class URTSCommandComponent;
class URTSTeamManager;

/**
 * Task node for moving to a location
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API URTSMoveToLocationTask : public URTSTaskNode
{
    GENERATED_BODY()

public:
    URTSMoveToLocationTask();

    virtual ERTSBehaviorNodeStatus ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime) override;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Move Task")
    FString TargetLocationKey = TEXT("TargetLocation");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Move Task")
    float AcceptanceRadius = 100.0f;

protected:
    bool bMovementStarted = false;
};

/**
 * Task node for attacking a target
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API URTSAttackTargetTask : public URTSTaskNode
{
    GENERATED_BODY()

public:
    URTSAttackTargetTask();

    virtual ERTSBehaviorNodeStatus ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime) override;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack Task")
    FString TargetKey = TEXT("Target");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack Task")
    float AttackDuration = 3.0f; // How long to attack before considering success

protected:
    float AttackStartTime = 0.0f;
};

/**
 * Task node for patrolling between points
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API URTSPatrolTask : public URTSTaskNode
{
    GENERATED_BODY()

public:
    URTSPatrolTask();

    virtual ERTSBehaviorNodeStatus ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime) override;
    virtual void InitializeNode(URTSBehaviorTreeComponent* BehaviorTreeComponent) override;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Patrol Task")
    TArray<FVector> PatrolPoints;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Patrol Task")
    bool bLoopPatrol = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Patrol Task")
    float WaitTimeAtPoint = 2.0f;

protected:
    int32 CurrentPatrolIndex = 0;
    float ArrivalTime = 0.0f;
    bool bWaitingAtPoint = false;
};

/**
 * Task node for finding and engaging enemies
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API URTSFindEnemyTask : public URTSTaskNode
{
    GENERATED_BODY()

public:
    URTSFindEnemyTask();

    virtual ERTSBehaviorNodeStatus ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime) override;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Find Enemy Task")
    float SearchRadius = 1500.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Find Enemy Task")
    FString FoundEnemyKey = TEXT("Target");
};

/**
 * Condition node for checking if health is low
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API URTSHealthLowCondition : public URTSConditionNode
{
    GENERATED_BODY()

public:
    URTSHealthLowCondition();

    virtual ERTSBehaviorNodeStatus ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime) override;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Health Condition")
    float HealthThreshold = 0.3f; // 30% health
};

/**
 * Condition node for checking if enemies are nearby
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API URTSEnemyNearbyCondition : public URTSConditionNode
{
    GENERATED_BODY()

public:
    URTSEnemyNearbyCondition();

    virtual ERTSBehaviorNodeStatus ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime) override;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Enemy Condition")
    float DetectionRadius = 1000.0f;
};

/**
 * Condition node for checking if a target is valid
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API URTSHasValidTargetCondition : public URTSConditionNode
{
    GENERATED_BODY()

public:
    URTSHasValidTargetCondition();

    virtual ERTSBehaviorNodeStatus ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime) override;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target Condition")
    FString TargetKey = TEXT("Target");
};

/**
 * Decorator node that inverts the result of its child
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API URTSInverterDecorator : public URTSDecoratorNode
{
    GENERATED_BODY()

public:
    URTSInverterDecorator();

    virtual ERTSBehaviorNodeStatus ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime) override;
};

/**
 * Decorator node that repeats its child a certain number of times
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API URTSRepeaterDecorator : public URTSDecoratorNode
{
    GENERATED_BODY()

public:
    URTSRepeaterDecorator();

    virtual ERTSBehaviorNodeStatus ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime) override;
    virtual void InitializeNode(URTSBehaviorTreeComponent* BehaviorTreeComponent) override;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Repeater")
    int32 RepeatCount = -1; // -1 for infinite

protected:
    int32 CurrentRepeatCount = 0;
};

/**
 * Decorator node that adds a cooldown to its child
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API URTSCooldownDecorator : public URTSDecoratorNode
{
    GENERATED_BODY()

public:
    URTSCooldownDecorator();

    virtual ERTSBehaviorNodeStatus ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime) override;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cooldown")
    float CooldownTime = 5.0f;

protected:
    float LastExecutionTime = 0.0f;
};

// ===== ENHANCED COMMAND-INTEGRATED BEHAVIOR TREE NODES =====

/**
 * Task node for executing player/AI commands
 * Highest priority task that overrides autonomous behavior
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API URTSExecuteCommandTaskNode : public URTSTaskNode
{
    GENERATED_BODY()

public:
    URTSExecuteCommandTaskNode();

    virtual ERTSBehaviorNodeStatus ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime) override;
    virtual void InitializeNode(URTSBehaviorTreeComponent* BehaviorTreeComponent) override;

protected:
    // Command execution parameters
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Commands")
    ERTSCommandPriority MinimumPriority = ERTSCommandPriority::Normal;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Commands")
    bool bAllowCommandInterruption = true;

    // Internal state
    FRTSCommand CurrentCommand;
    bool bExecutingCommand = false;
};

/**
 * Enhanced move task that integrates with formation system
 * Supports formation movement and return fire while moving
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API URTSEnhancedMoveTask : public URTSTaskNode
{
    GENERATED_BODY()

public:
    URTSEnhancedMoveTask();

    virtual ERTSBehaviorNodeStatus ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime) override;
    virtual void InitializeNode(URTSBehaviorTreeComponent* BehaviorTreeComponent) override;

    // Movement parameters
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    FString TargetLocationKey = TEXT("TargetLocation");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    float AcceptanceRadius = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    bool bUseFormationMovement = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    bool bReturnFireWhileMoving = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    bool bStopOnEnemyContact = false;

protected:
    // Internal state
    bool bMovementStarted = false;
    FVector LastTargetLocation = FVector::ZeroVector;
    float LastReturnFireTime = 0.0f;
};

/**
 * Enhanced combat task with stat integration
 * Uses unit stats for range, damage, and tactical decisions
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API URTSEnhancedCombatTask : public URTSTaskNode
{
    GENERATED_BODY()

public:
    URTSEnhancedCombatTask();

    virtual ERTSBehaviorNodeStatus ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime) override;
    virtual void InitializeNode(URTSBehaviorTreeComponent* BehaviorTreeComponent) override;

    // Combat parameters
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
    FString TargetActorKey = TEXT("TargetActor");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
    bool bAutoSelectTargets = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
    bool bUseUnitWeaponRange = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
    float MaxEngagementRange = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
    bool bPursueTargets = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
    float TargetSwitchCooldown = 2.0f;

protected:
    // Internal state
    float LastTargetSwitchTime = 0.0f;
    TWeakObjectPtr<ARTSBaseActor> CurrentTarget;
};

/**
 * Task node for following formation leader
 * Maintains formation position relative to leader
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API URTSFollowFormationTask : public URTSTaskNode
{
    GENERATED_BODY()

public:
    URTSFollowFormationTask();

    virtual ERTSBehaviorNodeStatus ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime) override;
    virtual void InitializeNode(URTSBehaviorTreeComponent* BehaviorTreeComponent) override;

protected:
    // Formation parameters
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation")
    FString FormationLeaderKey = TEXT("FormationLeader");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation")
    FString FormationOffsetKey = TEXT("FormationOffset");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation")
    float FormationTolerance = 50.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation")
    float MaxFormationDistance = 500.0f;

    // Internal state
    FVector LastFormationPosition = FVector::ZeroVector;
};

/**
 * Condition node for checking if unit has commands
 * Used to prioritize command execution over autonomous behavior
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API URTSHasCommandsCondition : public URTSConditionNode
{
    GENERATED_BODY()

public:
    URTSHasCommandsCondition();

    virtual ERTSBehaviorNodeStatus ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime) override;

    // Command checking parameters
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Commands")
    ERTSCommandPriority MinimumPriority = ERTSCommandPriority::Normal;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Commands")
    bool bCheckQueuedCommands = true;
};

/**
 * Enhanced enemies in range condition with team integration
 * Uses team system to identify enemies and unit stats for range
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API URTSEnemiesInRangeCondition : public URTSConditionNode
{
    GENERATED_BODY()

public:
    URTSEnemiesInRangeCondition();

    virtual ERTSBehaviorNodeStatus ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime) override;

    // Range checking parameters
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
    float DetectionRange = 800.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
    bool bUseWeaponRange = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
    bool bStoreNearestEnemy = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
    FString NearestEnemyKey = TEXT("NearestEnemy");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
    FString EnemyCountKey = TEXT("EnemyCount");
};

/**
 * Condition node for checking if unit is in formation
 * Used for formation behavior decisions
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API URTSInFormationCondition : public URTSConditionNode
{
    GENERATED_BODY()

public:
    URTSInFormationCondition();

    virtual ERTSBehaviorNodeStatus ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime) override;

protected:
    // Formation checking parameters
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation")
    bool bCheckFormationLeaderValid = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation")
    bool bCheckFormationDistance = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation")
    float MaxFormationDistance = 1000.0f;
};

/**
 * Decorator node that interrupts child execution when commands are received
 * Ensures command responsiveness
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API URTSCommandInterruptDecorator : public URTSDecoratorNode
{
    GENERATED_BODY()

public:
    URTSCommandInterruptDecorator();

    virtual ERTSBehaviorNodeStatus ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime) override;

    // Interrupt parameters
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Commands")
    ERTSCommandPriority MinimumInterruptPriority = ERTSCommandPriority::High;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Commands")
    float CheckInterval = 0.1f; // How often to check for commands

protected:
    // Internal state
    float LastCheckTime = 0.0f;
};

/**
 * Health condition node for checking unit health status
 * Used to trigger health-based behaviors like retreat
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API URTSHealthCondition : public URTSConditionNode
{
    GENERATED_BODY()

public:
    URTSHealthCondition();

    virtual ERTSBehaviorNodeStatus ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime) override;

    // Health checking parameters
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Health")
    float HealthThreshold = 0.3f; // Health percentage threshold (0.0 to 1.0)

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Health")
    bool bCheckLowHealth = true; // If true, returns success when health is below threshold

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Health")
    bool bCheckHighHealth = false; // If true, returns success when health is above threshold
};

/**
 * Decorator node that modifies behavior based on unit stats
 * Allows for stat-based AI behavior variations
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API URTSStatBasedDecorator : public URTSDecoratorNode
{
    GENERATED_BODY()

public:
    URTSStatBasedDecorator();

    virtual ERTSBehaviorNodeStatus ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime) override;

protected:
    // Stat checking parameters
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
    bool bCheckMovementSpeed = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
    float MinMovementSpeed = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
    bool bCheckAttackRange = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
    float MinAttackRange = 200.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
    bool bCheckHealth = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
    float MinHealthPercentage = 0.5f;
};
