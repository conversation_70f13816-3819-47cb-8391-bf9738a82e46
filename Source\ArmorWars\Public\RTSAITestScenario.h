#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "RTSAITestScenario.generated.h"

class ARTSUnit;
class ARTSAIController;
class ARTSGroupManager;
class ARTSBaseActor;

UENUM(BlueprintType)
enum class ERTSTestScenarioType : uint8
{
    MovementTest        UMETA(DisplayName = "Movement Test"),
    CombatTest          UMETA(DisplayName = "Combat Test"),
    FormationTest       UMETA(DisplayName = "Formation Test"),
    BehaviorTreeTest    UMETA(DisplayName = "Behavior Tree Test"),
    GroupCoordination   UMETA(DisplayName = "Group Coordination"),
    PathfindingTest     UMETA(DisplayName = "Pathfinding Test"),
    ObstacleAvoidance   UMETA(DisplayName = "Obstacle Avoidance"),
    StressTest          UMETA(DisplayName = "Stress Test")
};

/**
 * Test scenario class for verifying RTS AI functionality
 * Creates controlled test environments to validate AI behaviors
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API ARTSAITestScenario : public AActor
{
    GENERATED_BODY()

public:
    ARTSAITestScenario();

protected:
    virtual void BeginPlay() override;

public:
    virtual void Tick(float DeltaTime) override;

    // Test Management
    UFUNCTION(BlueprintCallable, Category = "AI Test")
    void StartTest();

    UFUNCTION(BlueprintCallable, Category = "AI Test")
    void StopTest();

    UFUNCTION(BlueprintCallable, Category = "AI Test")
    void ResetTest();

    UFUNCTION(BlueprintPure, Category = "AI Test")
    bool IsTestRunning() const { return bTestRunning; }

    UFUNCTION(BlueprintPure, Category = "AI Test")
    float GetTestProgress() const;

    UFUNCTION(BlueprintCallable, Category = "AI Test")
    void LogTestResults();

    // Test Configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Setup")
    ERTSTestScenarioType TestType = ERTSTestScenarioType::MovementTest;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Setup")
    int32 NumberOfUnits = 5;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Setup")
    float TestDuration = 60.0f; // Test duration in seconds

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Setup")
    bool bAutoStartTest = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Setup")
    bool bEnableDetailedLogging = true;

    // Test Results
    UPROPERTY(BlueprintReadOnly, Category = "Test Results")
    bool bTestPassed = false;

    UPROPERTY(BlueprintReadOnly, Category = "Test Results")
    FString TestResults;

    UPROPERTY(BlueprintReadOnly, Category = "Test Results")
    TArray<FString> TestLog;

protected:
    // Test execution
    virtual void ExecuteTest(float DeltaTime);
    virtual void ExecuteMovementTest(float DeltaTime);
    virtual void ExecuteCombatTest(float DeltaTime);
    virtual void ExecuteFormationTest(float DeltaTime);
    virtual void ExecuteBehaviorTreeTest(float DeltaTime);
    virtual void ExecuteGroupCoordinationTest(float DeltaTime);
    virtual void ExecutePathfindingTest(float DeltaTime);
    virtual void ExecuteObstacleAvoidanceTest(float DeltaTime);
    virtual void ExecuteStressTest(float DeltaTime);

    // Test setup helpers
    virtual void SetupTestUnits();
    virtual void SetupTestEnvironment();
    virtual void CleanupTest();

    // Validation helpers
    virtual bool ValidateMovementBehavior();
    virtual bool ValidateCombatBehavior();
    virtual bool ValidateFormationBehavior();
    virtual bool ValidateBehaviorTreeExecution();
    virtual bool ValidateGroupCoordination();
    virtual bool ValidatePathfinding();
    virtual bool ValidateObstacleAvoidance();

    // Utility functions
    virtual void LogMessage(const FString& Message);
    virtual void SpawnTestUnit(const FVector& Location, int32 TeamID = 0);
    virtual void SpawnTestEnemy(const FVector& Location, int32 TeamID = 1);
    virtual void CreateTestObstacles();

    // Test state
    bool bTestRunning = false;
    float TestStartTime = 0.0f;
    float CurrentTestTime = 0.0f;

    // Test objects
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Units")
    TArray<ARTSUnit*> TestUnits;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Units")
    TArray<ARTSUnit*> TestEnemies;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Environment")
    TArray<AActor*> TestObstacles;

    UPROPERTY()
    ARTSGroupManager* TestGroupManager;

    // Test parameters
    TArray<FVector> TestWaypoints;
    FVector TestTargetLocation = FVector::ZeroVector;
    int32 CurrentTestPhase = 0;
    int32 MaxTestPhases = 1;

    // Performance metrics
    int32 SuccessfulMovements = 0;
    int32 FailedMovements = 0;
    int32 SuccessfulAttacks = 0;
    int32 FailedAttacks = 0;
    float AverageResponseTime = 0.0f;
    TArray<float> ResponseTimes;

public:
    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnTestStarted, ERTSTestScenarioType, TestType);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTestCompleted, ERTSTestScenarioType, TestType, bool, bPassed);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTestPhaseChanged, int32, CurrentPhase, int32, MaxPhases);

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnTestStarted OnTestStarted;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnTestCompleted OnTestCompleted;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnTestPhaseChanged OnTestPhaseChanged;
};
