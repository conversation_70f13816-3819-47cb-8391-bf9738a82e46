#include "RTSMovementBehaviorTree.h"
#include "RTSBehaviorTreeComponent.h"
#include "RTSUnit.h"
#include "RTSCommandComponent.h"
#include "RTSFormationManager.h"
#include "RTSTeamManager.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Kismet/GameplayStatics.h"

// URTSMovementBehaviorTreeFactory Implementation

URTSMovementBehaviorTreeFactory::URTSMovementBehaviorTreeFactory()
{
}

URTSBehaviorNode* URTSMovementBehaviorTreeFactory::CreateBasicMovementTree()
{
    // Create root selector
    URTSCompositeNode* RootSelector = NewObject<URTSCompositeNode>();
    RootSelector->CompositeType = ERTSCompositeType::Selector;
    RootSelector->NodeName = TEXT("Basic Movement Root");
    
    // Command execution has highest priority
    URTSExecuteCommandTaskNode* CommandTask = NewObject<URTSExecuteCommandTaskNode>();
    RootSelector->AddChildNode(CommandTask);
    
    // Basic movement task
    URTSEnhancedMoveTask* MoveTask = NewObject<URTSEnhancedMoveTask>();
    MoveTask->bUseFormationMovement = false;
    MoveTask->bReturnFireWhileMoving = false;
    RootSelector->AddChildNode(MoveTask);
    
    return RootSelector;
}

URTSBehaviorNode* URTSMovementBehaviorTreeFactory::CreateFormationMovementTree()
{
    // Create root selector
    URTSCompositeNode* RootSelector = NewObject<URTSCompositeNode>();
    RootSelector->CompositeType = ERTSCompositeType::Selector;
    RootSelector->NodeName = TEXT("Formation Movement Root");
    
    // Command execution has highest priority
    RootSelector->AddChildNode(CreateCommandPrioritySelector());
    
    // Formation movement sequence
    RootSelector->AddChildNode(CreateFormationSequence());
    
    // Fallback to basic movement
    URTSEnhancedMoveTask* FallbackMove = NewObject<URTSEnhancedMoveTask>();
    FallbackMove->bUseFormationMovement = false;
    RootSelector->AddChildNode(FallbackMove);
    
    return RootSelector;
}

URTSBehaviorNode* URTSMovementBehaviorTreeFactory::CreateCombatMovementTree()
{
    // Create root selector
    URTSCompositeNode* RootSelector = NewObject<URTSCompositeNode>();
    RootSelector->CompositeType = ERTSCompositeType::Selector;
    RootSelector->NodeName = TEXT("Combat Movement Root");
    
    // Command execution has highest priority
    RootSelector->AddChildNode(CreateCommandPrioritySelector());
    
    // Combat sequence
    RootSelector->AddChildNode(CreateCombatSequence());
    
    // Movement with return fire
    URTSEnhancedMoveTask* CombatMove = NewObject<URTSEnhancedMoveTask>();
    CombatMove->bReturnFireWhileMoving = true;
    CombatMove->bStopOnEnemyContact = false;
    RootSelector->AddChildNode(CombatMove);
    
    return RootSelector;
}

URTSBehaviorNode* URTSMovementBehaviorTreeFactory::CreateAdvancedUnitBehaviorTree()
{
    // Create root selector with command interrupt decorator
    URTSCommandInterruptDecorator* CommandInterrupt = NewObject<URTSCommandInterruptDecorator>();
    CommandInterrupt->MinimumInterruptPriority = ERTSCommandPriority::High;
    
    URTSCompositeNode* RootSelector = NewObject<URTSCompositeNode>();
    RootSelector->CompositeType = ERTSCompositeType::Selector;
    RootSelector->NodeName = TEXT("Advanced Unit Behavior Root");
    
    // 1. Command execution (highest priority)
    RootSelector->AddChildNode(CreateCommandPrioritySelector());
    
    // 2. Combat behavior
    RootSelector->AddChildNode(CreateCombatSequence());
    
    // 3. Formation behavior
    RootSelector->AddChildNode(CreateFormationSequence());
    
    // 4. Movement with collision avoidance
    RootSelector->AddChildNode(CreateCollisionAvoidanceSequence());
    
    // 5. Idle behavior (patrol, hold position, etc.)
    URTSTaskNode* IdleTask = NewObject<URTSTaskNode>();
    IdleTask->NodeName = TEXT("Idle");
    RootSelector->AddChildNode(IdleTask);
    
    CommandInterrupt->AddChildNode(RootSelector);
    return CommandInterrupt;
}

URTSBehaviorNode* URTSMovementBehaviorTreeFactory::CreateCommandResponsiveBehaviorTree()
{
    // Create root parallel node for maximum responsiveness
    URTSCompositeNode* RootParallel = NewObject<URTSCompositeNode>();
    RootParallel->CompositeType = ERTSCompositeType::Parallel;
    RootParallel->NodeName = TEXT("Command Responsive Root");
    
    // Command monitoring branch (always running)
    URTSHasCommandsCondition* CommandCheck = NewObject<URTSHasCommandsCondition>();
    CommandCheck->MinimumPriority = ERTSCommandPriority::Normal;
    RootParallel->AddChildNode(CommandCheck);
    
    // Main behavior branch
    URTSCompositeNode* BehaviorSelector = NewObject<URTSCompositeNode>();
    BehaviorSelector->CompositeType = ERTSCompositeType::Selector;
    BehaviorSelector->NodeName = TEXT("Main Behavior");
    
    // Command execution
    BehaviorSelector->AddChildNode(CreateCommandPrioritySelector());
    
    // Autonomous behavior
    BehaviorSelector->AddChildNode(CreateAdvancedUnitBehaviorTree());
    
    RootParallel->AddChildNode(BehaviorSelector);
    
    return RootParallel;
}

URTSCompositeNode* URTSMovementBehaviorTreeFactory::CreateCommandPrioritySelector()
{
    URTSCompositeNode* CommandSelector = NewObject<URTSCompositeNode>();
    CommandSelector->CompositeType = ERTSCompositeType::Selector;
    CommandSelector->NodeName = TEXT("Command Priority");
    
    // Check if unit has commands
    URTSCompositeNode* CommandSequence = NewObject<URTSCompositeNode>();
    CommandSequence->CompositeType = ERTSCompositeType::Sequence;
    CommandSequence->NodeName = TEXT("Execute Commands");
    
    URTSHasCommandsCondition* HasCommands = NewObject<URTSHasCommandsCondition>();
    HasCommands->MinimumPriority = ERTSCommandPriority::Normal;
    CommandSequence->AddChildNode(HasCommands);
    
    URTSExecuteCommandTaskNode* ExecuteCommand = NewObject<URTSExecuteCommandTaskNode>();
    CommandSequence->AddChildNode(ExecuteCommand);
    
    CommandSelector->AddChildNode(CommandSequence);
    
    return CommandSelector;
}

URTSCompositeNode* URTSMovementBehaviorTreeFactory::CreateMovementSequence()
{
    URTSCompositeNode* MovementSequence = NewObject<URTSCompositeNode>();
    MovementSequence->CompositeType = ERTSCompositeType::Sequence;
    MovementSequence->NodeName = TEXT("Movement");
    
    // Check if path is clear
    URTSPathBlockedCondition* PathBlocked = NewObject<URTSPathBlockedCondition>();
    URTSInverterDecorator* NotBlocked = NewObject<URTSInverterDecorator>();
    NotBlocked->AddChildNode(PathBlocked);
    MovementSequence->AddChildNode(NotBlocked);
    
    // Move with collision avoidance
    URTSCollisionAvoidanceMoveTask* MoveTask = NewObject<URTSCollisionAvoidanceMoveTask>();
    MovementSequence->AddChildNode(MoveTask);
    
    return MovementSequence;
}

URTSCompositeNode* URTSMovementBehaviorTreeFactory::CreateCombatSequence()
{
    URTSCompositeNode* CombatSequence = NewObject<URTSCompositeNode>();
    CombatSequence->CompositeType = ERTSCompositeType::Sequence;
    CombatSequence->NodeName = TEXT("Combat");
    
    // Check if enemies are in range
    URTSEnemiesInRangeCondition* EnemiesInRange = NewObject<URTSEnemiesInRangeCondition>();
    EnemiesInRange->bUseWeaponRange = true;
    EnemiesInRange->bStoreNearestEnemy = true;
    CombatSequence->AddChildNode(EnemiesInRange);
    
    // Engage in combat
    URTSEnhancedCombatTask* CombatTask = NewObject<URTSEnhancedCombatTask>();
    CombatTask->bAutoSelectTargets = true;
    CombatTask->bUseUnitWeaponRange = true;
    CombatSequence->AddChildNode(CombatTask);
    
    return CombatSequence;
}

URTSCompositeNode* URTSMovementBehaviorTreeFactory::CreateFormationSequence()
{
    URTSCompositeNode* FormationSequence = NewObject<URTSCompositeNode>();
    FormationSequence->CompositeType = ERTSCompositeType::Sequence;
    FormationSequence->NodeName = TEXT("Formation");
    
    // Check if unit is in formation
    URTSInFormationCondition* InFormation = NewObject<URTSInFormationCondition>();
    FormationSequence->AddChildNode(InFormation);
    
    // Formation speed decorator
    URTSFormationSpeedDecorator* SpeedDecorator = NewObject<URTSFormationSpeedDecorator>();
    
    // Formation movement selector
    URTSCompositeNode* FormationSelector = NewObject<URTSCompositeNode>();
    FormationSelector->CompositeType = ERTSCompositeType::Selector;
    FormationSelector->NodeName = TEXT("Formation Movement");
    
    // Follow formation leader
    URTSFollowFormationTask* FollowTask = NewObject<URTSFollowFormationTask>();
    FormationSelector->AddChildNode(FollowTask);
    
    // Maintain formation spacing
    URTSMaintainFormationSpacingTask* SpacingTask = NewObject<URTSMaintainFormationSpacingTask>();
    FormationSelector->AddChildNode(SpacingTask);
    
    SpeedDecorator->AddChildNode(FormationSelector);
    FormationSequence->AddChildNode(SpeedDecorator);
    
    return FormationSequence;
}

URTSCompositeNode* URTSMovementBehaviorTreeFactory::CreateCollisionAvoidanceSequence()
{
    URTSCompositeNode* AvoidanceSequence = NewObject<URTSCompositeNode>();
    AvoidanceSequence->CompositeType = ERTSCompositeType::Selector;
    AvoidanceSequence->NodeName = TEXT("Collision Avoidance");
    
    // Try normal movement first
    URTSCompositeNode* NormalMovement = NewObject<URTSCompositeNode>();
    NormalMovement->CompositeType = ERTSCompositeType::Sequence;
    NormalMovement->NodeName = TEXT("Normal Movement");
    
    URTSPathBlockedCondition* PathBlocked = NewObject<URTSPathBlockedCondition>();
    URTSInverterDecorator* NotBlocked = NewObject<URTSInverterDecorator>();
    NotBlocked->AddChildNode(PathBlocked);
    NormalMovement->AddChildNode(NotBlocked);
    
    URTSCollisionAvoidanceMoveTask* NormalMove = NewObject<URTSCollisionAvoidanceMoveTask>();
    NormalMovement->AddChildNode(NormalMove);
    
    AvoidanceSequence->AddChildNode(NormalMovement);
    
    // Alternative path if blocked
    URTSCompositeNode* AlternativeMovement = NewObject<URTSCompositeNode>();
    AlternativeMovement->CompositeType = ERTSCompositeType::Sequence;
    AlternativeMovement->NodeName = TEXT("Alternative Movement");
    
    URTSFindAlternativePathTask* FindPath = NewObject<URTSFindAlternativePathTask>();
    AlternativeMovement->AddChildNode(FindPath);
    
    URTSCollisionAvoidanceMoveTask* AlternativeMove = NewObject<URTSCollisionAvoidanceMoveTask>();
    AlternativeMove->TargetLocationKey = TEXT("AlternativePath");
    AlternativeMovement->AddChildNode(AlternativeMove);
    
    AvoidanceSequence->AddChildNode(AlternativeMovement);
    
    return AvoidanceSequence;
}

// URTSCollisionAvoidanceMoveTask Implementation

URTSCollisionAvoidanceMoveTask::URTSCollisionAvoidanceMoveTask()
{
    NodeType = ERTSBehaviorNodeType::Task;
    NodeName = TEXT("Collision Avoidance Move");
    NodeDescription = TEXT("Movement with collision avoidance using flocking behavior");
    AvoidanceRadius = 150.0f;
    AvoidanceStrength = 1.0f;
    LookAheadDistance = 300.0f;
    MaxAvoidanceUnits = 10;
    TargetLocationKey = TEXT("TargetLocation");
    AcceptanceRadius = 100.0f;
    AvoidanceUpdateInterval = 0.1f;
    LastAvoidanceUpdate = 0.0f;
}

void URTSCollisionAvoidanceMoveTask::InitializeNode(URTSBehaviorTreeComponent* BehaviorTreeComponent)
{
    Super::InitializeNode(BehaviorTreeComponent);
    AvoidanceVector = FVector::ZeroVector;
    NearbyUnits.Empty();
    LastAvoidanceUpdate = 0.0f;
}

ERTSBehaviorNodeStatus URTSCollisionAvoidanceMoveTask::ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime)
{
    if (!BehaviorTreeComponent)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    ARTSUnit* Unit = Cast<ARTSUnit>(GetControlledPawn(BehaviorTreeComponent));
    if (!Unit)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    // Get target location from blackboard
    FVector TargetLocation = BehaviorTreeComponent->GetBlackboardVector(TargetLocationKey);
    if (TargetLocation.IsZero())
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;

    // Update avoidance vector periodically
    if (CurrentTime - LastAvoidanceUpdate >= AvoidanceUpdateInterval)
    {
        AvoidanceVector = CalculateAvoidanceVector(Unit);
        LastAvoidanceUpdate = CurrentTime;
    }

    // Calculate final movement direction
    FVector DirectionToTarget = (TargetLocation - Unit->GetActorLocation()).GetSafeNormal();
    FVector FinalDirection = (DirectionToTarget + AvoidanceVector * AvoidanceStrength).GetSafeNormal();

    // Calculate adjusted target location
    FVector AdjustedTarget = Unit->GetActorLocation() + FinalDirection * FVector::Dist(Unit->GetActorLocation(), TargetLocation);

    // Move towards adjusted target
    Unit->MoveToLocation(AdjustedTarget);

    // Check if we've reached the original target
    float DistanceToTarget = FVector::Dist(Unit->GetActorLocation(), TargetLocation);
    if (DistanceToTarget <= AcceptanceRadius)
    {
        return ERTSBehaviorNodeStatus::Success;
    }

    return ERTSBehaviorNodeStatus::Running;
}

FVector URTSCollisionAvoidanceMoveTask::CalculateAvoidanceVector(ARTSUnit* Unit)
{
    if (!Unit)
    {
        return FVector::ZeroVector;
    }

    TArray<ARTSUnit*> LocalNearbyUnits = FindNearbyUnits(Unit);
    if (LocalNearbyUnits.Num() == 0)
    {
        return FVector::ZeroVector;
    }

    // Calculate flocking forces
    FVector Separation = GetSeparationVector(Unit, LocalNearbyUnits);
    FVector Alignment = GetAlignmentVector(Unit, LocalNearbyUnits);
    FVector Cohesion = GetCohesionVector(Unit, LocalNearbyUnits);

    // Weight the forces (separation is most important for collision avoidance)
    FVector AvoidanceForce = Separation * 3.0f + Alignment * 1.0f + Cohesion * 0.5f;

    return AvoidanceForce.GetSafeNormal();
}

TArray<ARTSUnit*> URTSCollisionAvoidanceMoveTask::FindNearbyUnits(ARTSUnit* Unit)
{
    TArray<ARTSUnit*> LocalNearbyUnits;

    if (!Unit || !Unit->GetWorld())
    {
        return LocalNearbyUnits;
    }

    TArray<AActor*> FoundActors;
    UGameplayStatics::GetAllActorsOfClass(Unit->GetWorld(), ARTSUnit::StaticClass(), FoundActors);

    for (AActor* Actor : FoundActors)
    {
        if (ARTSUnit* OtherUnit = Cast<ARTSUnit>(Actor))
        {
            if (OtherUnit != Unit && OtherUnit->IsAlive())
            {
                float Distance = FVector::Dist(Unit->GetActorLocation(), OtherUnit->GetActorLocation());
                if (Distance <= AvoidanceRadius && LocalNearbyUnits.Num() < MaxAvoidanceUnits)
                {
                    LocalNearbyUnits.Add(OtherUnit);
                }
            }
        }
    }

    return LocalNearbyUnits;
}

FVector URTSCollisionAvoidanceMoveTask::GetSeparationVector(ARTSUnit* Unit, const TArray<ARTSUnit*>& LocalNearbyUnits)
{
    FVector SeparationForce = FVector::ZeroVector;

    for (ARTSUnit* OtherUnit : LocalNearbyUnits)
    {
        if (OtherUnit)
        {
            FVector ToOther = OtherUnit->GetActorLocation() - Unit->GetActorLocation();
            float Distance = ToOther.Size();

            if (Distance > 0.0f && Distance < AvoidanceRadius)
            {
                // Stronger force when closer
                float Force = (AvoidanceRadius - Distance) / AvoidanceRadius;
                SeparationForce -= ToOther.GetSafeNormal() * Force;
            }
        }
    }

    return SeparationForce.GetSafeNormal();
}

FVector URTSCollisionAvoidanceMoveTask::GetAlignmentVector(ARTSUnit* Unit, const TArray<ARTSUnit*>& LocalNearbyUnits)
{
    FVector AverageVelocity = FVector::ZeroVector;
    int32 Count = 0;

    for (ARTSUnit* OtherUnit : LocalNearbyUnits)
    {
        if (OtherUnit)
        {
            AverageVelocity += OtherUnit->GetVelocity();
            Count++;
        }
    }

    if (Count > 0)
    {
        AverageVelocity /= Count;
        return AverageVelocity.GetSafeNormal();
    }

    return FVector::ZeroVector;
}

FVector URTSCollisionAvoidanceMoveTask::GetCohesionVector(ARTSUnit* Unit, const TArray<ARTSUnit*>& LocalNearbyUnits)
{
    FVector CenterOfMass = FVector::ZeroVector;
    int32 Count = 0;

    for (ARTSUnit* OtherUnit : LocalNearbyUnits)
    {
        if (OtherUnit)
        {
            CenterOfMass += OtherUnit->GetActorLocation();
            Count++;
        }
    }

    if (Count > 0)
    {
        CenterOfMass /= Count;
        return (CenterOfMass - Unit->GetActorLocation()).GetSafeNormal();
    }

    return FVector::ZeroVector;
}

// URTSPathBlockedCondition Implementation

URTSPathBlockedCondition::URTSPathBlockedCondition()
{
    NodeType = ERTSBehaviorNodeType::Condition;
    NodeName = TEXT("Path Blocked");
    NodeDescription = TEXT("Checks if the path to target is blocked");
    CheckDistance = 200.0f;
    CheckWidth = 100.0f;
    CheckPoints = 5;
    TargetLocationKey = TEXT("TargetLocation");
}

ERTSBehaviorNodeStatus URTSPathBlockedCondition::ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime)
{
    if (!BehaviorTreeComponent)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    ARTSUnit* Unit = Cast<ARTSUnit>(GetControlledPawn(BehaviorTreeComponent));
    if (!Unit)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    FVector TargetLocation = BehaviorTreeComponent->GetBlackboardVector(TargetLocationKey);
    if (TargetLocation.IsZero())
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    FVector StartLocation = Unit->GetActorLocation();
    FVector DirectionToTarget = (TargetLocation - StartLocation).GetSafeNormal();

    // Check multiple points along the path
    for (int32 i = 1; i <= CheckPoints; i++)
    {
        float CheckDistanceStep = (CheckDistance / CheckPoints) * i;
        FVector CheckLocation = StartLocation + DirectionToTarget * CheckDistanceStep;

        // Check for obstacles at this point
        TArray<AActor*> FoundActors;
        UGameplayStatics::GetAllActorsOfClass(Unit->GetWorld(), ARTSUnit::StaticClass(), FoundActors);

        for (AActor* Actor : FoundActors)
        {
            if (ARTSUnit* OtherUnit = Cast<ARTSUnit>(Actor))
            {
                if (OtherUnit != Unit && OtherUnit->IsAlive())
                {
                    float Distance = FVector::Dist(CheckLocation, OtherUnit->GetActorLocation());
                    if (Distance <= CheckWidth)
                    {
                        return ERTSBehaviorNodeStatus::Success; // Path is blocked
                    }
                }
            }
        }
    }

    return ERTSBehaviorNodeStatus::Failure; // Path is clear
}

// URTSFindAlternativePathTask Implementation

URTSFindAlternativePathTask::URTSFindAlternativePathTask()
{
    NodeType = ERTSBehaviorNodeType::Task;
    NodeName = TEXT("Find Alternative Path");
    NodeDescription = TEXT("Finds alternative path when direct path is blocked");
    SearchRadius = 300.0f;
    SearchAngles = 8;
    MinClearDistance = 150.0f;
    TargetLocationKey = TEXT("TargetLocation");
    AlternativePathKey = TEXT("AlternativePath");
    CurrentPathIndex = 0;
}

void URTSFindAlternativePathTask::InitializeNode(URTSBehaviorTreeComponent* BehaviorTreeComponent)
{
    Super::InitializeNode(BehaviorTreeComponent);
    CurrentPath.Empty();
    CurrentPathIndex = 0;
}

ERTSBehaviorNodeStatus URTSFindAlternativePathTask::ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime)
{
    if (!BehaviorTreeComponent)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    ARTSUnit* Unit = Cast<ARTSUnit>(GetControlledPawn(BehaviorTreeComponent));
    if (!Unit)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    FVector TargetLocation = BehaviorTreeComponent->GetBlackboardVector(TargetLocationKey);
    if (TargetLocation.IsZero())
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    // Find alternative path
    if (CurrentPath.Num() == 0)
    {
        CurrentPath = FindPath(Unit, Unit->GetActorLocation(), TargetLocation);
        CurrentPathIndex = 0;
    }

    if (CurrentPath.Num() == 0)
    {
        return ERTSBehaviorNodeStatus::Failure; // No path found
    }

    // Set next waypoint in blackboard
    if (CurrentPathIndex < CurrentPath.Num())
    {
        BehaviorTreeComponent->SetBlackboardVector(AlternativePathKey, CurrentPath[CurrentPathIndex]);
        return ERTSBehaviorNodeStatus::Success;
    }

    return ERTSBehaviorNodeStatus::Failure;
}

TArray<FVector> URTSFindAlternativePathTask::FindPath(ARTSUnit* Unit, const FVector& Start, const FVector& End)
{
    TArray<FVector> Path;

    if (!Unit)
    {
        return Path;
    }

    FVector CurrentPos = Start;
    FVector DirectionToEnd = (End - Start).GetSafeNormal();

    // Simple pathfinding: try to find clear directions
    for (int32 Step = 0; Step < 5; Step++) // Max 5 waypoints
    {
        FVector ClearDirection = FindClearDirection(Unit, DirectionToEnd);
        if (ClearDirection.IsZero())
        {
            break; // No clear direction found
        }

        FVector NextWaypoint = CurrentPos + ClearDirection * SearchRadius;
        Path.Add(NextWaypoint);
        CurrentPos = NextWaypoint;

        // Check if we're close enough to the end
        if (FVector::Dist(CurrentPos, End) <= SearchRadius)
        {
            Path.Add(End);
            break;
        }

        // Update direction to end
        DirectionToEnd = (End - CurrentPos).GetSafeNormal();
    }

    return Path;
}

bool URTSFindAlternativePathTask::IsPathClear(ARTSUnit* Unit, const FVector& Start, const FVector& End)
{
    if (!Unit || !Unit->GetWorld())
    {
        return false;
    }

    FVector Direction = (End - Start).GetSafeNormal();
    float Distance = FVector::Dist(Start, End);

    // Check for obstacles along the path
    TArray<AActor*> FoundActors;
    UGameplayStatics::GetAllActorsOfClass(Unit->GetWorld(), ARTSUnit::StaticClass(), FoundActors);

    for (AActor* Actor : FoundActors)
    {
        if (ARTSUnit* OtherUnit = Cast<ARTSUnit>(Actor))
        {
            if (OtherUnit != Unit && OtherUnit->IsAlive())
            {
                FVector ToOther = OtherUnit->GetActorLocation() - Start;
                float ProjectedDistance = FVector::DotProduct(ToOther, Direction);

                if (ProjectedDistance > 0.0f && ProjectedDistance < Distance)
                {
                    FVector ProjectedPoint = Start + Direction * ProjectedDistance;
                    float DistanceToPath = FVector::Dist(OtherUnit->GetActorLocation(), ProjectedPoint);

                    if (DistanceToPath < MinClearDistance)
                    {
                        return false; // Path is blocked
                    }
                }
            }
        }
    }

    return true; // Path is clear
}

FVector URTSFindAlternativePathTask::FindClearDirection(ARTSUnit* Unit, const FVector& PreferredDirection)
{
    if (!Unit)
    {
        return FVector::ZeroVector;
    }

    // Try the preferred direction first
    FVector TestEnd = Unit->GetActorLocation() + PreferredDirection * SearchRadius;
    if (IsPathClear(Unit, Unit->GetActorLocation(), TestEnd))
    {
        return PreferredDirection;
    }

    // Try alternative directions
    float AngleStep = 360.0f / SearchAngles;
    FVector BaseDirection = PreferredDirection;

    for (int32 i = 1; i < SearchAngles; i++)
    {
        float Angle = AngleStep * i;

        // Try both positive and negative angles
        for (int32 Sign = -1; Sign <= 1; Sign += 2)
        {
            FVector TestDirection = BaseDirection.RotateAngleAxis(Angle * Sign, FVector::UpVector);
            TestEnd = Unit->GetActorLocation() + TestDirection * SearchRadius;

            if (IsPathClear(Unit, Unit->GetActorLocation(), TestEnd))
            {
                return TestDirection;
            }
        }
    }

    return FVector::ZeroVector; // No clear direction found
}
