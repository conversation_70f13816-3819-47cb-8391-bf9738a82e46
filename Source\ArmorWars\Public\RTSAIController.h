#pragma once

#include "CoreMinimal.h"
#include "AIController.h"
#include "RTSBaseActor.h"
#include "RTSFormationSystem.h"
#include "RTSAIController.generated.h"

class ARTSUnit;
class URTSUnitAIComponent;
class URTSBehaviorTreeComponent;
class URTSBehaviorNode;

// Enum for AI states
UENUM(BlueprintType)
enum class ERTSAIState : uint8
{
    Idle            UMETA(DisplayName = "Idle"),
    Patrol          UMETA(DisplayName = "Patrol"),
    Attack          UMETA(DisplayName = "Attack"),
    Defend          UMETA(DisplayName = "Defend"),
    Follow          UMETA(DisplayName = "Follow"),
    Retreat         UMETA(DisplayName = "Retreat"),
    FormationMove   UMETA(DisplayName = "Formation Move"),
    Investigate     UMETA(DisplayName = "Investigate")
};

// Enum for AI priorities
UENUM(BlueprintType)
enum class ERTSAIPriority : uint8
{
    Low             UMETA(DisplayName = "Low"),
    Normal          UMETA(DisplayName = "Normal"),
    High            UMETA(DisplayName = "High"),
    Critical        UMETA(DisplayName = "Critical")
};

// Enum for combat behaviors
UENUM(BlueprintType)
enum class ERTSCombatBehavior : uint8
{
    Aggressive      UMETA(DisplayName = "Aggressive"),
    Defensive       UMETA(DisplayName = "Defensive"),
    Kiting          UMETA(DisplayName = "Kiting"),
    Flanking        UMETA(DisplayName = "Flanking"),
    CoverSeeking    UMETA(DisplayName = "Cover Seeking"),
    SupportFire     UMETA(DisplayName = "Support Fire"),
    HitAndRun       UMETA(DisplayName = "Hit and Run")
};

// Struct for AI target information
USTRUCT(BlueprintType)
struct ARMORWARS_API FRTSAITargetInfo
{
    GENERATED_BODY()

    // The target actor
    UPROPERTY(BlueprintReadWrite, Category = "AI Target")
    TWeakObjectPtr<ARTSBaseActor> Target;

    // Priority of this target
    UPROPERTY(BlueprintReadWrite, Category = "AI Target")
    ERTSAIPriority Priority = ERTSAIPriority::Normal;

    // Distance to target
    UPROPERTY(BlueprintReadWrite, Category = "AI Target")
    float Distance = 0.0f;

    // Threat level (0-1)
    UPROPERTY(BlueprintReadWrite, Category = "AI Target")
    float ThreatLevel = 0.5f;

    // Last seen time
    UPROPERTY(BlueprintReadWrite, Category = "AI Target")
    float LastSeenTime = 0.0f;

    FRTSAITargetInfo()
    {
        Target = nullptr;
        Priority = ERTSAIPriority::Normal;
        Distance = 0.0f;
        ThreatLevel = 0.5f;
        LastSeenTime = 0.0f;
    }
};

/**
 * AI Controller for RTS units with autonomous behavior
 * Handles target acquisition, state management, and tactical decisions
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API ARTSAIController : public AAIController
{
    GENERATED_BODY()

public:
    ARTSAIController();

protected:
    virtual void BeginPlay() override;
    virtual void OnPossess(APawn* InPawn) override;
    virtual void OnUnPossess() override;

public:
    virtual void Tick(float DeltaTime) override;

public:
    // Current AI state
    UPROPERTY(BlueprintReadOnly, Category = "AI State")
    ERTSAIState CurrentState = ERTSAIState::Idle;

    // Detection range for enemies
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Settings")
    float DetectionRange = 1500.0f;

    // Attack range (will use unit's attack range if 0)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Settings")
    float AttackRange = 0.0f;

    // How often to update AI logic (seconds)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Settings")
    float AIUpdateInterval = 0.5f;

    // Whether this AI should be aggressive
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Settings")
    bool bIsAggressive = true;

    // Whether this AI should defend its position
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Settings")
    bool bShouldDefend = false;

    // Patrol points for patrol behavior
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Settings")
    TArray<FVector> PatrolPoints;

    // Current patrol index
    UPROPERTY(BlueprintReadOnly, Category = "AI State")
    int32 CurrentPatrolIndex = 0;

    // Current target information
    UPROPERTY(BlueprintReadOnly, Category = "AI State")
    FRTSAITargetInfo CurrentTarget;

    // List of known targets
    UPROPERTY(BlueprintReadOnly, Category = "AI State")
    TArray<FRTSAITargetInfo> KnownTargets;

    // Defend position
    UPROPERTY(BlueprintReadWrite, Category = "AI Settings")
    FVector DefendPosition = FVector::ZeroVector;

    // Maximum distance from defend position
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Settings")
    float DefendRadius = 1000.0f;

    /** Whether debug logging is enabled */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bEnableDebugLogging = true;

    // Behavior Tree Component
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    URTSBehaviorTreeComponent* BehaviorTreeComponent;

public:
    // AI State Management
    UFUNCTION(BlueprintCallable, Category = "AI Control")
    void SetAIState(ERTSAIState NewState);

    UFUNCTION(BlueprintCallable, Category = "AI Control")
    void SetPatrolPoints(const TArray<FVector>& Points);

    UFUNCTION(BlueprintCallable, Category = "AI Control")
    void SetDefendPosition(const FVector& Position);

    UFUNCTION(BlueprintCallable, Category = "AI Control")
    void SetFollowTarget(ARTSBaseActor* Target);

    // Target Management
    UFUNCTION(BlueprintCallable, Category = "AI Targeting")
    void AddKnownTarget(ARTSBaseActor* Target, ERTSAIPriority Priority = ERTSAIPriority::Normal);

    UFUNCTION(BlueprintCallable, Category = "AI Targeting")
    void RemoveKnownTarget(ARTSBaseActor* Target);

    UFUNCTION(BlueprintCallable, Category = "AI Targeting")
    ARTSBaseActor* FindBestTarget();

    UFUNCTION(BlueprintPure, Category = "AI Targeting")
    bool HasValidTarget() const;

    // Detection
    UFUNCTION(BlueprintCallable, Category = "AI Detection")
    TArray<ARTSBaseActor*> DetectEnemiesInRange();

    UFUNCTION(BlueprintPure, Category = "AI Detection")
    bool CanSeeTarget(ARTSBaseActor* Target) const;

    // State Queries
    UFUNCTION(BlueprintPure, Category = "AI State")
    bool IsInCombat() const;

    UFUNCTION(BlueprintPure, Category = "AI State")
    bool ShouldRetreat() const;

    UFUNCTION(BlueprintPure, Category = "AI State")
    float GetDistanceToDefendPosition() const;

    // Advanced Movement Functions
    UFUNCTION(BlueprintCallable, Category = "AI Movement")
    void MoveToLocationWithPathfinding(const FVector& Destination);

    UFUNCTION(BlueprintCallable, Category = "AI Movement")
    void SetFormationMovement(bool bEnabled, ARTSAIController* NewFormationLeader = nullptr, FVector NewFormationOffset = FVector::ZeroVector);

    UFUNCTION(BlueprintCallable, Category = "AI Movement")
    void EnableObstacleAvoidance(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "AI Movement")
    void SetMovementSpeed(float NewSpeed);

    UFUNCTION(BlueprintPure, Category = "AI Movement")
    bool IsMoving() const;

    UFUNCTION(BlueprintPure, Category = "AI Movement")
    bool HasReachedDestination() const;

    virtual void StopMovement() override;

    // Group Coordination
    UFUNCTION(BlueprintCallable, Category = "AI Group")
    void JoinGroup(ARTSAIController* NewGroupLeader);

    UFUNCTION(BlueprintCallable, Category = "AI Group")
    void LeaveGroup();

    UFUNCTION(BlueprintCallable, Category = "AI Group")
    void SetGroupFormation(ERTSFormationType Formation);

    UFUNCTION(BlueprintPure, Category = "AI Group")
    bool IsInGroup() const;

    UFUNCTION(BlueprintPure, Category = "AI Group")
    bool IsGroupLeader() const;

    // Advanced Combat Functions
    UFUNCTION(BlueprintCallable, Category = "AI Combat")
    void SetCombatBehavior(ERTSCombatBehavior Behavior);

    UFUNCTION(BlueprintCallable, Category = "AI Combat")
    void SetEngagementRange(float MinRange, float MaxRange);

    UFUNCTION(BlueprintCallable, Category = "AI Combat")
    void EnableKitingBehavior(bool bEnabled, float KitingDistance = 300.0f);

    UFUNCTION(BlueprintCallable, Category = "AI Combat")
    void SetFlankingBehavior(bool bEnabled, float FlankingRadius = 500.0f);

    UFUNCTION(BlueprintCallable, Category = "AI Combat")
    void EnableCoverSeeking(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "AI Combat")
    void SetRetreatThreshold(float HealthPercentage);

    UFUNCTION(BlueprintPure, Category = "AI Combat")
    bool IsInOptimalRange() const;

    UFUNCTION(BlueprintPure, Category = "AI Combat")
    bool ShouldKite() const;

    UFUNCTION(BlueprintPure, Category = "AI Combat")
    bool ShouldFlank() const;

    UFUNCTION(BlueprintCallable, Category = "AI Combat")
    void CoordinateAttackWithGroup(ARTSBaseActor* Target);

    // Behavior Tree Functions
    UFUNCTION(BlueprintCallable, Category = "AI Behavior Tree")
    void StartBehaviorTree(URTSBehaviorNode* RootNode);

    UFUNCTION(BlueprintCallable, Category = "AI Behavior Tree")
    void StopBehaviorTree();

    UFUNCTION(BlueprintCallable, Category = "AI Behavior Tree")
    void PauseBehaviorTree();

    UFUNCTION(BlueprintCallable, Category = "AI Behavior Tree")
    void ResumeBehaviorTree();

    UFUNCTION(BlueprintPure, Category = "AI Behavior Tree")
    bool IsBehaviorTreeRunning() const;

    UFUNCTION(BlueprintCallable, Category = "AI Behavior Tree")
    URTSBehaviorNode* CreateDefaultBehaviorTree();

    UFUNCTION(BlueprintCallable, Category = "AI Behavior Tree")
    URTSBehaviorNode* CreateCombatBehaviorTree();

    UFUNCTION(BlueprintCallable, Category = "AI Behavior Tree")
    URTSBehaviorNode* CreatePatrolBehaviorTree(const TArray<FVector>& PatrolWaypoints);

    // Unit death handling
    UFUNCTION(BlueprintCallable, Category = "AI Events")
    void OnUnitDeath();

protected:
    // AI Update Functions
    virtual void UpdateAI(float DeltaTime);
    virtual void UpdateTargetAcquisition(float DeltaTime);
    virtual void UpdateCurrentState(float DeltaTime);

    // State Handlers
    virtual void HandleIdleState(float DeltaTime);
    virtual void HandlePatrolState(float DeltaTime);
    virtual void HandleAttackState(float DeltaTime);
    virtual void HandleDefendState(float DeltaTime);
    virtual void HandleFollowState(float DeltaTime);
    virtual void HandleRetreatState(float DeltaTime);

    // Utility Functions
    virtual void MoveToNextPatrolPoint();
    virtual bool IsAtLocation(const FVector& Location, float Tolerance = 100.0f) const;
    virtual float CalculateThreatLevel(ARTSBaseActor* Target) const;
    virtual ERTSAIPriority CalculateTargetPriority(ARTSBaseActor* Target) const;

    // Get the controlled RTS unit
    ARTSUnit* GetControlledUnit() const;

    // Advanced Movement Functions
    virtual void UpdateMovementAI(float DeltaTime);
    virtual void UpdateFormationMovement(float DeltaTime);
    virtual void UpdateObstacleAvoidance(float DeltaTime);
    virtual void UpdateGroupCoordination(float DeltaTime);

    // Pathfinding helpers
    virtual bool FindPathToLocation(const FVector& Destination, TArray<FVector>& OutPath);
    virtual FVector GetNextPathPoint();
    virtual bool ShouldRecalculatePath() const;

    // Formation movement helpers
    virtual FVector CalculateFormationPosition() const;
    virtual void MaintainFormationPosition(float DeltaTime);

    // Obstacle avoidance helpers
    virtual FVector CalculateAvoidanceVector() const;
    virtual bool IsObstacleInPath(const FVector& Direction, float Distance) const;
    virtual TArray<AActor*> GetNearbyObstacles() const;

    // Group coordination helpers
    UFUNCTION(BlueprintCallable, Category = "AI Group")
    virtual void BroadcastToGroup(const FString& Message);

    virtual void ReceiveGroupMessage(const FString& Message, ARTSAIController* Sender);
    virtual void UpdateGroupFormation();

    // Combat AI helpers
    virtual void UpdateCombatAI(float DeltaTime);
    virtual void ExecuteCombatBehavior(float DeltaTime);
    virtual void HandleAggressiveBehavior(float DeltaTime);
    virtual void HandleDefensiveBehavior(float DeltaTime);
    virtual void HandleKitingBehavior(float DeltaTime);
    virtual void HandleFlankingBehavior(float DeltaTime);
    virtual void HandleCoverSeekingBehavior(float DeltaTime);
    virtual void HandleSupportFireBehavior(float DeltaTime);
    virtual void HandleHitAndRunBehavior(float DeltaTime);

    // Combat positioning helpers
    virtual FVector FindOptimalCombatPosition() const;
    virtual FVector FindFlankingPosition(ARTSBaseActor* Target) const;
    virtual FVector FindCoverPosition() const;
    virtual FVector FindKitingPosition(ARTSBaseActor* Target) const;
    virtual bool IsInCover() const;
    virtual bool HasLineOfSightToTarget(ARTSBaseActor* Target) const;

    // Target analysis
    virtual float CalculateTargetThreat(ARTSBaseActor* Target) const;
    virtual bool IsTargetInRange(ARTSBaseActor* Target) const;
    virtual bool ShouldEngageTarget(ARTSBaseActor* Target) const;
    virtual ARTSBaseActor* FindBestCombatTarget() const;

protected:
    // AI update timer
    float LastAIUpdateTime = 0.0f;

    // Follow target
    TWeakObjectPtr<ARTSBaseActor> FollowTarget;

    // Last known position of current target
    FVector LastKnownTargetPosition = FVector::ZeroVector;

    // Advanced Movement State
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Movement")
    bool bUsePathfinding = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Movement")
    bool bUseObstacleAvoidance = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Movement")
    float MovementSpeed = 600.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Movement")
    float ArrivalTolerance = 100.0f;

    // Pathfinding state
    TArray<FVector> CurrentPath;
    int32 CurrentPathIndex = 0;
    FVector CurrentDestination = FVector::ZeroVector;
    float LastPathfindingTime = 0.0f;
    float PathfindingInterval = 1.0f; // Recalculate path every second

    // Formation movement state
    bool bInFormation = false;
    TWeakObjectPtr<ARTSAIController> FormationLeader;
    FVector FormationOffset = FVector::ZeroVector;
    ERTSFormationType FormationType = ERTSFormationType::Line;

    // Obstacle avoidance state
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Movement")
    float ObstacleAvoidanceRadius = 300.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Movement")
    float ObstacleAvoidanceStrength = 1.0f;

    // Group coordination state
    TWeakObjectPtr<ARTSAIController> GroupLeader;
    TArray<TWeakObjectPtr<ARTSAIController>> GroupMembers;
    bool bIsGroupLeader = false;

    // Combat AI state
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Combat")
    ERTSCombatBehavior CombatBehavior = ERTSCombatBehavior::Aggressive;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Combat")
    float MinEngagementRange = 0.0f; // Use weapon range if 0

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Combat")
    float MaxEngagementRange = 0.0f; // Use weapon range if 0

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Combat")
    bool bUseKiting = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Combat")
    float KitingDistance = 300.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Combat")
    bool bUseFlanking = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Combat")
    float FlankingRadius = 500.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Combat")
    bool bUseCoverSeeking = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Combat")
    float RetreatHealthThreshold = 0.25f; // Retreat when below 25% health

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Combat")
    float CombatUpdateInterval = 0.1f; // Update combat AI more frequently

    // Combat state tracking
    float LastCombatUpdateTime = 0.0f;
    FVector LastCombatPosition = FVector::ZeroVector;
    float LastEngagementTime = 0.0f;
    bool bIsRetreating = false;

    // Behavior Tree Settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Behavior Tree")
    bool bUseBehaviorTree = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Behavior Tree")
    URTSBehaviorNode* DefaultBehaviorTree = nullptr;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Behavior Tree")
    bool bAutoStartBehaviorTree = true;

protected:
    // Blueprint events
    UFUNCTION(BlueprintImplementableEvent, Category = "AI Events")
    void OnStateChanged(ERTSAIState OldState, ERTSAIState NewState);

    UFUNCTION(BlueprintImplementableEvent, Category = "AI Events")
    void OnTargetAcquiredEvent(ARTSBaseActor* Target);

    UFUNCTION(BlueprintImplementableEvent, Category = "AI Events")
    void OnTargetLost(ARTSBaseActor* Target);

    UFUNCTION(BlueprintImplementableEvent, Category = "AI Events")
    void OnEnemyDetectedEvent(ARTSBaseActor* Enemy);

public:
    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnAIStateChanged, ERTSAIState, OldState, ERTSAIState, NewState);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnTargetAcquired, ARTSBaseActor*, Target);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnEnemyDetected, ARTSBaseActor*, Enemy);

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnAIStateChanged OnAIStateChanged;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnTargetAcquired OnTargetAcquired;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnEnemyDetected OnEnemyDetected;
};
