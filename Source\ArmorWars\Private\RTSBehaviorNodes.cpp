#include "RTSBehaviorNodes.h"
#include "RTSBehaviorTreeComponent.h"
#include "RTSAIController.h"
#include "RTSUnit.h"
#include "RTSBaseActor.h"
#include "Engine/World.h"
#include "Kismet/GameplayStatics.h"

// Move To Location Task
URTSMoveToLocationTask::URTSMoveToLocationTask()
{
    NodeName = TEXT("Move To Location");
    NodeDescription = TEXT("Moves the unit to a specified location");
}

ERTSBehaviorNodeStatus URTSMoveToLocationTask::ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime)
{
    ARTSAIController* AIController = GetAIController(BehaviorTreeComponent);
    if (!AIController)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    // Get target location from blackboard
    FVector TargetLocation = BehaviorTreeComponent->GetBlackboardVector(TargetLocationKey);
    if (TargetLocation.IsZero())
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    APawn* ControlledPawn = AIController->GetPawn();
    if (!ControlledPawn)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    // Start movement if not already started
    if (!bMovementStarted)
    {
        AIController->MoveToLocationWithPathfinding(TargetLocation);
        bMovementStarted = true;
    }

    // Check if we've reached the target
    float DistanceToTarget = FVector::Dist(ControlledPawn->GetActorLocation(), TargetLocation);
    if (DistanceToTarget <= AcceptanceRadius)
    {
        bMovementStarted = false;
        return ERTSBehaviorNodeStatus::Success;
    }

    // Check if we're still moving
    if (!AIController->IsMoving())
    {
        // Movement stopped but we haven't reached target - failure
        bMovementStarted = false;
        return ERTSBehaviorNodeStatus::Failure;
    }

    return ERTSBehaviorNodeStatus::Running;
}

// Attack Target Task
URTSAttackTargetTask::URTSAttackTargetTask()
{
    NodeName = TEXT("Attack Target");
    NodeDescription = TEXT("Attacks a specified target");
}

ERTSBehaviorNodeStatus URTSAttackTargetTask::ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime)
{
    ARTSAIController* AIController = GetAIController(BehaviorTreeComponent);
    if (!AIController)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    // Get target from blackboard
    UObject* TargetObject = BehaviorTreeComponent->GetBlackboardObject(TargetKey);
    ARTSBaseActor* Target = Cast<ARTSBaseActor>(TargetObject);
    
    if (!Target || !Target->IsAlive())
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    // Set AI to attack state
    if (AIController->CurrentState != ERTSAIState::Attack)
    {
        AIController->SetAIState(ERTSAIState::Attack);
        AIController->AddKnownTarget(Target, ERTSAIPriority::High);
        AttackStartTime = BehaviorTreeComponent->GetWorld()->GetTimeSeconds();
    }

    // Check if we've been attacking long enough
    float CurrentTime = BehaviorTreeComponent->GetWorld()->GetTimeSeconds();
    if (CurrentTime - AttackStartTime >= AttackDuration)
    {
        return ERTSBehaviorNodeStatus::Success;
    }

    return ERTSBehaviorNodeStatus::Running;
}

// Patrol Task
URTSPatrolTask::URTSPatrolTask()
{
    NodeName = TEXT("Patrol");
    NodeDescription = TEXT("Patrols between specified points");
}

void URTSPatrolTask::InitializeNode(URTSBehaviorTreeComponent* BehaviorTreeComponent)
{
    Super::InitializeNode(BehaviorTreeComponent);
    CurrentPatrolIndex = 0;
    bWaitingAtPoint = false;
}

ERTSBehaviorNodeStatus URTSPatrolTask::ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime)
{
    if (PatrolPoints.Num() == 0)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    ARTSAIController* AIController = GetAIController(BehaviorTreeComponent);
    if (!AIController)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    APawn* ControlledPawn = AIController->GetPawn();
    if (!ControlledPawn)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    FVector CurrentLocation = ControlledPawn->GetActorLocation();
    FVector TargetPoint = PatrolPoints[CurrentPatrolIndex];

    // Check if we're at the current patrol point
    float DistanceToPoint = FVector::Dist2D(CurrentLocation, TargetPoint);
    
    if (DistanceToPoint <= 150.0f) // Reached patrol point
    {
        if (!bWaitingAtPoint)
        {
            // Just arrived, start waiting
            bWaitingAtPoint = true;
            ArrivalTime = BehaviorTreeComponent->GetWorld()->GetTimeSeconds();
            AIController->StopMovement();
        }
        else
        {
            // Check if we've waited long enough
            float CurrentTime = BehaviorTreeComponent->GetWorld()->GetTimeSeconds();
            if (CurrentTime - ArrivalTime >= WaitTimeAtPoint)
            {
                // Move to next patrol point
                CurrentPatrolIndex++;
                if (CurrentPatrolIndex >= PatrolPoints.Num())
                {
                    if (bLoopPatrol)
                    {
                        CurrentPatrolIndex = 0;
                    }
                    else
                    {
                        return ERTSBehaviorNodeStatus::Success; // Patrol complete
                    }
                }
                bWaitingAtPoint = false;
            }
        }
    }
    else
    {
        // Move towards current patrol point
        if (!AIController->IsMoving() || bWaitingAtPoint)
        {
            AIController->MoveToLocationWithPathfinding(TargetPoint);
            bWaitingAtPoint = false;
        }
    }

    return ERTSBehaviorNodeStatus::Running;
}

// Find Enemy Task
URTSFindEnemyTask::URTSFindEnemyTask()
{
    NodeName = TEXT("Find Enemy");
    NodeDescription = TEXT("Searches for enemies within range");
}

ERTSBehaviorNodeStatus URTSFindEnemyTask::ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime)
{
    ARTSAIController* AIController = GetAIController(BehaviorTreeComponent);
    if (!AIController)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    // Use AI controller's detection system
    TArray<ARTSBaseActor*> DetectedEnemies = AIController->DetectEnemiesInRange();
    
    if (DetectedEnemies.Num() > 0)
    {
        // Find the best target
        ARTSBaseActor* BestTarget = AIController->FindBestTarget();
        if (BestTarget)
        {
            BehaviorTreeComponent->SetBlackboardObject(FoundEnemyKey, BestTarget);
            return ERTSBehaviorNodeStatus::Success;
        }
    }

    return ERTSBehaviorNodeStatus::Failure;
}

// Health Low Condition
URTSHealthLowCondition::URTSHealthLowCondition()
{
    NodeName = TEXT("Health Low");
    NodeDescription = TEXT("Checks if unit health is below threshold");
}

ERTSBehaviorNodeStatus URTSHealthLowCondition::ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime)
{
    ARTSAIController* AIController = GetAIController(BehaviorTreeComponent);
    if (!AIController)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    ARTSUnit* ControlledUnit = Cast<ARTSUnit>(AIController->GetPawn());
    if (!ControlledUnit)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    float HealthPercentage = ControlledUnit->GetHealthPercentage();
    return HealthPercentage <= HealthThreshold ? ERTSBehaviorNodeStatus::Success : ERTSBehaviorNodeStatus::Failure;
}

// Enemy Nearby Condition
URTSEnemyNearbyCondition::URTSEnemyNearbyCondition()
{
    NodeName = TEXT("Enemy Nearby");
    NodeDescription = TEXT("Checks if enemies are within detection range");
}

ERTSBehaviorNodeStatus URTSEnemyNearbyCondition::ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime)
{
    ARTSAIController* AIController = GetAIController(BehaviorTreeComponent);
    if (!AIController)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    // Temporarily set detection range
    float OriginalRange = AIController->DetectionRange;
    AIController->DetectionRange = DetectionRadius;
    
    TArray<ARTSBaseActor*> DetectedEnemies = AIController->DetectEnemiesInRange();
    
    // Restore original range
    AIController->DetectionRange = OriginalRange;

    return DetectedEnemies.Num() > 0 ? ERTSBehaviorNodeStatus::Success : ERTSBehaviorNodeStatus::Failure;
}

// Has Valid Target Condition
URTSHasValidTargetCondition::URTSHasValidTargetCondition()
{
    NodeName = TEXT("Has Valid Target");
    NodeDescription = TEXT("Checks if there is a valid target in blackboard");
}

ERTSBehaviorNodeStatus URTSHasValidTargetCondition::ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime)
{
    UObject* TargetObject = BehaviorTreeComponent->GetBlackboardObject(TargetKey);
    ARTSBaseActor* Target = Cast<ARTSBaseActor>(TargetObject);
    
    return (Target && Target->IsAlive()) ? ERTSBehaviorNodeStatus::Success : ERTSBehaviorNodeStatus::Failure;
}

// Inverter Decorator
URTSInverterDecorator::URTSInverterDecorator()
{
    NodeName = TEXT("Inverter");
    NodeDescription = TEXT("Inverts the result of its child node");
}

ERTSBehaviorNodeStatus URTSInverterDecorator::ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime)
{
    if (ChildNodes.Num() == 0)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    URTSBehaviorNode* ChildNode = ChildNodes[0];
    if (!ChildNode)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    ERTSBehaviorNodeStatus ChildResult = ChildNode->ExecuteNode(BehaviorTreeComponent, DeltaTime);

    // Invert the result
    switch (ChildResult)
    {
        case ERTSBehaviorNodeStatus::Success:
            return ERTSBehaviorNodeStatus::Failure;
        case ERTSBehaviorNodeStatus::Failure:
            return ERTSBehaviorNodeStatus::Success;
        case ERTSBehaviorNodeStatus::Running:
            return ERTSBehaviorNodeStatus::Running;
        default:
            return ERTSBehaviorNodeStatus::Failure;
    }
}

// Repeater Decorator
URTSRepeaterDecorator::URTSRepeaterDecorator()
{
    NodeName = TEXT("Repeater");
    NodeDescription = TEXT("Repeats its child node a specified number of times");
}

void URTSRepeaterDecorator::InitializeNode(URTSBehaviorTreeComponent* BehaviorTreeComponent)
{
    Super::InitializeNode(BehaviorTreeComponent);
    CurrentRepeatCount = 0;
}

ERTSBehaviorNodeStatus URTSRepeaterDecorator::ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime)
{
    if (ChildNodes.Num() == 0)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    URTSBehaviorNode* ChildNode = ChildNodes[0];
    if (!ChildNode)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    ERTSBehaviorNodeStatus ChildResult = ChildNode->ExecuteNode(BehaviorTreeComponent, DeltaTime);

    if (ChildResult == ERTSBehaviorNodeStatus::Running)
    {
        return ERTSBehaviorNodeStatus::Running;
    }

    // Child completed (success or failure)
    CurrentRepeatCount++;

    // Check if we should repeat
    if (RepeatCount == -1 || CurrentRepeatCount < RepeatCount)
    {
        // Reset child node for next iteration
        ChildNode->AbortNode(BehaviorTreeComponent);
        return ERTSBehaviorNodeStatus::Running;
    }
    else
    {
        // Finished all repetitions
        return ERTSBehaviorNodeStatus::Success;
    }
}

// Cooldown Decorator
URTSCooldownDecorator::URTSCooldownDecorator()
{
    NodeName = TEXT("Cooldown");
    NodeDescription = TEXT("Adds a cooldown period before allowing child execution");
}

ERTSBehaviorNodeStatus URTSCooldownDecorator::ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime)
{
    if (ChildNodes.Num() == 0)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    float CurrentTime = BehaviorTreeComponent->GetWorld()->GetTimeSeconds();

    // Check if cooldown has elapsed
    if (CurrentTime - LastExecutionTime < CooldownTime)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    URTSBehaviorNode* ChildNode = ChildNodes[0];
    if (!ChildNode)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    ERTSBehaviorNodeStatus ChildResult = ChildNode->ExecuteNode(BehaviorTreeComponent, DeltaTime);

    // Update last execution time when child completes
    if (ChildResult != ERTSBehaviorNodeStatus::Running)
    {
        LastExecutionTime = CurrentTime;
    }

    return ChildResult;
}

// Health Condition
URTSHealthCondition::URTSHealthCondition()
{
    NodeName = TEXT("Health Condition");
    NodeDescription = TEXT("Checks unit health against threshold");
}

ERTSBehaviorNodeStatus URTSHealthCondition::ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime)
{
    ARTSAIController* AIController = GetAIController(BehaviorTreeComponent);
    if (!AIController)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    ARTSUnit* ControlledUnit = Cast<ARTSUnit>(AIController->GetPawn());
    if (!ControlledUnit)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    float HealthPercentage = ControlledUnit->GetHealthPercentage();

    if (bCheckLowHealth)
    {
        return HealthPercentage <= HealthThreshold ? ERTSBehaviorNodeStatus::Success : ERTSBehaviorNodeStatus::Failure;
    }
    else if (bCheckHighHealth)
    {
        return HealthPercentage >= HealthThreshold ? ERTSBehaviorNodeStatus::Success : ERTSBehaviorNodeStatus::Failure;
    }

    return ERTSBehaviorNodeStatus::Failure;
}
