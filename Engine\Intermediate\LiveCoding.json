{"LinkerPath": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\Hostx64\\x64\\link.exe", "LinkerEnvironment": {"VC_COMPILER_PATH": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\Hostx64\\x64\\cl.exe", "SESSIONNAME": "<PERSON><PERSON><PERSON>", "APPDATA": "C:\\Users\\<USER>\\AppData\\Roaming", "GIT_LFS_PATH": "C:\\Program Files\\Git LFS", "USERDOMAIN": "DESKTOP-GBDVD69", "LOGONSERVER": "\\\\DESKTOP-GBDVD69", "CommonProgramFiles": "C:\\Program Files\\Common Files", "FPS_BROWSER_USER_PROFILE_STRING": "<PERSON><PERSON><PERSON>", "USERNAME": "linck", "OneDriveConsumer": "C:\\Users\\<USER>\\OneDrive", "USERPROFILE": "C:\\Users\\<USER>", "UE_DOTNET_ARCH": "win-x64", "ProgramFiles": "C:\\Program Files", "ProjectFile": "\"Programs\\UnrealBuildTool\\UnrealBuildTool.csproj\"", "DriverData": "C:\\Windows\\System32\\Drivers\\DriverData", "PROCESSOR_IDENTIFIER": "Intel64 Family 6 Model 158 Stepping 13, GenuineIntel", "UE-ZenSubprocessDataPath": "C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data", "CommonProgramW6432": "C:\\Program Files\\Common Files", "PYTHONHOME": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37", "DOTNET_ROLL_FORWARD": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "UE_DOTNET_VERSION": "8.0.300", "UE_DOTNET_DIR": "F:\\UE_5.6\\Engine\\Build\\BatchFiles\\..\\..\\Binaries\\ThirdParty\\DotNet\\8.0.300\\win-x64", "SystemDrive": "C:", "UBTPath": "\"..\\..\\Engine\\Binaries\\DotNET\\UnrealBuildTool\\UnrealBuildTool.dll\"", "VS140COMNTOOLS": "C:\\Program Files (x86)\\Microsoft Visual Studio 14.0\\Common7\\Tools\\", "windir": "C:\\Windows", "CommonProgramFiles(x86)": "C:\\Program Files (x86)\\Common Files", "ComSpec": "C:\\Windows\\system32\\cmd.exe", "ProgramW6432": "C:\\Program Files", "PROCESSOR_ARCHITECTURE": "AMD64", "PROCESSOR_LEVEL": "6", "PSModulePath": "C:\\Program Files\\WindowsPowerShell\\Modules;C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules", "PGO_PATH_TRANSLATION": "F:\\UE_5.6=ROOT;F:\\ArmorWars=PROJ", "Path": "C:\\Program Files (x86)\\Windows Kits\\10\\bin\\10.0.26100.0\\x64;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\Hostx64\\x64;F:\\UE_5.6\\Engine\\Binaries\\DotNET\\UnrealBuildTool;F:\\UE_5.6\\Engine\\Build\\BatchFiles\\..\\..\\Binaries\\ThirdParty\\DotNet\\8.0.300\\win-x64;C:\\Program Files\\Common Files\\Oracle\\Java\\javapath;C:\\Program Files (x86)\\Common Files\\Oracle\\Java\\java8path;C:\\Program Files (x86)\\Common Files\\Oracle\\Java\\javapath;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\dotnet\\;C:\\Program Files\\Process Lasso\\;C:\\Program Files\\Graphviz\\bin;C:\\Program Files\\Git LFS;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\nodejs\\;C:\\Program Files\\CMake\\bin;C:\\Program Files\\GitHub CLI\\;C:\\Program Files\\TortoiseSVN\\bin;C:\\Program Files\\uesave\\bin\\;C:\\Program Files\\repak_cli\\bin\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Meld\\;C:\\Users\\<USER>\\.cargo\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Pandoc\\;C:\\Users\\<USER>\\xmake", "LockFile": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\F-UE_5.6-Engine-Build-BatchFiles-Build.bat.lock", "PROMPT": "$P$G", "UE_DesktopUnrealProcess": "1", "VC_COMPILER_DIR": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\Hostx64\\x64", "PATHEXT": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "WaitingForLock": "0", "TEMP": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\UnrealBuildTool\\8c4c1eb4", "COMPUTERNAME": "DESKTOP-GBDVD69", "FPS_BROWSER_APP_PROFILE_STRING": "Internet Explorer", "DOTNET_MULTILEVEL_LOOKUP": "0", "asl.log": "Destination=file", "OneDrive": "C:\\Users\\<USER>\\OneDrive", "DOTNET_ROOT": "F:\\UE_5.6\\Engine\\Build\\BatchFiles\\..\\..\\Binaries\\ThirdParty\\DotNet\\8.0.300\\win-x64", "HOMEDRIVE": "C:", "PUBLIC": "C:\\Users\\<USER>", "ProgramFiles(x86)": "C:\\Program Files (x86)", "EOS_LAUNCHED_BY_EPIC": "0", "UnrealBuildTool_TMP": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\UnrealBuildTool\\8c4c1eb4", "LOCALAPPDATA": "C:\\Users\\<USER>\\AppData\\Local", "SystemRoot": "C:\\Windows", "VC_TOOLCHAIN_DIR": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\Hostx64\\x64", "TMP": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\UnrealBuildTool\\8c4c1eb4", "NUMBER_OF_PROCESSORS": "8", "HOMEPATH": "\\Users\\linck", "USERDOMAIN_ROAMINGPROFILE": "DESKTOP-GBDVD69", "ALLUSERSPROFILE": "C:\\ProgramData", "PROCESSOR_REVISION": "9e0d", "ProgramData": "C:\\ProgramData", "OS": "Windows_NT"}, "Modules": [{"Output": "F:\\ArmorWars\\Binaries\\Win64\\UnrealEditor-ArmorWars.dll", "Inputs": ["F:\\ArmorWars\\Intermediate\\Build\\Win64\\x64\\UnrealEditor\\Development\\ArmorWars\\Module.ArmorWars.1.cpp.lc.obj", "F:\\ArmorWars\\Intermediate\\Build\\Win64\\x64\\UnrealEditor\\Development\\ArmorWars\\Module.ArmorWars.2.cpp.lc.obj", "F:\\ArmorWars\\Intermediate\\Build\\Win64\\x64\\UnrealEditor\\Development\\ArmorWars\\RTSAIDebugHUD.cpp.lc.obj", "F:\\ArmorWars\\Intermediate\\Build\\Win64\\x64\\UnrealEditor\\Development\\ArmorWars\\RTSAITestScenario.cpp.lc.obj"], "Libraries": ["F:\\UE_5.6\\Engine\\Intermediate\\Build\\Win64\\x64\\UnrealEditor\\Development\\AIModule\\UnrealEditor-AIModule.lib", "F:\\UE_5.6\\Engine\\Intermediate\\Build\\Win64\\x64\\UnrealEditor\\Development\\CoreUObject\\UnrealEditor-CoreUObject.lib", "F:\\UE_5.6\\Engine\\Intermediate\\Build\\Win64\\x64\\UnrealEditor\\Development\\Core\\UnrealEditor-Core.lib", "F:\\UE_5.6\\Engine\\Intermediate\\Build\\Win64\\x64\\UnrealEditor\\Development\\Engine\\UnrealEditor-Engine.lib", "F:\\UE_5.6\\Engine\\Intermediate\\Build\\Win64\\x64\\UnrealEditor\\Development\\GameplayTags\\UnrealEditor-GameplayTags.lib", "F:\\UE_5.6\\Engine\\Intermediate\\Build\\Win64\\x64\\UnrealEditor\\Development\\InputCore\\UnrealEditor-InputCore.lib", "F:\\UE_5.6\\Engine\\Intermediate\\Build\\Win64\\x64\\UnrealEditor\\Development\\NavigationSystem\\UnrealEditor-NavigationSystem.lib", "F:\\UE_5.6\\Engine\\Intermediate\\Build\\Win64\\x64\\UnrealEditor\\Development\\SlateCore\\UnrealEditor-SlateCore.lib", "F:\\UE_5.6\\Engine\\Intermediate\\Build\\Win64\\x64\\UnrealEditor\\Development\\Slate\\UnrealEditor-Slate.lib", "F:\\UE_5.6\\Engine\\Intermediate\\Build\\Win64\\x64\\UnrealEditor\\Development\\UMG\\UnrealEditor-UMG.lib", "F:\\UE_5.6\\Engine\\Plugins\\EnhancedInput\\Intermediate\\Build\\Win64\\x64\\UnrealEditor\\Development\\EnhancedInput\\UnrealEditor-EnhancedInput.lib", "F:\\UE_5.6\\Engine\\Plugins\\Runtime\\InstancedActors\\Intermediate\\Build\\Win64\\x64\\UnrealEditor\\Development\\InstancedActors\\UnrealEditor-InstancedActors.lib"]}], "Vfs": []}