// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RTSTeamManager.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeRTSTeamManager() {}

// ********** Begin Cross Module References ********************************************************
ARMORWARS_API UClass* Z_Construct_UClass_ARTSBaseActor_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSTeamManager();
ARMORWARS_API UClass* Z_Construct_UClass_URTSTeamManager_NoRegister();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamCreated__DelegateSignature();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamRemoved__DelegateSignature();
ARMORWARS_API UScriptStruct* Z_Construct_UScriptStruct_FRTSTeamInfo();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UWorldSubsystem();
UPackage* Z_Construct_UPackage__Script_ArmorWars();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FRTSTeamInfo ******************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FRTSTeamInfo;
class UScriptStruct* FRTSTeamInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FRTSTeamInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FRTSTeamInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FRTSTeamInfo, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("RTSTeamInfo"));
	}
	return Z_Registration_Info_UScriptStruct_FRTSTeamInfo.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FRTSTeamInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Struct for team information\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSTeamManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Struct for team information" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeamID_MetaData[] = {
		{ "Category", "Team" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Team ID\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSTeamManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Team ID" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeamName_MetaData[] = {
		{ "Category", "Team" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Team name\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSTeamManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Team name" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsAIControlled_MetaData[] = {
		{ "Category", "Team" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Whether this team is controlled by AI\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSTeamManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether this team is controlled by AI" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "Team" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Whether this team is active\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSTeamManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether this team is active" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamID;
	static const UECodeGen_Private::FTextPropertyParams NewProp_TeamName;
	static void NewProp_bIsAIControlled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsAIControlled;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FRTSTeamInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FRTSTeamInfo_Statics::NewProp_TeamID = { "TeamID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSTeamInfo, TeamID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeamID_MetaData), NewProp_TeamID_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FRTSTeamInfo_Statics::NewProp_TeamName = { "TeamName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSTeamInfo, TeamName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeamName_MetaData), NewProp_TeamName_MetaData) };
void Z_Construct_UScriptStruct_FRTSTeamInfo_Statics::NewProp_bIsAIControlled_SetBit(void* Obj)
{
	((FRTSTeamInfo*)Obj)->bIsAIControlled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FRTSTeamInfo_Statics::NewProp_bIsAIControlled = { "bIsAIControlled", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FRTSTeamInfo), &Z_Construct_UScriptStruct_FRTSTeamInfo_Statics::NewProp_bIsAIControlled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsAIControlled_MetaData), NewProp_bIsAIControlled_MetaData) };
void Z_Construct_UScriptStruct_FRTSTeamInfo_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FRTSTeamInfo*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FRTSTeamInfo_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FRTSTeamInfo), &Z_Construct_UScriptStruct_FRTSTeamInfo_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FRTSTeamInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSTeamInfo_Statics::NewProp_TeamID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSTeamInfo_Statics::NewProp_TeamName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSTeamInfo_Statics::NewProp_bIsAIControlled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSTeamInfo_Statics::NewProp_bIsActive,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSTeamInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FRTSTeamInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	&NewStructOps,
	"RTSTeamInfo",
	Z_Construct_UScriptStruct_FRTSTeamInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSTeamInfo_Statics::PropPointers),
	sizeof(FRTSTeamInfo),
	alignof(FRTSTeamInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSTeamInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FRTSTeamInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FRTSTeamInfo()
{
	if (!Z_Registration_Info_UScriptStruct_FRTSTeamInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FRTSTeamInfo.InnerSingleton, Z_Construct_UScriptStruct_FRTSTeamInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FRTSTeamInfo.InnerSingleton;
}
// ********** End ScriptStruct FRTSTeamInfo ********************************************************

// ********** Begin Delegate FOnTeamCreated ********************************************************
struct Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamCreated__DelegateSignature_Statics
{
	struct RTSTeamManager_eventOnTeamCreated_Parms
	{
		int32 TeamID;
		FRTSTeamInfo TeamInfo;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSTeamManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeamInfo_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TeamInfo;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamCreated__DelegateSignature_Statics::NewProp_TeamID = { "TeamID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventOnTeamCreated_Parms, TeamID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamCreated__DelegateSignature_Statics::NewProp_TeamInfo = { "TeamInfo", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventOnTeamCreated_Parms, TeamInfo), Z_Construct_UScriptStruct_FRTSTeamInfo, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeamInfo_MetaData), NewProp_TeamInfo_MetaData) }; // 1113985836
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamCreated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamCreated__DelegateSignature_Statics::NewProp_TeamID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamCreated__DelegateSignature_Statics::NewProp_TeamInfo,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamCreated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamCreated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSTeamManager, nullptr, "OnTeamCreated__DelegateSignature", Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamCreated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamCreated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamCreated__DelegateSignature_Statics::RTSTeamManager_eventOnTeamCreated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamCreated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamCreated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamCreated__DelegateSignature_Statics::RTSTeamManager_eventOnTeamCreated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamCreated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamCreated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void URTSTeamManager::FOnTeamCreated_DelegateWrapper(const FMulticastScriptDelegate& OnTeamCreated, int32 TeamID, FRTSTeamInfo const& TeamInfo)
{
	struct RTSTeamManager_eventOnTeamCreated_Parms
	{
		int32 TeamID;
		FRTSTeamInfo TeamInfo;
	};
	RTSTeamManager_eventOnTeamCreated_Parms Parms;
	Parms.TeamID=TeamID;
	Parms.TeamInfo=TeamInfo;
	OnTeamCreated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnTeamCreated **********************************************************

// ********** Begin Delegate FOnTeamRemoved ********************************************************
struct Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamRemoved__DelegateSignature_Statics
{
	struct RTSTeamManager_eventOnTeamRemoved_Parms
	{
		int32 TeamID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/RTSTeamManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamRemoved__DelegateSignature_Statics::NewProp_TeamID = { "TeamID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventOnTeamRemoved_Parms, TeamID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamRemoved__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamRemoved__DelegateSignature_Statics::NewProp_TeamID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamRemoved__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamRemoved__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSTeamManager, nullptr, "OnTeamRemoved__DelegateSignature", Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamRemoved__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamRemoved__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamRemoved__DelegateSignature_Statics::RTSTeamManager_eventOnTeamRemoved_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamRemoved__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamRemoved__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamRemoved__DelegateSignature_Statics::RTSTeamManager_eventOnTeamRemoved_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamRemoved__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamRemoved__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void URTSTeamManager::FOnTeamRemoved_DelegateWrapper(const FMulticastScriptDelegate& OnTeamRemoved, int32 TeamID)
{
	struct RTSTeamManager_eventOnTeamRemoved_Parms
	{
		int32 TeamID;
	};
	RTSTeamManager_eventOnTeamRemoved_Parms Parms;
	Parms.TeamID=TeamID;
	OnTeamRemoved.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnTeamRemoved **********************************************************

// ********** Begin Class URTSTeamManager Function AreActorsAllied *********************************
struct Z_Construct_UFunction_URTSTeamManager_AreActorsAllied_Statics
{
	struct RTSTeamManager_eventAreActorsAllied_Parms
	{
		const ARTSBaseActor* ActorA;
		const ARTSBaseActor* ActorB;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Teams" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Actor utility functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSTeamManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Actor utility functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorA_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorB_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActorA;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActorB;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSTeamManager_AreActorsAllied_Statics::NewProp_ActorA = { "ActorA", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventAreActorsAllied_Parms, ActorA), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorA_MetaData), NewProp_ActorA_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSTeamManager_AreActorsAllied_Statics::NewProp_ActorB = { "ActorB", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventAreActorsAllied_Parms, ActorB), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorB_MetaData), NewProp_ActorB_MetaData) };
void Z_Construct_UFunction_URTSTeamManager_AreActorsAllied_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSTeamManager_eventAreActorsAllied_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSTeamManager_AreActorsAllied_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSTeamManager_eventAreActorsAllied_Parms), &Z_Construct_UFunction_URTSTeamManager_AreActorsAllied_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSTeamManager_AreActorsAllied_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_AreActorsAllied_Statics::NewProp_ActorA,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_AreActorsAllied_Statics::NewProp_ActorB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_AreActorsAllied_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_AreActorsAllied_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSTeamManager_AreActorsAllied_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSTeamManager, nullptr, "AreActorsAllied", Z_Construct_UFunction_URTSTeamManager_AreActorsAllied_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_AreActorsAllied_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSTeamManager_AreActorsAllied_Statics::RTSTeamManager_eventAreActorsAllied_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_AreActorsAllied_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSTeamManager_AreActorsAllied_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSTeamManager_AreActorsAllied_Statics::RTSTeamManager_eventAreActorsAllied_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSTeamManager_AreActorsAllied()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSTeamManager_AreActorsAllied_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSTeamManager::execAreActorsAllied)
{
	P_GET_OBJECT(ARTSBaseActor,Z_Param_ActorA);
	P_GET_OBJECT(ARTSBaseActor,Z_Param_ActorB);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AreActorsAllied(Z_Param_ActorA,Z_Param_ActorB);
	P_NATIVE_END;
}
// ********** End Class URTSTeamManager Function AreActorsAllied ***********************************

// ********** Begin Class URTSTeamManager Function AreActorsEnemies ********************************
struct Z_Construct_UFunction_URTSTeamManager_AreActorsEnemies_Statics
{
	struct RTSTeamManager_eventAreActorsEnemies_Parms
	{
		const ARTSBaseActor* ActorA;
		const ARTSBaseActor* ActorB;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Teams" },
		{ "ModuleRelativePath", "Public/RTSTeamManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorA_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorB_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActorA;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActorB;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSTeamManager_AreActorsEnemies_Statics::NewProp_ActorA = { "ActorA", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventAreActorsEnemies_Parms, ActorA), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorA_MetaData), NewProp_ActorA_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSTeamManager_AreActorsEnemies_Statics::NewProp_ActorB = { "ActorB", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventAreActorsEnemies_Parms, ActorB), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorB_MetaData), NewProp_ActorB_MetaData) };
void Z_Construct_UFunction_URTSTeamManager_AreActorsEnemies_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSTeamManager_eventAreActorsEnemies_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSTeamManager_AreActorsEnemies_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSTeamManager_eventAreActorsEnemies_Parms), &Z_Construct_UFunction_URTSTeamManager_AreActorsEnemies_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSTeamManager_AreActorsEnemies_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_AreActorsEnemies_Statics::NewProp_ActorA,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_AreActorsEnemies_Statics::NewProp_ActorB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_AreActorsEnemies_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_AreActorsEnemies_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSTeamManager_AreActorsEnemies_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSTeamManager, nullptr, "AreActorsEnemies", Z_Construct_UFunction_URTSTeamManager_AreActorsEnemies_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_AreActorsEnemies_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSTeamManager_AreActorsEnemies_Statics::RTSTeamManager_eventAreActorsEnemies_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_AreActorsEnemies_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSTeamManager_AreActorsEnemies_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSTeamManager_AreActorsEnemies_Statics::RTSTeamManager_eventAreActorsEnemies_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSTeamManager_AreActorsEnemies()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSTeamManager_AreActorsEnemies_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSTeamManager::execAreActorsEnemies)
{
	P_GET_OBJECT(ARTSBaseActor,Z_Param_ActorA);
	P_GET_OBJECT(ARTSBaseActor,Z_Param_ActorB);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AreActorsEnemies(Z_Param_ActorA,Z_Param_ActorB);
	P_NATIVE_END;
}
// ********** End Class URTSTeamManager Function AreActorsEnemies **********************************

// ********** Begin Class URTSTeamManager Function AreTeamsAllied **********************************
struct Z_Construct_UFunction_URTSTeamManager_AreTeamsAllied_Statics
{
	struct RTSTeamManager_eventAreTeamsAllied_Parms
	{
		int32 TeamA;
		int32 TeamB;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Teams" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Simple team relationship functions (all different teams are enemies)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSTeamManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Simple team relationship functions (all different teams are enemies)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamA;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamB;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSTeamManager_AreTeamsAllied_Statics::NewProp_TeamA = { "TeamA", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventAreTeamsAllied_Parms, TeamA), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSTeamManager_AreTeamsAllied_Statics::NewProp_TeamB = { "TeamB", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventAreTeamsAllied_Parms, TeamB), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSTeamManager_AreTeamsAllied_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSTeamManager_eventAreTeamsAllied_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSTeamManager_AreTeamsAllied_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSTeamManager_eventAreTeamsAllied_Parms), &Z_Construct_UFunction_URTSTeamManager_AreTeamsAllied_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSTeamManager_AreTeamsAllied_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_AreTeamsAllied_Statics::NewProp_TeamA,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_AreTeamsAllied_Statics::NewProp_TeamB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_AreTeamsAllied_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_AreTeamsAllied_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSTeamManager_AreTeamsAllied_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSTeamManager, nullptr, "AreTeamsAllied", Z_Construct_UFunction_URTSTeamManager_AreTeamsAllied_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_AreTeamsAllied_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSTeamManager_AreTeamsAllied_Statics::RTSTeamManager_eventAreTeamsAllied_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_AreTeamsAllied_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSTeamManager_AreTeamsAllied_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSTeamManager_AreTeamsAllied_Statics::RTSTeamManager_eventAreTeamsAllied_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSTeamManager_AreTeamsAllied()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSTeamManager_AreTeamsAllied_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSTeamManager::execAreTeamsAllied)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_TeamA);
	P_GET_PROPERTY(FIntProperty,Z_Param_TeamB);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AreTeamsAllied(Z_Param_TeamA,Z_Param_TeamB);
	P_NATIVE_END;
}
// ********** End Class URTSTeamManager Function AreTeamsAllied ************************************

// ********** Begin Class URTSTeamManager Function AreTeamsEnemies *********************************
struct Z_Construct_UFunction_URTSTeamManager_AreTeamsEnemies_Statics
{
	struct RTSTeamManager_eventAreTeamsEnemies_Parms
	{
		int32 TeamA;
		int32 TeamB;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Teams" },
		{ "ModuleRelativePath", "Public/RTSTeamManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamA;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamB;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSTeamManager_AreTeamsEnemies_Statics::NewProp_TeamA = { "TeamA", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventAreTeamsEnemies_Parms, TeamA), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSTeamManager_AreTeamsEnemies_Statics::NewProp_TeamB = { "TeamB", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventAreTeamsEnemies_Parms, TeamB), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSTeamManager_AreTeamsEnemies_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSTeamManager_eventAreTeamsEnemies_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSTeamManager_AreTeamsEnemies_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSTeamManager_eventAreTeamsEnemies_Parms), &Z_Construct_UFunction_URTSTeamManager_AreTeamsEnemies_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSTeamManager_AreTeamsEnemies_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_AreTeamsEnemies_Statics::NewProp_TeamA,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_AreTeamsEnemies_Statics::NewProp_TeamB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_AreTeamsEnemies_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_AreTeamsEnemies_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSTeamManager_AreTeamsEnemies_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSTeamManager, nullptr, "AreTeamsEnemies", Z_Construct_UFunction_URTSTeamManager_AreTeamsEnemies_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_AreTeamsEnemies_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSTeamManager_AreTeamsEnemies_Statics::RTSTeamManager_eventAreTeamsEnemies_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_AreTeamsEnemies_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSTeamManager_AreTeamsEnemies_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSTeamManager_AreTeamsEnemies_Statics::RTSTeamManager_eventAreTeamsEnemies_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSTeamManager_AreTeamsEnemies()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSTeamManager_AreTeamsEnemies_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSTeamManager::execAreTeamsEnemies)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_TeamA);
	P_GET_PROPERTY(FIntProperty,Z_Param_TeamB);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AreTeamsEnemies(Z_Param_TeamA,Z_Param_TeamB);
	P_NATIVE_END;
}
// ********** End Class URTSTeamManager Function AreTeamsEnemies ***********************************

// ********** Begin Class URTSTeamManager Function CalculateTargetPriority *************************
struct Z_Construct_UFunction_URTSTeamManager_CalculateTargetPriority_Statics
{
	struct RTSTeamManager_eventCalculateTargetPriority_Parms
	{
		const ARTSBaseActor* Attacker;
		const ARTSBaseActor* Target;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Teams|AI" },
		{ "ModuleRelativePath", "Public/RTSTeamManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Attacker_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Target_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Attacker;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSTeamManager_CalculateTargetPriority_Statics::NewProp_Attacker = { "Attacker", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventCalculateTargetPriority_Parms, Attacker), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Attacker_MetaData), NewProp_Attacker_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSTeamManager_CalculateTargetPriority_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventCalculateTargetPriority_Parms, Target), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Target_MetaData), NewProp_Target_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URTSTeamManager_CalculateTargetPriority_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventCalculateTargetPriority_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSTeamManager_CalculateTargetPriority_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_CalculateTargetPriority_Statics::NewProp_Attacker,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_CalculateTargetPriority_Statics::NewProp_Target,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_CalculateTargetPriority_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_CalculateTargetPriority_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSTeamManager_CalculateTargetPriority_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSTeamManager, nullptr, "CalculateTargetPriority", Z_Construct_UFunction_URTSTeamManager_CalculateTargetPriority_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_CalculateTargetPriority_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSTeamManager_CalculateTargetPriority_Statics::RTSTeamManager_eventCalculateTargetPriority_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_CalculateTargetPriority_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSTeamManager_CalculateTargetPriority_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSTeamManager_CalculateTargetPriority_Statics::RTSTeamManager_eventCalculateTargetPriority_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSTeamManager_CalculateTargetPriority()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSTeamManager_CalculateTargetPriority_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSTeamManager::execCalculateTargetPriority)
{
	P_GET_OBJECT(ARTSBaseActor,Z_Param_Attacker);
	P_GET_OBJECT(ARTSBaseActor,Z_Param_Target);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateTargetPriority(Z_Param_Attacker,Z_Param_Target);
	P_NATIVE_END;
}
// ********** End Class URTSTeamManager Function CalculateTargetPriority ***************************

// ********** Begin Class URTSTeamManager Function ClearAllTeams ***********************************
struct Z_Construct_UFunction_URTSTeamManager_ClearAllTeams_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Teams" },
		{ "ModuleRelativePath", "Public/RTSTeamManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSTeamManager_ClearAllTeams_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSTeamManager, nullptr, "ClearAllTeams", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_ClearAllTeams_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSTeamManager_ClearAllTeams_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSTeamManager_ClearAllTeams()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSTeamManager_ClearAllTeams_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSTeamManager::execClearAllTeams)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearAllTeams();
	P_NATIVE_END;
}
// ********** End Class URTSTeamManager Function ClearAllTeams *************************************

// ********** Begin Class URTSTeamManager Function CreateTeam **************************************
struct Z_Construct_UFunction_URTSTeamManager_CreateTeam_Statics
{
	struct RTSTeamManager_eventCreateTeam_Parms
	{
		int32 TeamID;
		FText TeamName;
		bool bIsAI;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Teams" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Team management functions\n" },
#endif
		{ "CPP_Default_bIsAI", "false" },
		{ "ModuleRelativePath", "Public/RTSTeamManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Team management functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeamName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamID;
	static const UECodeGen_Private::FTextPropertyParams NewProp_TeamName;
	static void NewProp_bIsAI_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsAI;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSTeamManager_CreateTeam_Statics::NewProp_TeamID = { "TeamID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventCreateTeam_Parms, TeamID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UFunction_URTSTeamManager_CreateTeam_Statics::NewProp_TeamName = { "TeamName", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventCreateTeam_Parms, TeamName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeamName_MetaData), NewProp_TeamName_MetaData) };
void Z_Construct_UFunction_URTSTeamManager_CreateTeam_Statics::NewProp_bIsAI_SetBit(void* Obj)
{
	((RTSTeamManager_eventCreateTeam_Parms*)Obj)->bIsAI = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSTeamManager_CreateTeam_Statics::NewProp_bIsAI = { "bIsAI", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSTeamManager_eventCreateTeam_Parms), &Z_Construct_UFunction_URTSTeamManager_CreateTeam_Statics::NewProp_bIsAI_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSTeamManager_CreateTeam_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSTeamManager_eventCreateTeam_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSTeamManager_CreateTeam_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSTeamManager_eventCreateTeam_Parms), &Z_Construct_UFunction_URTSTeamManager_CreateTeam_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSTeamManager_CreateTeam_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_CreateTeam_Statics::NewProp_TeamID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_CreateTeam_Statics::NewProp_TeamName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_CreateTeam_Statics::NewProp_bIsAI,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_CreateTeam_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_CreateTeam_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSTeamManager_CreateTeam_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSTeamManager, nullptr, "CreateTeam", Z_Construct_UFunction_URTSTeamManager_CreateTeam_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_CreateTeam_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSTeamManager_CreateTeam_Statics::RTSTeamManager_eventCreateTeam_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_CreateTeam_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSTeamManager_CreateTeam_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSTeamManager_CreateTeam_Statics::RTSTeamManager_eventCreateTeam_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSTeamManager_CreateTeam()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSTeamManager_CreateTeam_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSTeamManager::execCreateTeam)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_TeamID);
	P_GET_PROPERTY_REF(FTextProperty,Z_Param_Out_TeamName);
	P_GET_UBOOL(Z_Param_bIsAI);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CreateTeam(Z_Param_TeamID,Z_Param_Out_TeamName,Z_Param_bIsAI);
	P_NATIVE_END;
}
// ********** End Class URTSTeamManager Function CreateTeam ****************************************

// ********** Begin Class URTSTeamManager Function DoesTeamExist ***********************************
struct Z_Construct_UFunction_URTSTeamManager_DoesTeamExist_Statics
{
	struct RTSTeamManager_eventDoesTeamExist_Parms
	{
		int32 TeamID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Teams" },
		{ "ModuleRelativePath", "Public/RTSTeamManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSTeamManager_DoesTeamExist_Statics::NewProp_TeamID = { "TeamID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventDoesTeamExist_Parms, TeamID), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSTeamManager_DoesTeamExist_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSTeamManager_eventDoesTeamExist_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSTeamManager_DoesTeamExist_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSTeamManager_eventDoesTeamExist_Parms), &Z_Construct_UFunction_URTSTeamManager_DoesTeamExist_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSTeamManager_DoesTeamExist_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_DoesTeamExist_Statics::NewProp_TeamID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_DoesTeamExist_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_DoesTeamExist_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSTeamManager_DoesTeamExist_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSTeamManager, nullptr, "DoesTeamExist", Z_Construct_UFunction_URTSTeamManager_DoesTeamExist_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_DoesTeamExist_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSTeamManager_DoesTeamExist_Statics::RTSTeamManager_eventDoesTeamExist_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_DoesTeamExist_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSTeamManager_DoesTeamExist_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSTeamManager_DoesTeamExist_Statics::RTSTeamManager_eventDoesTeamExist_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSTeamManager_DoesTeamExist()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSTeamManager_DoesTeamExist_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSTeamManager::execDoesTeamExist)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_TeamID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DoesTeamExist(Z_Param_TeamID);
	P_NATIVE_END;
}
// ********** End Class URTSTeamManager Function DoesTeamExist *************************************

// ********** Begin Class URTSTeamManager Function FindEnemiesInRange ******************************
struct Z_Construct_UFunction_URTSTeamManager_FindEnemiesInRange_Statics
{
	struct RTSTeamManager_eventFindEnemiesInRange_Parms
	{
		int32 TeamID;
		FVector Location;
		float Range;
		TArray<ARTSBaseActor*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Teams|AI" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enhanced AI support functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSTeamManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enhanced AI support functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Range;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSTeamManager_FindEnemiesInRange_Statics::NewProp_TeamID = { "TeamID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventFindEnemiesInRange_Parms, TeamID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSTeamManager_FindEnemiesInRange_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventFindEnemiesInRange_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URTSTeamManager_FindEnemiesInRange_Statics::NewProp_Range = { "Range", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventFindEnemiesInRange_Parms, Range), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSTeamManager_FindEnemiesInRange_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSTeamManager_FindEnemiesInRange_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventFindEnemiesInRange_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSTeamManager_FindEnemiesInRange_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_FindEnemiesInRange_Statics::NewProp_TeamID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_FindEnemiesInRange_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_FindEnemiesInRange_Statics::NewProp_Range,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_FindEnemiesInRange_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_FindEnemiesInRange_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_FindEnemiesInRange_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSTeamManager_FindEnemiesInRange_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSTeamManager, nullptr, "FindEnemiesInRange", Z_Construct_UFunction_URTSTeamManager_FindEnemiesInRange_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_FindEnemiesInRange_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSTeamManager_FindEnemiesInRange_Statics::RTSTeamManager_eventFindEnemiesInRange_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_FindEnemiesInRange_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSTeamManager_FindEnemiesInRange_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSTeamManager_FindEnemiesInRange_Statics::RTSTeamManager_eventFindEnemiesInRange_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSTeamManager_FindEnemiesInRange()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSTeamManager_FindEnemiesInRange_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSTeamManager::execFindEnemiesInRange)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_TeamID);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Range);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<ARTSBaseActor*>*)Z_Param__Result=P_THIS->FindEnemiesInRange(Z_Param_TeamID,Z_Param_Out_Location,Z_Param_Range);
	P_NATIVE_END;
}
// ********** End Class URTSTeamManager Function FindEnemiesInRange ********************************

// ********** Begin Class URTSTeamManager Function FindHighestPriorityTarget ***********************
struct Z_Construct_UFunction_URTSTeamManager_FindHighestPriorityTarget_Statics
{
	struct RTSTeamManager_eventFindHighestPriorityTarget_Parms
	{
		int32 TeamID;
		FVector Location;
		float MaxRange;
		ARTSBaseActor* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Teams|AI" },
		{ "CPP_Default_MaxRange", "0.000000" },
		{ "ModuleRelativePath", "Public/RTSTeamManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxRange;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSTeamManager_FindHighestPriorityTarget_Statics::NewProp_TeamID = { "TeamID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventFindHighestPriorityTarget_Parms, TeamID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSTeamManager_FindHighestPriorityTarget_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventFindHighestPriorityTarget_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URTSTeamManager_FindHighestPriorityTarget_Statics::NewProp_MaxRange = { "MaxRange", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventFindHighestPriorityTarget_Parms, MaxRange), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSTeamManager_FindHighestPriorityTarget_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventFindHighestPriorityTarget_Parms, ReturnValue), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSTeamManager_FindHighestPriorityTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_FindHighestPriorityTarget_Statics::NewProp_TeamID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_FindHighestPriorityTarget_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_FindHighestPriorityTarget_Statics::NewProp_MaxRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_FindHighestPriorityTarget_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_FindHighestPriorityTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSTeamManager_FindHighestPriorityTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSTeamManager, nullptr, "FindHighestPriorityTarget", Z_Construct_UFunction_URTSTeamManager_FindHighestPriorityTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_FindHighestPriorityTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSTeamManager_FindHighestPriorityTarget_Statics::RTSTeamManager_eventFindHighestPriorityTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_FindHighestPriorityTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSTeamManager_FindHighestPriorityTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSTeamManager_FindHighestPriorityTarget_Statics::RTSTeamManager_eventFindHighestPriorityTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSTeamManager_FindHighestPriorityTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSTeamManager_FindHighestPriorityTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSTeamManager::execFindHighestPriorityTarget)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_TeamID);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_MaxRange);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ARTSBaseActor**)Z_Param__Result=P_THIS->FindHighestPriorityTarget(Z_Param_TeamID,Z_Param_Out_Location,Z_Param_MaxRange);
	P_NATIVE_END;
}
// ********** End Class URTSTeamManager Function FindHighestPriorityTarget *************************

// ********** Begin Class URTSTeamManager Function FindNearestEnemy ********************************
struct Z_Construct_UFunction_URTSTeamManager_FindNearestEnemy_Statics
{
	struct RTSTeamManager_eventFindNearestEnemy_Parms
	{
		int32 TeamID;
		FVector Location;
		float MaxRange;
		ARTSBaseActor* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Teams|AI" },
		{ "CPP_Default_MaxRange", "0.000000" },
		{ "ModuleRelativePath", "Public/RTSTeamManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxRange;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSTeamManager_FindNearestEnemy_Statics::NewProp_TeamID = { "TeamID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventFindNearestEnemy_Parms, TeamID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSTeamManager_FindNearestEnemy_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventFindNearestEnemy_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URTSTeamManager_FindNearestEnemy_Statics::NewProp_MaxRange = { "MaxRange", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventFindNearestEnemy_Parms, MaxRange), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSTeamManager_FindNearestEnemy_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventFindNearestEnemy_Parms, ReturnValue), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSTeamManager_FindNearestEnemy_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_FindNearestEnemy_Statics::NewProp_TeamID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_FindNearestEnemy_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_FindNearestEnemy_Statics::NewProp_MaxRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_FindNearestEnemy_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_FindNearestEnemy_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSTeamManager_FindNearestEnemy_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSTeamManager, nullptr, "FindNearestEnemy", Z_Construct_UFunction_URTSTeamManager_FindNearestEnemy_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_FindNearestEnemy_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSTeamManager_FindNearestEnemy_Statics::RTSTeamManager_eventFindNearestEnemy_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_FindNearestEnemy_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSTeamManager_FindNearestEnemy_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSTeamManager_FindNearestEnemy_Statics::RTSTeamManager_eventFindNearestEnemy_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSTeamManager_FindNearestEnemy()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSTeamManager_FindNearestEnemy_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSTeamManager::execFindNearestEnemy)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_TeamID);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_MaxRange);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ARTSBaseActor**)Z_Param__Result=P_THIS->FindNearestEnemy(Z_Param_TeamID,Z_Param_Out_Location,Z_Param_MaxRange);
	P_NATIVE_END;
}
// ********** End Class URTSTeamManager Function FindNearestEnemy **********************************

// ********** Begin Class URTSTeamManager Function GetActiveTeamCount ******************************
struct Z_Construct_UFunction_URTSTeamManager_GetActiveTeamCount_Statics
{
	struct RTSTeamManager_eventGetActiveTeamCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Teams" },
		{ "ModuleRelativePath", "Public/RTSTeamManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSTeamManager_GetActiveTeamCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventGetActiveTeamCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSTeamManager_GetActiveTeamCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_GetActiveTeamCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_GetActiveTeamCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSTeamManager_GetActiveTeamCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSTeamManager, nullptr, "GetActiveTeamCount", Z_Construct_UFunction_URTSTeamManager_GetActiveTeamCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_GetActiveTeamCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSTeamManager_GetActiveTeamCount_Statics::RTSTeamManager_eventGetActiveTeamCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_GetActiveTeamCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSTeamManager_GetActiveTeamCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSTeamManager_GetActiveTeamCount_Statics::RTSTeamManager_eventGetActiveTeamCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSTeamManager_GetActiveTeamCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSTeamManager_GetActiveTeamCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSTeamManager::execGetActiveTeamCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetActiveTeamCount();
	P_NATIVE_END;
}
// ********** End Class URTSTeamManager Function GetActiveTeamCount ********************************

// ********** Begin Class URTSTeamManager Function GetAllActorsOnTeam ******************************
struct Z_Construct_UFunction_URTSTeamManager_GetAllActorsOnTeam_Statics
{
	struct RTSTeamManager_eventGetAllActorsOnTeam_Parms
	{
		int32 TeamID;
		TArray<ARTSBaseActor*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Teams" },
		{ "ModuleRelativePath", "Public/RTSTeamManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamID;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSTeamManager_GetAllActorsOnTeam_Statics::NewProp_TeamID = { "TeamID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventGetAllActorsOnTeam_Parms, TeamID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSTeamManager_GetAllActorsOnTeam_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSTeamManager_GetAllActorsOnTeam_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventGetAllActorsOnTeam_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSTeamManager_GetAllActorsOnTeam_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_GetAllActorsOnTeam_Statics::NewProp_TeamID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_GetAllActorsOnTeam_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_GetAllActorsOnTeam_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_GetAllActorsOnTeam_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSTeamManager_GetAllActorsOnTeam_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSTeamManager, nullptr, "GetAllActorsOnTeam", Z_Construct_UFunction_URTSTeamManager_GetAllActorsOnTeam_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_GetAllActorsOnTeam_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSTeamManager_GetAllActorsOnTeam_Statics::RTSTeamManager_eventGetAllActorsOnTeam_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_GetAllActorsOnTeam_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSTeamManager_GetAllActorsOnTeam_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSTeamManager_GetAllActorsOnTeam_Statics::RTSTeamManager_eventGetAllActorsOnTeam_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSTeamManager_GetAllActorsOnTeam()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSTeamManager_GetAllActorsOnTeam_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSTeamManager::execGetAllActorsOnTeam)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_TeamID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<ARTSBaseActor*>*)Z_Param__Result=P_THIS->GetAllActorsOnTeam(Z_Param_TeamID);
	P_NATIVE_END;
}
// ********** End Class URTSTeamManager Function GetAllActorsOnTeam ********************************

// ********** Begin Class URTSTeamManager Function GetAllEnemyActors *******************************
struct Z_Construct_UFunction_URTSTeamManager_GetAllEnemyActors_Statics
{
	struct RTSTeamManager_eventGetAllEnemyActors_Parms
	{
		int32 TeamID;
		TArray<ARTSBaseActor*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Teams" },
		{ "ModuleRelativePath", "Public/RTSTeamManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamID;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSTeamManager_GetAllEnemyActors_Statics::NewProp_TeamID = { "TeamID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventGetAllEnemyActors_Parms, TeamID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSTeamManager_GetAllEnemyActors_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSTeamManager_GetAllEnemyActors_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventGetAllEnemyActors_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSTeamManager_GetAllEnemyActors_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_GetAllEnemyActors_Statics::NewProp_TeamID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_GetAllEnemyActors_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_GetAllEnemyActors_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_GetAllEnemyActors_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSTeamManager_GetAllEnemyActors_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSTeamManager, nullptr, "GetAllEnemyActors", Z_Construct_UFunction_URTSTeamManager_GetAllEnemyActors_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_GetAllEnemyActors_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSTeamManager_GetAllEnemyActors_Statics::RTSTeamManager_eventGetAllEnemyActors_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_GetAllEnemyActors_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSTeamManager_GetAllEnemyActors_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSTeamManager_GetAllEnemyActors_Statics::RTSTeamManager_eventGetAllEnemyActors_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSTeamManager_GetAllEnemyActors()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSTeamManager_GetAllEnemyActors_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSTeamManager::execGetAllEnemyActors)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_TeamID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<ARTSBaseActor*>*)Z_Param__Result=P_THIS->GetAllEnemyActors(Z_Param_TeamID);
	P_NATIVE_END;
}
// ********** End Class URTSTeamManager Function GetAllEnemyActors *********************************

// ********** Begin Class URTSTeamManager Function GetAllTeamIDs ***********************************
struct Z_Construct_UFunction_URTSTeamManager_GetAllTeamIDs_Statics
{
	struct RTSTeamManager_eventGetAllTeamIDs_Parms
	{
		TArray<int32> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Teams" },
		{ "ModuleRelativePath", "Public/RTSTeamManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSTeamManager_GetAllTeamIDs_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSTeamManager_GetAllTeamIDs_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventGetAllTeamIDs_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSTeamManager_GetAllTeamIDs_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_GetAllTeamIDs_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_GetAllTeamIDs_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_GetAllTeamIDs_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSTeamManager_GetAllTeamIDs_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSTeamManager, nullptr, "GetAllTeamIDs", Z_Construct_UFunction_URTSTeamManager_GetAllTeamIDs_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_GetAllTeamIDs_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSTeamManager_GetAllTeamIDs_Statics::RTSTeamManager_eventGetAllTeamIDs_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_GetAllTeamIDs_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSTeamManager_GetAllTeamIDs_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSTeamManager_GetAllTeamIDs_Statics::RTSTeamManager_eventGetAllTeamIDs_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSTeamManager_GetAllTeamIDs()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSTeamManager_GetAllTeamIDs_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSTeamManager::execGetAllTeamIDs)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<int32>*)Z_Param__Result=P_THIS->GetAllTeamIDs();
	P_NATIVE_END;
}
// ********** End Class URTSTeamManager Function GetAllTeamIDs *************************************

// ********** Begin Class URTSTeamManager Function GetAllTeams *************************************
struct Z_Construct_UFunction_URTSTeamManager_GetAllTeams_Statics
{
	struct RTSTeamManager_eventGetAllTeams_Parms
	{
		TArray<FRTSTeamInfo> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Teams" },
		{ "ModuleRelativePath", "Public/RTSTeamManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSTeamManager_GetAllTeams_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FRTSTeamInfo, METADATA_PARAMS(0, nullptr) }; // 1113985836
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSTeamManager_GetAllTeams_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventGetAllTeams_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1113985836
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSTeamManager_GetAllTeams_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_GetAllTeams_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_GetAllTeams_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_GetAllTeams_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSTeamManager_GetAllTeams_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSTeamManager, nullptr, "GetAllTeams", Z_Construct_UFunction_URTSTeamManager_GetAllTeams_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_GetAllTeams_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSTeamManager_GetAllTeams_Statics::RTSTeamManager_eventGetAllTeams_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_GetAllTeams_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSTeamManager_GetAllTeams_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSTeamManager_GetAllTeams_Statics::RTSTeamManager_eventGetAllTeams_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSTeamManager_GetAllTeams()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSTeamManager_GetAllTeams_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSTeamManager::execGetAllTeams)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FRTSTeamInfo>*)Z_Param__Result=P_THIS->GetAllTeams();
	P_NATIVE_END;
}
// ********** End Class URTSTeamManager Function GetAllTeams ***************************************

// ********** Begin Class URTSTeamManager Function GetTeamInfo *************************************
struct Z_Construct_UFunction_URTSTeamManager_GetTeamInfo_Statics
{
	struct RTSTeamManager_eventGetTeamInfo_Parms
	{
		int32 TeamID;
		FRTSTeamInfo ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Teams" },
		{ "ModuleRelativePath", "Public/RTSTeamManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSTeamManager_GetTeamInfo_Statics::NewProp_TeamID = { "TeamID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventGetTeamInfo_Parms, TeamID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSTeamManager_GetTeamInfo_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventGetTeamInfo_Parms, ReturnValue), Z_Construct_UScriptStruct_FRTSTeamInfo, METADATA_PARAMS(0, nullptr) }; // 1113985836
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSTeamManager_GetTeamInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_GetTeamInfo_Statics::NewProp_TeamID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_GetTeamInfo_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_GetTeamInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSTeamManager_GetTeamInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSTeamManager, nullptr, "GetTeamInfo", Z_Construct_UFunction_URTSTeamManager_GetTeamInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_GetTeamInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSTeamManager_GetTeamInfo_Statics::RTSTeamManager_eventGetTeamInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_GetTeamInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSTeamManager_GetTeamInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSTeamManager_GetTeamInfo_Statics::RTSTeamManager_eventGetTeamInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSTeamManager_GetTeamInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSTeamManager_GetTeamInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSTeamManager::execGetTeamInfo)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_TeamID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FRTSTeamInfo*)Z_Param__Result=P_THIS->GetTeamInfo(Z_Param_TeamID);
	P_NATIVE_END;
}
// ********** End Class URTSTeamManager Function GetTeamInfo ***************************************

// ********** Begin Class URTSTeamManager Function InitializeDefaultTeams **************************
struct Z_Construct_UFunction_URTSTeamManager_InitializeDefaultTeams_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Teams" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Utility functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSTeamManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Utility functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSTeamManager_InitializeDefaultTeams_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSTeamManager, nullptr, "InitializeDefaultTeams", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_InitializeDefaultTeams_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSTeamManager_InitializeDefaultTeams_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSTeamManager_InitializeDefaultTeams()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSTeamManager_InitializeDefaultTeams_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSTeamManager::execInitializeDefaultTeams)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeDefaultTeams();
	P_NATIVE_END;
}
// ********** End Class URTSTeamManager Function InitializeDefaultTeams ****************************

// ********** Begin Class URTSTeamManager Function IsTeamActive ************************************
struct Z_Construct_UFunction_URTSTeamManager_IsTeamActive_Statics
{
	struct RTSTeamManager_eventIsTeamActive_Parms
	{
		int32 TeamID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Teams" },
		{ "ModuleRelativePath", "Public/RTSTeamManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSTeamManager_IsTeamActive_Statics::NewProp_TeamID = { "TeamID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventIsTeamActive_Parms, TeamID), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSTeamManager_IsTeamActive_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSTeamManager_eventIsTeamActive_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSTeamManager_IsTeamActive_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSTeamManager_eventIsTeamActive_Parms), &Z_Construct_UFunction_URTSTeamManager_IsTeamActive_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSTeamManager_IsTeamActive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_IsTeamActive_Statics::NewProp_TeamID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_IsTeamActive_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_IsTeamActive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSTeamManager_IsTeamActive_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSTeamManager, nullptr, "IsTeamActive", Z_Construct_UFunction_URTSTeamManager_IsTeamActive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_IsTeamActive_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSTeamManager_IsTeamActive_Statics::RTSTeamManager_eventIsTeamActive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_IsTeamActive_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSTeamManager_IsTeamActive_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSTeamManager_IsTeamActive_Statics::RTSTeamManager_eventIsTeamActive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSTeamManager_IsTeamActive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSTeamManager_IsTeamActive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSTeamManager::execIsTeamActive)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_TeamID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsTeamActive(Z_Param_TeamID);
	P_NATIVE_END;
}
// ********** End Class URTSTeamManager Function IsTeamActive **************************************

// ********** Begin Class URTSTeamManager Function IsValidTarget ***********************************
struct Z_Construct_UFunction_URTSTeamManager_IsValidTarget_Statics
{
	struct RTSTeamManager_eventIsValidTarget_Parms
	{
		const ARTSBaseActor* Attacker;
		const ARTSBaseActor* Target;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Teams|AI" },
		{ "ModuleRelativePath", "Public/RTSTeamManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Attacker_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Target_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Attacker;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSTeamManager_IsValidTarget_Statics::NewProp_Attacker = { "Attacker", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventIsValidTarget_Parms, Attacker), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Attacker_MetaData), NewProp_Attacker_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSTeamManager_IsValidTarget_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventIsValidTarget_Parms, Target), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Target_MetaData), NewProp_Target_MetaData) };
void Z_Construct_UFunction_URTSTeamManager_IsValidTarget_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSTeamManager_eventIsValidTarget_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSTeamManager_IsValidTarget_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSTeamManager_eventIsValidTarget_Parms), &Z_Construct_UFunction_URTSTeamManager_IsValidTarget_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSTeamManager_IsValidTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_IsValidTarget_Statics::NewProp_Attacker,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_IsValidTarget_Statics::NewProp_Target,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_IsValidTarget_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_IsValidTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSTeamManager_IsValidTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSTeamManager, nullptr, "IsValidTarget", Z_Construct_UFunction_URTSTeamManager_IsValidTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_IsValidTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSTeamManager_IsValidTarget_Statics::RTSTeamManager_eventIsValidTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_IsValidTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSTeamManager_IsValidTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSTeamManager_IsValidTarget_Statics::RTSTeamManager_eventIsValidTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSTeamManager_IsValidTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSTeamManager_IsValidTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSTeamManager::execIsValidTarget)
{
	P_GET_OBJECT(ARTSBaseActor,Z_Param_Attacker);
	P_GET_OBJECT(ARTSBaseActor,Z_Param_Target);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsValidTarget(Z_Param_Attacker,Z_Param_Target);
	P_NATIVE_END;
}
// ********** End Class URTSTeamManager Function IsValidTarget *************************************

// ********** Begin Class URTSTeamManager Function RemoveTeam **************************************
struct Z_Construct_UFunction_URTSTeamManager_RemoveTeam_Statics
{
	struct RTSTeamManager_eventRemoveTeam_Parms
	{
		int32 TeamID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Teams" },
		{ "ModuleRelativePath", "Public/RTSTeamManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSTeamManager_RemoveTeam_Statics::NewProp_TeamID = { "TeamID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventRemoveTeam_Parms, TeamID), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSTeamManager_RemoveTeam_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSTeamManager_eventRemoveTeam_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSTeamManager_RemoveTeam_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSTeamManager_eventRemoveTeam_Parms), &Z_Construct_UFunction_URTSTeamManager_RemoveTeam_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSTeamManager_RemoveTeam_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_RemoveTeam_Statics::NewProp_TeamID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_RemoveTeam_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_RemoveTeam_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSTeamManager_RemoveTeam_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSTeamManager, nullptr, "RemoveTeam", Z_Construct_UFunction_URTSTeamManager_RemoveTeam_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_RemoveTeam_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSTeamManager_RemoveTeam_Statics::RTSTeamManager_eventRemoveTeam_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_RemoveTeam_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSTeamManager_RemoveTeam_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSTeamManager_RemoveTeam_Statics::RTSTeamManager_eventRemoveTeam_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSTeamManager_RemoveTeam()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSTeamManager_RemoveTeam_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSTeamManager::execRemoveTeam)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_TeamID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveTeam(Z_Param_TeamID);
	P_NATIVE_END;
}
// ********** End Class URTSTeamManager Function RemoveTeam ****************************************

// ********** Begin Class URTSTeamManager Function SetTeamActive ***********************************
struct Z_Construct_UFunction_URTSTeamManager_SetTeamActive_Statics
{
	struct RTSTeamManager_eventSetTeamActive_Parms
	{
		int32 TeamID;
		bool bActive;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Teams" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Team state functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSTeamManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Team state functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamID;
	static void NewProp_bActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bActive;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSTeamManager_SetTeamActive_Statics::NewProp_TeamID = { "TeamID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventSetTeamActive_Parms, TeamID), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSTeamManager_SetTeamActive_Statics::NewProp_bActive_SetBit(void* Obj)
{
	((RTSTeamManager_eventSetTeamActive_Parms*)Obj)->bActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSTeamManager_SetTeamActive_Statics::NewProp_bActive = { "bActive", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSTeamManager_eventSetTeamActive_Parms), &Z_Construct_UFunction_URTSTeamManager_SetTeamActive_Statics::NewProp_bActive_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSTeamManager_SetTeamActive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_SetTeamActive_Statics::NewProp_TeamID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_SetTeamActive_Statics::NewProp_bActive,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_SetTeamActive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSTeamManager_SetTeamActive_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSTeamManager, nullptr, "SetTeamActive", Z_Construct_UFunction_URTSTeamManager_SetTeamActive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_SetTeamActive_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSTeamManager_SetTeamActive_Statics::RTSTeamManager_eventSetTeamActive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_SetTeamActive_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSTeamManager_SetTeamActive_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSTeamManager_SetTeamActive_Statics::RTSTeamManager_eventSetTeamActive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSTeamManager_SetTeamActive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSTeamManager_SetTeamActive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSTeamManager::execSetTeamActive)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_TeamID);
	P_GET_UBOOL(Z_Param_bActive);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetTeamActive(Z_Param_TeamID,Z_Param_bActive);
	P_NATIVE_END;
}
// ********** End Class URTSTeamManager Function SetTeamActive *************************************

// ********** Begin Class URTSTeamManager Function SetTeamInfo *************************************
struct Z_Construct_UFunction_URTSTeamManager_SetTeamInfo_Statics
{
	struct RTSTeamManager_eventSetTeamInfo_Parms
	{
		int32 TeamID;
		FRTSTeamInfo NewTeamInfo;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Teams" },
		{ "ModuleRelativePath", "Public/RTSTeamManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewTeamInfo_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewTeamInfo;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSTeamManager_SetTeamInfo_Statics::NewProp_TeamID = { "TeamID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventSetTeamInfo_Parms, TeamID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSTeamManager_SetTeamInfo_Statics::NewProp_NewTeamInfo = { "NewTeamInfo", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSTeamManager_eventSetTeamInfo_Parms, NewTeamInfo), Z_Construct_UScriptStruct_FRTSTeamInfo, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewTeamInfo_MetaData), NewProp_NewTeamInfo_MetaData) }; // 1113985836
void Z_Construct_UFunction_URTSTeamManager_SetTeamInfo_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSTeamManager_eventSetTeamInfo_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSTeamManager_SetTeamInfo_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSTeamManager_eventSetTeamInfo_Parms), &Z_Construct_UFunction_URTSTeamManager_SetTeamInfo_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSTeamManager_SetTeamInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_SetTeamInfo_Statics::NewProp_TeamID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_SetTeamInfo_Statics::NewProp_NewTeamInfo,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSTeamManager_SetTeamInfo_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_SetTeamInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSTeamManager_SetTeamInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSTeamManager, nullptr, "SetTeamInfo", Z_Construct_UFunction_URTSTeamManager_SetTeamInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_SetTeamInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSTeamManager_SetTeamInfo_Statics::RTSTeamManager_eventSetTeamInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSTeamManager_SetTeamInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSTeamManager_SetTeamInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSTeamManager_SetTeamInfo_Statics::RTSTeamManager_eventSetTeamInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSTeamManager_SetTeamInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSTeamManager_SetTeamInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSTeamManager::execSetTeamInfo)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_TeamID);
	P_GET_STRUCT_REF(FRTSTeamInfo,Z_Param_Out_NewTeamInfo);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetTeamInfo(Z_Param_TeamID,Z_Param_Out_NewTeamInfo);
	P_NATIVE_END;
}
// ********** End Class URTSTeamManager Function SetTeamInfo ***************************************

// ********** Begin Class URTSTeamManager **********************************************************
void URTSTeamManager::StaticRegisterNativesURTSTeamManager()
{
	UClass* Class = URTSTeamManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AreActorsAllied", &URTSTeamManager::execAreActorsAllied },
		{ "AreActorsEnemies", &URTSTeamManager::execAreActorsEnemies },
		{ "AreTeamsAllied", &URTSTeamManager::execAreTeamsAllied },
		{ "AreTeamsEnemies", &URTSTeamManager::execAreTeamsEnemies },
		{ "CalculateTargetPriority", &URTSTeamManager::execCalculateTargetPriority },
		{ "ClearAllTeams", &URTSTeamManager::execClearAllTeams },
		{ "CreateTeam", &URTSTeamManager::execCreateTeam },
		{ "DoesTeamExist", &URTSTeamManager::execDoesTeamExist },
		{ "FindEnemiesInRange", &URTSTeamManager::execFindEnemiesInRange },
		{ "FindHighestPriorityTarget", &URTSTeamManager::execFindHighestPriorityTarget },
		{ "FindNearestEnemy", &URTSTeamManager::execFindNearestEnemy },
		{ "GetActiveTeamCount", &URTSTeamManager::execGetActiveTeamCount },
		{ "GetAllActorsOnTeam", &URTSTeamManager::execGetAllActorsOnTeam },
		{ "GetAllEnemyActors", &URTSTeamManager::execGetAllEnemyActors },
		{ "GetAllTeamIDs", &URTSTeamManager::execGetAllTeamIDs },
		{ "GetAllTeams", &URTSTeamManager::execGetAllTeams },
		{ "GetTeamInfo", &URTSTeamManager::execGetTeamInfo },
		{ "InitializeDefaultTeams", &URTSTeamManager::execInitializeDefaultTeams },
		{ "IsTeamActive", &URTSTeamManager::execIsTeamActive },
		{ "IsValidTarget", &URTSTeamManager::execIsValidTarget },
		{ "RemoveTeam", &URTSTeamManager::execRemoveTeam },
		{ "SetTeamActive", &URTSTeamManager::execSetTeamActive },
		{ "SetTeamInfo", &URTSTeamManager::execSetTeamInfo },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSTeamManager;
UClass* URTSTeamManager::GetPrivateStaticClass()
{
	using TClass = URTSTeamManager;
	if (!Z_Registration_Info_UClass_URTSTeamManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSTeamManager"),
			Z_Registration_Info_UClass_URTSTeamManager.InnerSingleton,
			StaticRegisterNativesURTSTeamManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSTeamManager.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSTeamManager_NoRegister()
{
	return URTSTeamManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSTeamManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Simple team manager subsystem for basic team functionality\n * All teams are enemies except for the same team ID\n */" },
#endif
		{ "IncludePath", "RTSTeamManager.h" },
		{ "ModuleRelativePath", "Public/RTSTeamManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Simple team manager subsystem for basic team functionality\nAll teams are enemies except for the same team ID" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Teams_MetaData[] = {
		{ "Category", "Teams" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Team data storage\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSTeamManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Team data storage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnTeamCreated_MetaData[] = {
		{ "Category", "RTS|Events" },
		{ "ModuleRelativePath", "Public/RTSTeamManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnTeamRemoved_MetaData[] = {
		{ "Category", "RTS|Events" },
		{ "ModuleRelativePath", "Public/RTSTeamManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Teams_ValueProp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Teams_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Teams;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnTeamCreated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnTeamRemoved;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_URTSTeamManager_AreActorsAllied, "AreActorsAllied" }, // 2364776423
		{ &Z_Construct_UFunction_URTSTeamManager_AreActorsEnemies, "AreActorsEnemies" }, // 1209088986
		{ &Z_Construct_UFunction_URTSTeamManager_AreTeamsAllied, "AreTeamsAllied" }, // 4097110432
		{ &Z_Construct_UFunction_URTSTeamManager_AreTeamsEnemies, "AreTeamsEnemies" }, // 149148091
		{ &Z_Construct_UFunction_URTSTeamManager_CalculateTargetPriority, "CalculateTargetPriority" }, // 320928214
		{ &Z_Construct_UFunction_URTSTeamManager_ClearAllTeams, "ClearAllTeams" }, // 2401691376
		{ &Z_Construct_UFunction_URTSTeamManager_CreateTeam, "CreateTeam" }, // 1056186598
		{ &Z_Construct_UFunction_URTSTeamManager_DoesTeamExist, "DoesTeamExist" }, // 275580111
		{ &Z_Construct_UFunction_URTSTeamManager_FindEnemiesInRange, "FindEnemiesInRange" }, // 986076234
		{ &Z_Construct_UFunction_URTSTeamManager_FindHighestPriorityTarget, "FindHighestPriorityTarget" }, // 1938154523
		{ &Z_Construct_UFunction_URTSTeamManager_FindNearestEnemy, "FindNearestEnemy" }, // 111027973
		{ &Z_Construct_UFunction_URTSTeamManager_GetActiveTeamCount, "GetActiveTeamCount" }, // 1995219736
		{ &Z_Construct_UFunction_URTSTeamManager_GetAllActorsOnTeam, "GetAllActorsOnTeam" }, // 486462480
		{ &Z_Construct_UFunction_URTSTeamManager_GetAllEnemyActors, "GetAllEnemyActors" }, // 3950419934
		{ &Z_Construct_UFunction_URTSTeamManager_GetAllTeamIDs, "GetAllTeamIDs" }, // 1318195000
		{ &Z_Construct_UFunction_URTSTeamManager_GetAllTeams, "GetAllTeams" }, // 1541874230
		{ &Z_Construct_UFunction_URTSTeamManager_GetTeamInfo, "GetTeamInfo" }, // 456586057
		{ &Z_Construct_UFunction_URTSTeamManager_InitializeDefaultTeams, "InitializeDefaultTeams" }, // 369729204
		{ &Z_Construct_UFunction_URTSTeamManager_IsTeamActive, "IsTeamActive" }, // 3637606681
		{ &Z_Construct_UFunction_URTSTeamManager_IsValidTarget, "IsValidTarget" }, // 471769588
		{ &Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamCreated__DelegateSignature, "OnTeamCreated__DelegateSignature" }, // 3462514941
		{ &Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamRemoved__DelegateSignature, "OnTeamRemoved__DelegateSignature" }, // 3860762241
		{ &Z_Construct_UFunction_URTSTeamManager_RemoveTeam, "RemoveTeam" }, // 2249836766
		{ &Z_Construct_UFunction_URTSTeamManager_SetTeamActive, "SetTeamActive" }, // 3217332325
		{ &Z_Construct_UFunction_URTSTeamManager_SetTeamInfo, "SetTeamInfo" }, // 2916449649
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSTeamManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_URTSTeamManager_Statics::NewProp_Teams_ValueProp = { "Teams", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FRTSTeamInfo, METADATA_PARAMS(0, nullptr) }; // 1113985836
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_URTSTeamManager_Statics::NewProp_Teams_Key_KeyProp = { "Teams_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_URTSTeamManager_Statics::NewProp_Teams = { "Teams", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSTeamManager, Teams), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Teams_MetaData), NewProp_Teams_MetaData) }; // 1113985836
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_URTSTeamManager_Statics::NewProp_OnTeamCreated = { "OnTeamCreated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSTeamManager, OnTeamCreated), Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamCreated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnTeamCreated_MetaData), NewProp_OnTeamCreated_MetaData) }; // 3462514941
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_URTSTeamManager_Statics::NewProp_OnTeamRemoved = { "OnTeamRemoved", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSTeamManager, OnTeamRemoved), Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamRemoved__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnTeamRemoved_MetaData), NewProp_OnTeamRemoved_MetaData) }; // 3860762241
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSTeamManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSTeamManager_Statics::NewProp_Teams_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSTeamManager_Statics::NewProp_Teams_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSTeamManager_Statics::NewProp_Teams,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSTeamManager_Statics::NewProp_OnTeamCreated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSTeamManager_Statics::NewProp_OnTeamRemoved,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSTeamManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSTeamManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UWorldSubsystem,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSTeamManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSTeamManager_Statics::ClassParams = {
	&URTSTeamManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_URTSTeamManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSTeamManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSTeamManager_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSTeamManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSTeamManager()
{
	if (!Z_Registration_Info_UClass_URTSTeamManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSTeamManager.OuterSingleton, Z_Construct_UClass_URTSTeamManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSTeamManager.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSTeamManager);
URTSTeamManager::~URTSTeamManager() {}
// ********** End Class URTSTeamManager ************************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSTeamManager_h__Script_ArmorWars_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FRTSTeamInfo::StaticStruct, Z_Construct_UScriptStruct_FRTSTeamInfo_Statics::NewStructOps, TEXT("RTSTeamInfo"), &Z_Registration_Info_UScriptStruct_FRTSTeamInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FRTSTeamInfo), 1113985836U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_URTSTeamManager, URTSTeamManager::StaticClass, TEXT("URTSTeamManager"), &Z_Registration_Info_UClass_URTSTeamManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSTeamManager), 2538783888U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSTeamManager_h__Script_ArmorWars_324136940(TEXT("/Script/ArmorWars"),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSTeamManager_h__Script_ArmorWars_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSTeamManager_h__Script_ArmorWars_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSTeamManager_h__Script_ArmorWars_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSTeamManager_h__Script_ArmorWars_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
