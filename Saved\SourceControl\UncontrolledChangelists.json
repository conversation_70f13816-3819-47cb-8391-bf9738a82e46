{"version": 1, "changelists": [{"guid": "00000000000000000000000000000000", "description": "Default Uncontrolled Changelist", "files": ["F:/ArmorWars/Content/GameInit/ArmorWarsGameInstance.uasset", "F:/ArmorWars/Content/GameInit/ArmorWarsGameMode.uasset", "F:/ArmorWars/Content/GameInit/ArmorWarsPlayerController.uasset", "F:/ArmorWars/Content/Models/RTSTank3.uasset", "F:/ArmorWars/Content/Models/AltColor1.uasset", "F:/ArmorWars/Content/Models/AltColor2.uasset", "F:/ArmorWars/Content/Models/AltColor3.uasset", "F:/ArmorWars/Content/Models/Barrel.uasset", "F:/ArmorWars/Content/Models/ColorStripes.uasset", "F:/ArmorWars/Content/Models/Engine.uasset", "F:/ArmorWars/Content/Models/Hatch.uasset", "F:/ArmorWars/Content/Models/RTSTank3_PhysicsAsset.uasset", "F:/ArmorWars/Content/Models/RTSTank3_Skeleton.uasset", "F:/ArmorWars/Content/Models/TankBase.uasset", "F:/ArmorWars/Content/Models/TankRotator.uasset", "F:/ArmorWars/Content/Models/Tracks.uasset", "F:/ArmorWars/Content/Models/Turret.uasset", "F:/ArmorWars/Content/Models/Test/Cannonbase.uasset", "F:/ArmorWars/Content/Models/Test/TankBase.uasset", "F:/ArmorWars/Content/Models/Test/Turret.uasset", "F:/ArmorWars/Content/UnitBPs/BP_MediumTank.uasset", "F:/ArmorWars/Content/UnitBPs/BPC_MedTankTurret.uasset", "F:/ArmorWars/Content/UnitBPs/MedTankProjectile.uasset", "F:/ArmorWars/Content/GameInit/ArmorWarsHud.uasset", "F:/ArmorWars/Content/GameInit/EnhancedInput/ArmorWarsIMC.uasset", "F:/ArmorWars/Content/GameInit/ArmorWarsHUDWidget.uasset", "F:/ArmorWars/Content/GameInit/ArmorWarsGameState.uasset", "F:/ArmorWars/Content/GameInit/EnhancedInput/PrimaryAction.uasset", "F:/ArmorWars/Content/GameInit/EnhancedInput/SecondaryAction.uasset", "F:/ArmorWars/Content/GameInit/EnhancedInput/CameraMoveForward.uasset", "F:/ArmorWars/Content/GameInit/EnhancedInput/CameraMoveRight.uasset", "F:/ArmorWars/Content/GameInit/EnhancedInput/CameraZoom.uasset", "F:/ArmorWars/Content/GameInit/EnhancedInput/CameraRotate.uasset", "F:/ArmorWars/Content/GridBasedBuilder/Demo/BP_Demo_Camera.uasset", "F:/ArmorWars/Content/GameInit/BP_RTSPawn.uasset", "F:/ArmorWars/Content/UnitBPs/MedTankAIcontroller.uasset", "F:/ArmorWars/Content/TeamColors.uasset", "F:/ArmorWars/Content/DT_TeamColor.uasset", "F:/ArmorWars/Content/GameInit/TestBB.uasset", "F:/ArmorWars/Content/GameInit/RTSTEST.uasset", "F:/ArmorWars/Content/GameInit/ArmorWarsCameraPawn.uasset", "F:/ArmorWars/Content/GameInit/EnhancedInput/MouseClick.uasset", "F:/ArmorWars/Content/GameInit/EnhancedInput/RightMouseClick.uasset", "F:/ArmorWars/Content/Models/SM_MediumTank.uasset", "F:/ArmorWars/Content/RTSLandUnit.uasset", "F:/ArmorWars/Content/AltColor1.uasset", "F:/ArmorWars/Content/AltColor2.uasset", "F:/ArmorWars/Content/AltColor3.uasset", "F:/ArmorWars/Content/Barrel.uasset", "F:/ArmorWars/Content/Cannonbase.uasset", "F:/ArmorWars/Content/ColorStripes.uasset", "F:/ArmorWars/Content/Engine.uasset", "F:/ArmorWars/Content/Hatch.uasset", "F:/ArmorWars/Content/TankBase.uasset", "F:/ArmorWars/Content/TankBase1.uasset", "F:/ArmorWars/Content/TankRotator.uasset", "F:/ArmorWars/Content/TankRotator1.uasset", "F:/ArmorWars/Content/Tracks.uasset", "F:/ArmorWars/Content/Turret.uasset", "F:/ArmorWars/Content/Turret1.uasset", "F:/ArmorWars/Content/TankTurret.uasset"]}]}