// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RTSBehaviorNodes.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeRTSBehaviorNodes() {}

// ********** Begin Cross Module References ********************************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSAttackTargetTask();
ARMORWARS_API UClass* Z_Construct_UClass_URTSAttackTargetTask_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSCommandInterruptDecorator();
ARMORWARS_API UClass* Z_Construct_UClass_URTSCommandInterruptDecorator_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSConditionNode();
ARMORWARS_API UClass* Z_Construct_UClass_URTSCooldownDecorator();
ARMORWARS_API UClass* Z_Construct_UClass_URTSCooldownDecorator_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSDecoratorNode();
ARMORWARS_API UClass* Z_Construct_UClass_URTSEnemiesInRangeCondition();
ARMORWARS_API UClass* Z_Construct_UClass_URTSEnemiesInRangeCondition_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSEnemyNearbyCondition();
ARMORWARS_API UClass* Z_Construct_UClass_URTSEnemyNearbyCondition_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSEnhancedCombatTask();
ARMORWARS_API UClass* Z_Construct_UClass_URTSEnhancedCombatTask_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSEnhancedMoveTask();
ARMORWARS_API UClass* Z_Construct_UClass_URTSEnhancedMoveTask_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSExecuteCommandTaskNode();
ARMORWARS_API UClass* Z_Construct_UClass_URTSExecuteCommandTaskNode_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSFindEnemyTask();
ARMORWARS_API UClass* Z_Construct_UClass_URTSFindEnemyTask_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSFollowFormationTask();
ARMORWARS_API UClass* Z_Construct_UClass_URTSFollowFormationTask_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSHasCommandsCondition();
ARMORWARS_API UClass* Z_Construct_UClass_URTSHasCommandsCondition_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSHasValidTargetCondition();
ARMORWARS_API UClass* Z_Construct_UClass_URTSHasValidTargetCondition_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSHealthCondition();
ARMORWARS_API UClass* Z_Construct_UClass_URTSHealthCondition_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSHealthLowCondition();
ARMORWARS_API UClass* Z_Construct_UClass_URTSHealthLowCondition_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSInFormationCondition();
ARMORWARS_API UClass* Z_Construct_UClass_URTSInFormationCondition_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSInverterDecorator();
ARMORWARS_API UClass* Z_Construct_UClass_URTSInverterDecorator_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSMoveToLocationTask();
ARMORWARS_API UClass* Z_Construct_UClass_URTSMoveToLocationTask_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSPatrolTask();
ARMORWARS_API UClass* Z_Construct_UClass_URTSPatrolTask_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSRepeaterDecorator();
ARMORWARS_API UClass* Z_Construct_UClass_URTSRepeaterDecorator_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSStatBasedDecorator();
ARMORWARS_API UClass* Z_Construct_UClass_URTSStatBasedDecorator_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSTaskNode();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSCommandPriority();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
UPackage* Z_Construct_UPackage__Script_ArmorWars();
// ********** End Cross Module References **********************************************************

// ********** Begin Class URTSMoveToLocationTask ***************************************************
void URTSMoveToLocationTask::StaticRegisterNativesURTSMoveToLocationTask()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSMoveToLocationTask;
UClass* URTSMoveToLocationTask::GetPrivateStaticClass()
{
	using TClass = URTSMoveToLocationTask;
	if (!Z_Registration_Info_UClass_URTSMoveToLocationTask.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSMoveToLocationTask"),
			Z_Registration_Info_UClass_URTSMoveToLocationTask.InnerSingleton,
			StaticRegisterNativesURTSMoveToLocationTask,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSMoveToLocationTask.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSMoveToLocationTask_NoRegister()
{
	return URTSMoveToLocationTask::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSMoveToLocationTask_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Task node for moving to a location\n */" },
#endif
		{ "IncludePath", "RTSBehaviorNodes.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Task node for moving to a location" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetLocationKey_MetaData[] = {
		{ "Category", "Move Task" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AcceptanceRadius_MetaData[] = {
		{ "Category", "Move Task" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TargetLocationKey;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AcceptanceRadius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSMoveToLocationTask>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_URTSMoveToLocationTask_Statics::NewProp_TargetLocationKey = { "TargetLocationKey", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSMoveToLocationTask, TargetLocationKey), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetLocationKey_MetaData), NewProp_TargetLocationKey_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSMoveToLocationTask_Statics::NewProp_AcceptanceRadius = { "AcceptanceRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSMoveToLocationTask, AcceptanceRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AcceptanceRadius_MetaData), NewProp_AcceptanceRadius_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSMoveToLocationTask_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSMoveToLocationTask_Statics::NewProp_TargetLocationKey,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSMoveToLocationTask_Statics::NewProp_AcceptanceRadius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSMoveToLocationTask_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSMoveToLocationTask_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_URTSTaskNode,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSMoveToLocationTask_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSMoveToLocationTask_Statics::ClassParams = {
	&URTSMoveToLocationTask::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_URTSMoveToLocationTask_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSMoveToLocationTask_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSMoveToLocationTask_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSMoveToLocationTask_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSMoveToLocationTask()
{
	if (!Z_Registration_Info_UClass_URTSMoveToLocationTask.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSMoveToLocationTask.OuterSingleton, Z_Construct_UClass_URTSMoveToLocationTask_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSMoveToLocationTask.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSMoveToLocationTask);
URTSMoveToLocationTask::~URTSMoveToLocationTask() {}
// ********** End Class URTSMoveToLocationTask *****************************************************

// ********** Begin Class URTSAttackTargetTask *****************************************************
void URTSAttackTargetTask::StaticRegisterNativesURTSAttackTargetTask()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSAttackTargetTask;
UClass* URTSAttackTargetTask::GetPrivateStaticClass()
{
	using TClass = URTSAttackTargetTask;
	if (!Z_Registration_Info_UClass_URTSAttackTargetTask.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSAttackTargetTask"),
			Z_Registration_Info_UClass_URTSAttackTargetTask.InnerSingleton,
			StaticRegisterNativesURTSAttackTargetTask,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSAttackTargetTask.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSAttackTargetTask_NoRegister()
{
	return URTSAttackTargetTask::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSAttackTargetTask_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Task node for attacking a target\n */" },
#endif
		{ "IncludePath", "RTSBehaviorNodes.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Task node for attacking a target" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetKey_MetaData[] = {
		{ "Category", "Attack Task" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackDuration_MetaData[] = {
		{ "Category", "Attack Task" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TargetKey;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AttackDuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSAttackTargetTask>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_URTSAttackTargetTask_Statics::NewProp_TargetKey = { "TargetKey", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSAttackTargetTask, TargetKey), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetKey_MetaData), NewProp_TargetKey_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSAttackTargetTask_Statics::NewProp_AttackDuration = { "AttackDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSAttackTargetTask, AttackDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackDuration_MetaData), NewProp_AttackDuration_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSAttackTargetTask_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSAttackTargetTask_Statics::NewProp_TargetKey,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSAttackTargetTask_Statics::NewProp_AttackDuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSAttackTargetTask_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSAttackTargetTask_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_URTSTaskNode,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSAttackTargetTask_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSAttackTargetTask_Statics::ClassParams = {
	&URTSAttackTargetTask::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_URTSAttackTargetTask_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSAttackTargetTask_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSAttackTargetTask_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSAttackTargetTask_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSAttackTargetTask()
{
	if (!Z_Registration_Info_UClass_URTSAttackTargetTask.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSAttackTargetTask.OuterSingleton, Z_Construct_UClass_URTSAttackTargetTask_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSAttackTargetTask.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSAttackTargetTask);
URTSAttackTargetTask::~URTSAttackTargetTask() {}
// ********** End Class URTSAttackTargetTask *******************************************************

// ********** Begin Class URTSPatrolTask ***********************************************************
void URTSPatrolTask::StaticRegisterNativesURTSPatrolTask()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSPatrolTask;
UClass* URTSPatrolTask::GetPrivateStaticClass()
{
	using TClass = URTSPatrolTask;
	if (!Z_Registration_Info_UClass_URTSPatrolTask.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSPatrolTask"),
			Z_Registration_Info_UClass_URTSPatrolTask.InnerSingleton,
			StaticRegisterNativesURTSPatrolTask,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSPatrolTask.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSPatrolTask_NoRegister()
{
	return URTSPatrolTask::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSPatrolTask_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Task node for patrolling between points\n */" },
#endif
		{ "IncludePath", "RTSBehaviorNodes.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Task node for patrolling between points" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PatrolPoints_MetaData[] = {
		{ "Category", "Patrol Task" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLoopPatrol_MetaData[] = {
		{ "Category", "Patrol Task" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WaitTimeAtPoint_MetaData[] = {
		{ "Category", "Patrol Task" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PatrolPoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PatrolPoints;
	static void NewProp_bLoopPatrol_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLoopPatrol;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WaitTimeAtPoint;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSPatrolTask>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_URTSPatrolTask_Statics::NewProp_PatrolPoints_Inner = { "PatrolPoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_URTSPatrolTask_Statics::NewProp_PatrolPoints = { "PatrolPoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSPatrolTask, PatrolPoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PatrolPoints_MetaData), NewProp_PatrolPoints_MetaData) };
void Z_Construct_UClass_URTSPatrolTask_Statics::NewProp_bLoopPatrol_SetBit(void* Obj)
{
	((URTSPatrolTask*)Obj)->bLoopPatrol = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSPatrolTask_Statics::NewProp_bLoopPatrol = { "bLoopPatrol", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSPatrolTask), &Z_Construct_UClass_URTSPatrolTask_Statics::NewProp_bLoopPatrol_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLoopPatrol_MetaData), NewProp_bLoopPatrol_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSPatrolTask_Statics::NewProp_WaitTimeAtPoint = { "WaitTimeAtPoint", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSPatrolTask, WaitTimeAtPoint), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WaitTimeAtPoint_MetaData), NewProp_WaitTimeAtPoint_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSPatrolTask_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSPatrolTask_Statics::NewProp_PatrolPoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSPatrolTask_Statics::NewProp_PatrolPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSPatrolTask_Statics::NewProp_bLoopPatrol,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSPatrolTask_Statics::NewProp_WaitTimeAtPoint,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSPatrolTask_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSPatrolTask_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_URTSTaskNode,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSPatrolTask_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSPatrolTask_Statics::ClassParams = {
	&URTSPatrolTask::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_URTSPatrolTask_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSPatrolTask_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSPatrolTask_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSPatrolTask_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSPatrolTask()
{
	if (!Z_Registration_Info_UClass_URTSPatrolTask.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSPatrolTask.OuterSingleton, Z_Construct_UClass_URTSPatrolTask_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSPatrolTask.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSPatrolTask);
URTSPatrolTask::~URTSPatrolTask() {}
// ********** End Class URTSPatrolTask *************************************************************

// ********** Begin Class URTSFindEnemyTask ********************************************************
void URTSFindEnemyTask::StaticRegisterNativesURTSFindEnemyTask()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSFindEnemyTask;
UClass* URTSFindEnemyTask::GetPrivateStaticClass()
{
	using TClass = URTSFindEnemyTask;
	if (!Z_Registration_Info_UClass_URTSFindEnemyTask.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSFindEnemyTask"),
			Z_Registration_Info_UClass_URTSFindEnemyTask.InnerSingleton,
			StaticRegisterNativesURTSFindEnemyTask,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSFindEnemyTask.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSFindEnemyTask_NoRegister()
{
	return URTSFindEnemyTask::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSFindEnemyTask_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Task node for finding and engaging enemies\n */" },
#endif
		{ "IncludePath", "RTSBehaviorNodes.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Task node for finding and engaging enemies" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SearchRadius_MetaData[] = {
		{ "Category", "Find Enemy Task" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoundEnemyKey_MetaData[] = {
		{ "Category", "Find Enemy Task" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SearchRadius;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoundEnemyKey;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSFindEnemyTask>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSFindEnemyTask_Statics::NewProp_SearchRadius = { "SearchRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSFindEnemyTask, SearchRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SearchRadius_MetaData), NewProp_SearchRadius_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_URTSFindEnemyTask_Statics::NewProp_FoundEnemyKey = { "FoundEnemyKey", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSFindEnemyTask, FoundEnemyKey), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoundEnemyKey_MetaData), NewProp_FoundEnemyKey_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSFindEnemyTask_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSFindEnemyTask_Statics::NewProp_SearchRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSFindEnemyTask_Statics::NewProp_FoundEnemyKey,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSFindEnemyTask_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSFindEnemyTask_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_URTSTaskNode,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSFindEnemyTask_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSFindEnemyTask_Statics::ClassParams = {
	&URTSFindEnemyTask::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_URTSFindEnemyTask_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSFindEnemyTask_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSFindEnemyTask_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSFindEnemyTask_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSFindEnemyTask()
{
	if (!Z_Registration_Info_UClass_URTSFindEnemyTask.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSFindEnemyTask.OuterSingleton, Z_Construct_UClass_URTSFindEnemyTask_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSFindEnemyTask.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSFindEnemyTask);
URTSFindEnemyTask::~URTSFindEnemyTask() {}
// ********** End Class URTSFindEnemyTask **********************************************************

// ********** Begin Class URTSHealthLowCondition ***************************************************
void URTSHealthLowCondition::StaticRegisterNativesURTSHealthLowCondition()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSHealthLowCondition;
UClass* URTSHealthLowCondition::GetPrivateStaticClass()
{
	using TClass = URTSHealthLowCondition;
	if (!Z_Registration_Info_UClass_URTSHealthLowCondition.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSHealthLowCondition"),
			Z_Registration_Info_UClass_URTSHealthLowCondition.InnerSingleton,
			StaticRegisterNativesURTSHealthLowCondition,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSHealthLowCondition.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSHealthLowCondition_NoRegister()
{
	return URTSHealthLowCondition::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSHealthLowCondition_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Condition node for checking if health is low\n */" },
#endif
		{ "IncludePath", "RTSBehaviorNodes.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Condition node for checking if health is low" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealthThreshold_MetaData[] = {
		{ "Category", "Health Condition" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HealthThreshold;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSHealthLowCondition>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSHealthLowCondition_Statics::NewProp_HealthThreshold = { "HealthThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSHealthLowCondition, HealthThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealthThreshold_MetaData), NewProp_HealthThreshold_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSHealthLowCondition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSHealthLowCondition_Statics::NewProp_HealthThreshold,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSHealthLowCondition_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSHealthLowCondition_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_URTSConditionNode,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSHealthLowCondition_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSHealthLowCondition_Statics::ClassParams = {
	&URTSHealthLowCondition::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_URTSHealthLowCondition_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSHealthLowCondition_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSHealthLowCondition_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSHealthLowCondition_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSHealthLowCondition()
{
	if (!Z_Registration_Info_UClass_URTSHealthLowCondition.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSHealthLowCondition.OuterSingleton, Z_Construct_UClass_URTSHealthLowCondition_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSHealthLowCondition.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSHealthLowCondition);
URTSHealthLowCondition::~URTSHealthLowCondition() {}
// ********** End Class URTSHealthLowCondition *****************************************************

// ********** Begin Class URTSEnemyNearbyCondition *************************************************
void URTSEnemyNearbyCondition::StaticRegisterNativesURTSEnemyNearbyCondition()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSEnemyNearbyCondition;
UClass* URTSEnemyNearbyCondition::GetPrivateStaticClass()
{
	using TClass = URTSEnemyNearbyCondition;
	if (!Z_Registration_Info_UClass_URTSEnemyNearbyCondition.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSEnemyNearbyCondition"),
			Z_Registration_Info_UClass_URTSEnemyNearbyCondition.InnerSingleton,
			StaticRegisterNativesURTSEnemyNearbyCondition,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSEnemyNearbyCondition.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSEnemyNearbyCondition_NoRegister()
{
	return URTSEnemyNearbyCondition::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSEnemyNearbyCondition_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Condition node for checking if enemies are nearby\n */" },
#endif
		{ "IncludePath", "RTSBehaviorNodes.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Condition node for checking if enemies are nearby" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DetectionRadius_MetaData[] = {
		{ "Category", "Enemy Condition" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DetectionRadius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSEnemyNearbyCondition>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSEnemyNearbyCondition_Statics::NewProp_DetectionRadius = { "DetectionRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSEnemyNearbyCondition, DetectionRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DetectionRadius_MetaData), NewProp_DetectionRadius_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSEnemyNearbyCondition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSEnemyNearbyCondition_Statics::NewProp_DetectionRadius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSEnemyNearbyCondition_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSEnemyNearbyCondition_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_URTSConditionNode,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSEnemyNearbyCondition_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSEnemyNearbyCondition_Statics::ClassParams = {
	&URTSEnemyNearbyCondition::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_URTSEnemyNearbyCondition_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSEnemyNearbyCondition_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSEnemyNearbyCondition_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSEnemyNearbyCondition_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSEnemyNearbyCondition()
{
	if (!Z_Registration_Info_UClass_URTSEnemyNearbyCondition.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSEnemyNearbyCondition.OuterSingleton, Z_Construct_UClass_URTSEnemyNearbyCondition_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSEnemyNearbyCondition.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSEnemyNearbyCondition);
URTSEnemyNearbyCondition::~URTSEnemyNearbyCondition() {}
// ********** End Class URTSEnemyNearbyCondition ***************************************************

// ********** Begin Class URTSHasValidTargetCondition **********************************************
void URTSHasValidTargetCondition::StaticRegisterNativesURTSHasValidTargetCondition()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSHasValidTargetCondition;
UClass* URTSHasValidTargetCondition::GetPrivateStaticClass()
{
	using TClass = URTSHasValidTargetCondition;
	if (!Z_Registration_Info_UClass_URTSHasValidTargetCondition.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSHasValidTargetCondition"),
			Z_Registration_Info_UClass_URTSHasValidTargetCondition.InnerSingleton,
			StaticRegisterNativesURTSHasValidTargetCondition,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSHasValidTargetCondition.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSHasValidTargetCondition_NoRegister()
{
	return URTSHasValidTargetCondition::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSHasValidTargetCondition_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Condition node for checking if a target is valid\n */" },
#endif
		{ "IncludePath", "RTSBehaviorNodes.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Condition node for checking if a target is valid" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetKey_MetaData[] = {
		{ "Category", "Target Condition" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TargetKey;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSHasValidTargetCondition>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_URTSHasValidTargetCondition_Statics::NewProp_TargetKey = { "TargetKey", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSHasValidTargetCondition, TargetKey), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetKey_MetaData), NewProp_TargetKey_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSHasValidTargetCondition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSHasValidTargetCondition_Statics::NewProp_TargetKey,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSHasValidTargetCondition_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSHasValidTargetCondition_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_URTSConditionNode,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSHasValidTargetCondition_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSHasValidTargetCondition_Statics::ClassParams = {
	&URTSHasValidTargetCondition::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_URTSHasValidTargetCondition_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSHasValidTargetCondition_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSHasValidTargetCondition_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSHasValidTargetCondition_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSHasValidTargetCondition()
{
	if (!Z_Registration_Info_UClass_URTSHasValidTargetCondition.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSHasValidTargetCondition.OuterSingleton, Z_Construct_UClass_URTSHasValidTargetCondition_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSHasValidTargetCondition.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSHasValidTargetCondition);
URTSHasValidTargetCondition::~URTSHasValidTargetCondition() {}
// ********** End Class URTSHasValidTargetCondition ************************************************

// ********** Begin Class URTSInverterDecorator ****************************************************
void URTSInverterDecorator::StaticRegisterNativesURTSInverterDecorator()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSInverterDecorator;
UClass* URTSInverterDecorator::GetPrivateStaticClass()
{
	using TClass = URTSInverterDecorator;
	if (!Z_Registration_Info_UClass_URTSInverterDecorator.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSInverterDecorator"),
			Z_Registration_Info_UClass_URTSInverterDecorator.InnerSingleton,
			StaticRegisterNativesURTSInverterDecorator,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSInverterDecorator.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSInverterDecorator_NoRegister()
{
	return URTSInverterDecorator::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSInverterDecorator_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Decorator node that inverts the result of its child\n */" },
#endif
		{ "IncludePath", "RTSBehaviorNodes.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Decorator node that inverts the result of its child" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSInverterDecorator>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_URTSInverterDecorator_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_URTSDecoratorNode,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSInverterDecorator_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSInverterDecorator_Statics::ClassParams = {
	&URTSInverterDecorator::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSInverterDecorator_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSInverterDecorator_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSInverterDecorator()
{
	if (!Z_Registration_Info_UClass_URTSInverterDecorator.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSInverterDecorator.OuterSingleton, Z_Construct_UClass_URTSInverterDecorator_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSInverterDecorator.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSInverterDecorator);
URTSInverterDecorator::~URTSInverterDecorator() {}
// ********** End Class URTSInverterDecorator ******************************************************

// ********** Begin Class URTSRepeaterDecorator ****************************************************
void URTSRepeaterDecorator::StaticRegisterNativesURTSRepeaterDecorator()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSRepeaterDecorator;
UClass* URTSRepeaterDecorator::GetPrivateStaticClass()
{
	using TClass = URTSRepeaterDecorator;
	if (!Z_Registration_Info_UClass_URTSRepeaterDecorator.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSRepeaterDecorator"),
			Z_Registration_Info_UClass_URTSRepeaterDecorator.InnerSingleton,
			StaticRegisterNativesURTSRepeaterDecorator,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSRepeaterDecorator.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSRepeaterDecorator_NoRegister()
{
	return URTSRepeaterDecorator::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSRepeaterDecorator_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Decorator node that repeats its child a certain number of times\n */" },
#endif
		{ "IncludePath", "RTSBehaviorNodes.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Decorator node that repeats its child a certain number of times" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RepeatCount_MetaData[] = {
		{ "Category", "Repeater" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_RepeatCount;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSRepeaterDecorator>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_URTSRepeaterDecorator_Statics::NewProp_RepeatCount = { "RepeatCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSRepeaterDecorator, RepeatCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RepeatCount_MetaData), NewProp_RepeatCount_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSRepeaterDecorator_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSRepeaterDecorator_Statics::NewProp_RepeatCount,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSRepeaterDecorator_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSRepeaterDecorator_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_URTSDecoratorNode,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSRepeaterDecorator_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSRepeaterDecorator_Statics::ClassParams = {
	&URTSRepeaterDecorator::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_URTSRepeaterDecorator_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSRepeaterDecorator_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSRepeaterDecorator_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSRepeaterDecorator_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSRepeaterDecorator()
{
	if (!Z_Registration_Info_UClass_URTSRepeaterDecorator.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSRepeaterDecorator.OuterSingleton, Z_Construct_UClass_URTSRepeaterDecorator_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSRepeaterDecorator.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSRepeaterDecorator);
URTSRepeaterDecorator::~URTSRepeaterDecorator() {}
// ********** End Class URTSRepeaterDecorator ******************************************************

// ********** Begin Class URTSCooldownDecorator ****************************************************
void URTSCooldownDecorator::StaticRegisterNativesURTSCooldownDecorator()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSCooldownDecorator;
UClass* URTSCooldownDecorator::GetPrivateStaticClass()
{
	using TClass = URTSCooldownDecorator;
	if (!Z_Registration_Info_UClass_URTSCooldownDecorator.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSCooldownDecorator"),
			Z_Registration_Info_UClass_URTSCooldownDecorator.InnerSingleton,
			StaticRegisterNativesURTSCooldownDecorator,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSCooldownDecorator.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSCooldownDecorator_NoRegister()
{
	return URTSCooldownDecorator::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSCooldownDecorator_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Decorator node that adds a cooldown to its child\n */" },
#endif
		{ "IncludePath", "RTSBehaviorNodes.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Decorator node that adds a cooldown to its child" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CooldownTime_MetaData[] = {
		{ "Category", "Cooldown" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CooldownTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSCooldownDecorator>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSCooldownDecorator_Statics::NewProp_CooldownTime = { "CooldownTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCooldownDecorator, CooldownTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CooldownTime_MetaData), NewProp_CooldownTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSCooldownDecorator_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCooldownDecorator_Statics::NewProp_CooldownTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSCooldownDecorator_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSCooldownDecorator_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_URTSDecoratorNode,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSCooldownDecorator_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSCooldownDecorator_Statics::ClassParams = {
	&URTSCooldownDecorator::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_URTSCooldownDecorator_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSCooldownDecorator_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSCooldownDecorator_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSCooldownDecorator_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSCooldownDecorator()
{
	if (!Z_Registration_Info_UClass_URTSCooldownDecorator.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSCooldownDecorator.OuterSingleton, Z_Construct_UClass_URTSCooldownDecorator_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSCooldownDecorator.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSCooldownDecorator);
URTSCooldownDecorator::~URTSCooldownDecorator() {}
// ********** End Class URTSCooldownDecorator ******************************************************

// ********** Begin Class URTSExecuteCommandTaskNode ***********************************************
void URTSExecuteCommandTaskNode::StaticRegisterNativesURTSExecuteCommandTaskNode()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSExecuteCommandTaskNode;
UClass* URTSExecuteCommandTaskNode::GetPrivateStaticClass()
{
	using TClass = URTSExecuteCommandTaskNode;
	if (!Z_Registration_Info_UClass_URTSExecuteCommandTaskNode.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSExecuteCommandTaskNode"),
			Z_Registration_Info_UClass_URTSExecuteCommandTaskNode.InnerSingleton,
			StaticRegisterNativesURTSExecuteCommandTaskNode,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSExecuteCommandTaskNode.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSExecuteCommandTaskNode_NoRegister()
{
	return URTSExecuteCommandTaskNode::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSExecuteCommandTaskNode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Task node for executing player/AI commands\n * Highest priority task that overrides autonomous behavior\n */" },
#endif
		{ "IncludePath", "RTSBehaviorNodes.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Task node for executing player/AI commands\nHighest priority task that overrides autonomous behavior" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinimumPriority_MetaData[] = {
		{ "Category", "Commands" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Command execution parameters\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Command execution parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAllowCommandInterruption_MetaData[] = {
		{ "Category", "Commands" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_MinimumPriority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MinimumPriority;
	static void NewProp_bAllowCommandInterruption_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAllowCommandInterruption;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSExecuteCommandTaskNode>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_URTSExecuteCommandTaskNode_Statics::NewProp_MinimumPriority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_URTSExecuteCommandTaskNode_Statics::NewProp_MinimumPriority = { "MinimumPriority", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSExecuteCommandTaskNode, MinimumPriority), Z_Construct_UEnum_ArmorWars_ERTSCommandPriority, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinimumPriority_MetaData), NewProp_MinimumPriority_MetaData) }; // 136588220
void Z_Construct_UClass_URTSExecuteCommandTaskNode_Statics::NewProp_bAllowCommandInterruption_SetBit(void* Obj)
{
	((URTSExecuteCommandTaskNode*)Obj)->bAllowCommandInterruption = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSExecuteCommandTaskNode_Statics::NewProp_bAllowCommandInterruption = { "bAllowCommandInterruption", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSExecuteCommandTaskNode), &Z_Construct_UClass_URTSExecuteCommandTaskNode_Statics::NewProp_bAllowCommandInterruption_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAllowCommandInterruption_MetaData), NewProp_bAllowCommandInterruption_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSExecuteCommandTaskNode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSExecuteCommandTaskNode_Statics::NewProp_MinimumPriority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSExecuteCommandTaskNode_Statics::NewProp_MinimumPriority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSExecuteCommandTaskNode_Statics::NewProp_bAllowCommandInterruption,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSExecuteCommandTaskNode_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSExecuteCommandTaskNode_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_URTSTaskNode,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSExecuteCommandTaskNode_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSExecuteCommandTaskNode_Statics::ClassParams = {
	&URTSExecuteCommandTaskNode::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_URTSExecuteCommandTaskNode_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSExecuteCommandTaskNode_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSExecuteCommandTaskNode_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSExecuteCommandTaskNode_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSExecuteCommandTaskNode()
{
	if (!Z_Registration_Info_UClass_URTSExecuteCommandTaskNode.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSExecuteCommandTaskNode.OuterSingleton, Z_Construct_UClass_URTSExecuteCommandTaskNode_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSExecuteCommandTaskNode.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSExecuteCommandTaskNode);
URTSExecuteCommandTaskNode::~URTSExecuteCommandTaskNode() {}
// ********** End Class URTSExecuteCommandTaskNode *************************************************

// ********** Begin Class URTSEnhancedMoveTask *****************************************************
void URTSEnhancedMoveTask::StaticRegisterNativesURTSEnhancedMoveTask()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSEnhancedMoveTask;
UClass* URTSEnhancedMoveTask::GetPrivateStaticClass()
{
	using TClass = URTSEnhancedMoveTask;
	if (!Z_Registration_Info_UClass_URTSEnhancedMoveTask.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSEnhancedMoveTask"),
			Z_Registration_Info_UClass_URTSEnhancedMoveTask.InnerSingleton,
			StaticRegisterNativesURTSEnhancedMoveTask,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSEnhancedMoveTask.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSEnhancedMoveTask_NoRegister()
{
	return URTSEnhancedMoveTask::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSEnhancedMoveTask_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enhanced move task that integrates with formation system\n * Supports formation movement and return fire while moving\n */" },
#endif
		{ "IncludePath", "RTSBehaviorNodes.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enhanced move task that integrates with formation system\nSupports formation movement and return fire while moving" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetLocationKey_MetaData[] = {
		{ "Category", "Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Movement parameters\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Movement parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AcceptanceRadius_MetaData[] = {
		{ "Category", "Movement" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseFormationMovement_MetaData[] = {
		{ "Category", "Movement" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bReturnFireWhileMoving_MetaData[] = {
		{ "Category", "Movement" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bStopOnEnemyContact_MetaData[] = {
		{ "Category", "Movement" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TargetLocationKey;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AcceptanceRadius;
	static void NewProp_bUseFormationMovement_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseFormationMovement;
	static void NewProp_bReturnFireWhileMoving_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bReturnFireWhileMoving;
	static void NewProp_bStopOnEnemyContact_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bStopOnEnemyContact;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSEnhancedMoveTask>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_URTSEnhancedMoveTask_Statics::NewProp_TargetLocationKey = { "TargetLocationKey", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSEnhancedMoveTask, TargetLocationKey), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetLocationKey_MetaData), NewProp_TargetLocationKey_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSEnhancedMoveTask_Statics::NewProp_AcceptanceRadius = { "AcceptanceRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSEnhancedMoveTask, AcceptanceRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AcceptanceRadius_MetaData), NewProp_AcceptanceRadius_MetaData) };
void Z_Construct_UClass_URTSEnhancedMoveTask_Statics::NewProp_bUseFormationMovement_SetBit(void* Obj)
{
	((URTSEnhancedMoveTask*)Obj)->bUseFormationMovement = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSEnhancedMoveTask_Statics::NewProp_bUseFormationMovement = { "bUseFormationMovement", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSEnhancedMoveTask), &Z_Construct_UClass_URTSEnhancedMoveTask_Statics::NewProp_bUseFormationMovement_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseFormationMovement_MetaData), NewProp_bUseFormationMovement_MetaData) };
void Z_Construct_UClass_URTSEnhancedMoveTask_Statics::NewProp_bReturnFireWhileMoving_SetBit(void* Obj)
{
	((URTSEnhancedMoveTask*)Obj)->bReturnFireWhileMoving = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSEnhancedMoveTask_Statics::NewProp_bReturnFireWhileMoving = { "bReturnFireWhileMoving", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSEnhancedMoveTask), &Z_Construct_UClass_URTSEnhancedMoveTask_Statics::NewProp_bReturnFireWhileMoving_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bReturnFireWhileMoving_MetaData), NewProp_bReturnFireWhileMoving_MetaData) };
void Z_Construct_UClass_URTSEnhancedMoveTask_Statics::NewProp_bStopOnEnemyContact_SetBit(void* Obj)
{
	((URTSEnhancedMoveTask*)Obj)->bStopOnEnemyContact = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSEnhancedMoveTask_Statics::NewProp_bStopOnEnemyContact = { "bStopOnEnemyContact", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSEnhancedMoveTask), &Z_Construct_UClass_URTSEnhancedMoveTask_Statics::NewProp_bStopOnEnemyContact_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bStopOnEnemyContact_MetaData), NewProp_bStopOnEnemyContact_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSEnhancedMoveTask_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSEnhancedMoveTask_Statics::NewProp_TargetLocationKey,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSEnhancedMoveTask_Statics::NewProp_AcceptanceRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSEnhancedMoveTask_Statics::NewProp_bUseFormationMovement,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSEnhancedMoveTask_Statics::NewProp_bReturnFireWhileMoving,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSEnhancedMoveTask_Statics::NewProp_bStopOnEnemyContact,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSEnhancedMoveTask_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSEnhancedMoveTask_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_URTSTaskNode,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSEnhancedMoveTask_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSEnhancedMoveTask_Statics::ClassParams = {
	&URTSEnhancedMoveTask::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_URTSEnhancedMoveTask_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSEnhancedMoveTask_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSEnhancedMoveTask_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSEnhancedMoveTask_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSEnhancedMoveTask()
{
	if (!Z_Registration_Info_UClass_URTSEnhancedMoveTask.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSEnhancedMoveTask.OuterSingleton, Z_Construct_UClass_URTSEnhancedMoveTask_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSEnhancedMoveTask.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSEnhancedMoveTask);
URTSEnhancedMoveTask::~URTSEnhancedMoveTask() {}
// ********** End Class URTSEnhancedMoveTask *******************************************************

// ********** Begin Class URTSEnhancedCombatTask ***************************************************
void URTSEnhancedCombatTask::StaticRegisterNativesURTSEnhancedCombatTask()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSEnhancedCombatTask;
UClass* URTSEnhancedCombatTask::GetPrivateStaticClass()
{
	using TClass = URTSEnhancedCombatTask;
	if (!Z_Registration_Info_UClass_URTSEnhancedCombatTask.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSEnhancedCombatTask"),
			Z_Registration_Info_UClass_URTSEnhancedCombatTask.InnerSingleton,
			StaticRegisterNativesURTSEnhancedCombatTask,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSEnhancedCombatTask.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSEnhancedCombatTask_NoRegister()
{
	return URTSEnhancedCombatTask::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSEnhancedCombatTask_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enhanced combat task with stat integration\n * Uses unit stats for range, damage, and tactical decisions\n */" },
#endif
		{ "IncludePath", "RTSBehaviorNodes.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enhanced combat task with stat integration\nUses unit stats for range, damage, and tactical decisions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetActorKey_MetaData[] = {
		{ "Category", "Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Combat parameters\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Combat parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoSelectTargets_MetaData[] = {
		{ "Category", "Combat" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseUnitWeaponRange_MetaData[] = {
		{ "Category", "Combat" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxEngagementRange_MetaData[] = {
		{ "Category", "Combat" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPursueTargets_MetaData[] = {
		{ "Category", "Combat" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetSwitchCooldown_MetaData[] = {
		{ "Category", "Combat" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TargetActorKey;
	static void NewProp_bAutoSelectTargets_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoSelectTargets;
	static void NewProp_bUseUnitWeaponRange_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseUnitWeaponRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxEngagementRange;
	static void NewProp_bPursueTargets_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPursueTargets;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TargetSwitchCooldown;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSEnhancedCombatTask>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_URTSEnhancedCombatTask_Statics::NewProp_TargetActorKey = { "TargetActorKey", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSEnhancedCombatTask, TargetActorKey), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetActorKey_MetaData), NewProp_TargetActorKey_MetaData) };
void Z_Construct_UClass_URTSEnhancedCombatTask_Statics::NewProp_bAutoSelectTargets_SetBit(void* Obj)
{
	((URTSEnhancedCombatTask*)Obj)->bAutoSelectTargets = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSEnhancedCombatTask_Statics::NewProp_bAutoSelectTargets = { "bAutoSelectTargets", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSEnhancedCombatTask), &Z_Construct_UClass_URTSEnhancedCombatTask_Statics::NewProp_bAutoSelectTargets_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoSelectTargets_MetaData), NewProp_bAutoSelectTargets_MetaData) };
void Z_Construct_UClass_URTSEnhancedCombatTask_Statics::NewProp_bUseUnitWeaponRange_SetBit(void* Obj)
{
	((URTSEnhancedCombatTask*)Obj)->bUseUnitWeaponRange = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSEnhancedCombatTask_Statics::NewProp_bUseUnitWeaponRange = { "bUseUnitWeaponRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSEnhancedCombatTask), &Z_Construct_UClass_URTSEnhancedCombatTask_Statics::NewProp_bUseUnitWeaponRange_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseUnitWeaponRange_MetaData), NewProp_bUseUnitWeaponRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSEnhancedCombatTask_Statics::NewProp_MaxEngagementRange = { "MaxEngagementRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSEnhancedCombatTask, MaxEngagementRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxEngagementRange_MetaData), NewProp_MaxEngagementRange_MetaData) };
void Z_Construct_UClass_URTSEnhancedCombatTask_Statics::NewProp_bPursueTargets_SetBit(void* Obj)
{
	((URTSEnhancedCombatTask*)Obj)->bPursueTargets = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSEnhancedCombatTask_Statics::NewProp_bPursueTargets = { "bPursueTargets", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSEnhancedCombatTask), &Z_Construct_UClass_URTSEnhancedCombatTask_Statics::NewProp_bPursueTargets_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPursueTargets_MetaData), NewProp_bPursueTargets_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSEnhancedCombatTask_Statics::NewProp_TargetSwitchCooldown = { "TargetSwitchCooldown", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSEnhancedCombatTask, TargetSwitchCooldown), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetSwitchCooldown_MetaData), NewProp_TargetSwitchCooldown_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSEnhancedCombatTask_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSEnhancedCombatTask_Statics::NewProp_TargetActorKey,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSEnhancedCombatTask_Statics::NewProp_bAutoSelectTargets,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSEnhancedCombatTask_Statics::NewProp_bUseUnitWeaponRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSEnhancedCombatTask_Statics::NewProp_MaxEngagementRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSEnhancedCombatTask_Statics::NewProp_bPursueTargets,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSEnhancedCombatTask_Statics::NewProp_TargetSwitchCooldown,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSEnhancedCombatTask_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSEnhancedCombatTask_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_URTSTaskNode,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSEnhancedCombatTask_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSEnhancedCombatTask_Statics::ClassParams = {
	&URTSEnhancedCombatTask::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_URTSEnhancedCombatTask_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSEnhancedCombatTask_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSEnhancedCombatTask_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSEnhancedCombatTask_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSEnhancedCombatTask()
{
	if (!Z_Registration_Info_UClass_URTSEnhancedCombatTask.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSEnhancedCombatTask.OuterSingleton, Z_Construct_UClass_URTSEnhancedCombatTask_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSEnhancedCombatTask.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSEnhancedCombatTask);
URTSEnhancedCombatTask::~URTSEnhancedCombatTask() {}
// ********** End Class URTSEnhancedCombatTask *****************************************************

// ********** Begin Class URTSFollowFormationTask **************************************************
void URTSFollowFormationTask::StaticRegisterNativesURTSFollowFormationTask()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSFollowFormationTask;
UClass* URTSFollowFormationTask::GetPrivateStaticClass()
{
	using TClass = URTSFollowFormationTask;
	if (!Z_Registration_Info_UClass_URTSFollowFormationTask.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSFollowFormationTask"),
			Z_Registration_Info_UClass_URTSFollowFormationTask.InnerSingleton,
			StaticRegisterNativesURTSFollowFormationTask,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSFollowFormationTask.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSFollowFormationTask_NoRegister()
{
	return URTSFollowFormationTask::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSFollowFormationTask_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Task node for following formation leader\n * Maintains formation position relative to leader\n */" },
#endif
		{ "IncludePath", "RTSBehaviorNodes.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Task node for following formation leader\nMaintains formation position relative to leader" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FormationLeaderKey_MetaData[] = {
		{ "Category", "Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Formation parameters\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Formation parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FormationOffsetKey_MetaData[] = {
		{ "Category", "Formation" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FormationTolerance_MetaData[] = {
		{ "Category", "Formation" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxFormationDistance_MetaData[] = {
		{ "Category", "Formation" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FormationLeaderKey;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FormationOffsetKey;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FormationTolerance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxFormationDistance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSFollowFormationTask>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_URTSFollowFormationTask_Statics::NewProp_FormationLeaderKey = { "FormationLeaderKey", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSFollowFormationTask, FormationLeaderKey), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FormationLeaderKey_MetaData), NewProp_FormationLeaderKey_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_URTSFollowFormationTask_Statics::NewProp_FormationOffsetKey = { "FormationOffsetKey", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSFollowFormationTask, FormationOffsetKey), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FormationOffsetKey_MetaData), NewProp_FormationOffsetKey_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSFollowFormationTask_Statics::NewProp_FormationTolerance = { "FormationTolerance", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSFollowFormationTask, FormationTolerance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FormationTolerance_MetaData), NewProp_FormationTolerance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSFollowFormationTask_Statics::NewProp_MaxFormationDistance = { "MaxFormationDistance", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSFollowFormationTask, MaxFormationDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxFormationDistance_MetaData), NewProp_MaxFormationDistance_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSFollowFormationTask_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSFollowFormationTask_Statics::NewProp_FormationLeaderKey,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSFollowFormationTask_Statics::NewProp_FormationOffsetKey,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSFollowFormationTask_Statics::NewProp_FormationTolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSFollowFormationTask_Statics::NewProp_MaxFormationDistance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSFollowFormationTask_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSFollowFormationTask_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_URTSTaskNode,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSFollowFormationTask_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSFollowFormationTask_Statics::ClassParams = {
	&URTSFollowFormationTask::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_URTSFollowFormationTask_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSFollowFormationTask_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSFollowFormationTask_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSFollowFormationTask_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSFollowFormationTask()
{
	if (!Z_Registration_Info_UClass_URTSFollowFormationTask.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSFollowFormationTask.OuterSingleton, Z_Construct_UClass_URTSFollowFormationTask_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSFollowFormationTask.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSFollowFormationTask);
URTSFollowFormationTask::~URTSFollowFormationTask() {}
// ********** End Class URTSFollowFormationTask ****************************************************

// ********** Begin Class URTSHasCommandsCondition *************************************************
void URTSHasCommandsCondition::StaticRegisterNativesURTSHasCommandsCondition()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSHasCommandsCondition;
UClass* URTSHasCommandsCondition::GetPrivateStaticClass()
{
	using TClass = URTSHasCommandsCondition;
	if (!Z_Registration_Info_UClass_URTSHasCommandsCondition.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSHasCommandsCondition"),
			Z_Registration_Info_UClass_URTSHasCommandsCondition.InnerSingleton,
			StaticRegisterNativesURTSHasCommandsCondition,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSHasCommandsCondition.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSHasCommandsCondition_NoRegister()
{
	return URTSHasCommandsCondition::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSHasCommandsCondition_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Condition node for checking if unit has commands\n * Used to prioritize command execution over autonomous behavior\n */" },
#endif
		{ "IncludePath", "RTSBehaviorNodes.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Condition node for checking if unit has commands\nUsed to prioritize command execution over autonomous behavior" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinimumPriority_MetaData[] = {
		{ "Category", "Commands" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Command checking parameters\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Command checking parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCheckQueuedCommands_MetaData[] = {
		{ "Category", "Commands" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_MinimumPriority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MinimumPriority;
	static void NewProp_bCheckQueuedCommands_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCheckQueuedCommands;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSHasCommandsCondition>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_URTSHasCommandsCondition_Statics::NewProp_MinimumPriority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_URTSHasCommandsCondition_Statics::NewProp_MinimumPriority = { "MinimumPriority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSHasCommandsCondition, MinimumPriority), Z_Construct_UEnum_ArmorWars_ERTSCommandPriority, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinimumPriority_MetaData), NewProp_MinimumPriority_MetaData) }; // 136588220
void Z_Construct_UClass_URTSHasCommandsCondition_Statics::NewProp_bCheckQueuedCommands_SetBit(void* Obj)
{
	((URTSHasCommandsCondition*)Obj)->bCheckQueuedCommands = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSHasCommandsCondition_Statics::NewProp_bCheckQueuedCommands = { "bCheckQueuedCommands", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSHasCommandsCondition), &Z_Construct_UClass_URTSHasCommandsCondition_Statics::NewProp_bCheckQueuedCommands_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCheckQueuedCommands_MetaData), NewProp_bCheckQueuedCommands_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSHasCommandsCondition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSHasCommandsCondition_Statics::NewProp_MinimumPriority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSHasCommandsCondition_Statics::NewProp_MinimumPriority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSHasCommandsCondition_Statics::NewProp_bCheckQueuedCommands,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSHasCommandsCondition_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSHasCommandsCondition_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_URTSConditionNode,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSHasCommandsCondition_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSHasCommandsCondition_Statics::ClassParams = {
	&URTSHasCommandsCondition::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_URTSHasCommandsCondition_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSHasCommandsCondition_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSHasCommandsCondition_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSHasCommandsCondition_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSHasCommandsCondition()
{
	if (!Z_Registration_Info_UClass_URTSHasCommandsCondition.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSHasCommandsCondition.OuterSingleton, Z_Construct_UClass_URTSHasCommandsCondition_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSHasCommandsCondition.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSHasCommandsCondition);
URTSHasCommandsCondition::~URTSHasCommandsCondition() {}
// ********** End Class URTSHasCommandsCondition ***************************************************

// ********** Begin Class URTSEnemiesInRangeCondition **********************************************
void URTSEnemiesInRangeCondition::StaticRegisterNativesURTSEnemiesInRangeCondition()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSEnemiesInRangeCondition;
UClass* URTSEnemiesInRangeCondition::GetPrivateStaticClass()
{
	using TClass = URTSEnemiesInRangeCondition;
	if (!Z_Registration_Info_UClass_URTSEnemiesInRangeCondition.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSEnemiesInRangeCondition"),
			Z_Registration_Info_UClass_URTSEnemiesInRangeCondition.InnerSingleton,
			StaticRegisterNativesURTSEnemiesInRangeCondition,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSEnemiesInRangeCondition.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSEnemiesInRangeCondition_NoRegister()
{
	return URTSEnemiesInRangeCondition::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSEnemiesInRangeCondition_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enhanced enemies in range condition with team integration\n * Uses team system to identify enemies and unit stats for range\n */" },
#endif
		{ "IncludePath", "RTSBehaviorNodes.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enhanced enemies in range condition with team integration\nUses team system to identify enemies and unit stats for range" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DetectionRange_MetaData[] = {
		{ "Category", "Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Range checking parameters\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Range checking parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseWeaponRange_MetaData[] = {
		{ "Category", "Combat" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bStoreNearestEnemy_MetaData[] = {
		{ "Category", "Combat" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NearestEnemyKey_MetaData[] = {
		{ "Category", "Combat" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnemyCountKey_MetaData[] = {
		{ "Category", "Combat" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DetectionRange;
	static void NewProp_bUseWeaponRange_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseWeaponRange;
	static void NewProp_bStoreNearestEnemy_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bStoreNearestEnemy;
	static const UECodeGen_Private::FStrPropertyParams NewProp_NearestEnemyKey;
	static const UECodeGen_Private::FStrPropertyParams NewProp_EnemyCountKey;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSEnemiesInRangeCondition>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSEnemiesInRangeCondition_Statics::NewProp_DetectionRange = { "DetectionRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSEnemiesInRangeCondition, DetectionRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DetectionRange_MetaData), NewProp_DetectionRange_MetaData) };
void Z_Construct_UClass_URTSEnemiesInRangeCondition_Statics::NewProp_bUseWeaponRange_SetBit(void* Obj)
{
	((URTSEnemiesInRangeCondition*)Obj)->bUseWeaponRange = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSEnemiesInRangeCondition_Statics::NewProp_bUseWeaponRange = { "bUseWeaponRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSEnemiesInRangeCondition), &Z_Construct_UClass_URTSEnemiesInRangeCondition_Statics::NewProp_bUseWeaponRange_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseWeaponRange_MetaData), NewProp_bUseWeaponRange_MetaData) };
void Z_Construct_UClass_URTSEnemiesInRangeCondition_Statics::NewProp_bStoreNearestEnemy_SetBit(void* Obj)
{
	((URTSEnemiesInRangeCondition*)Obj)->bStoreNearestEnemy = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSEnemiesInRangeCondition_Statics::NewProp_bStoreNearestEnemy = { "bStoreNearestEnemy", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSEnemiesInRangeCondition), &Z_Construct_UClass_URTSEnemiesInRangeCondition_Statics::NewProp_bStoreNearestEnemy_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bStoreNearestEnemy_MetaData), NewProp_bStoreNearestEnemy_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_URTSEnemiesInRangeCondition_Statics::NewProp_NearestEnemyKey = { "NearestEnemyKey", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSEnemiesInRangeCondition, NearestEnemyKey), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NearestEnemyKey_MetaData), NewProp_NearestEnemyKey_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_URTSEnemiesInRangeCondition_Statics::NewProp_EnemyCountKey = { "EnemyCountKey", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSEnemiesInRangeCondition, EnemyCountKey), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnemyCountKey_MetaData), NewProp_EnemyCountKey_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSEnemiesInRangeCondition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSEnemiesInRangeCondition_Statics::NewProp_DetectionRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSEnemiesInRangeCondition_Statics::NewProp_bUseWeaponRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSEnemiesInRangeCondition_Statics::NewProp_bStoreNearestEnemy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSEnemiesInRangeCondition_Statics::NewProp_NearestEnemyKey,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSEnemiesInRangeCondition_Statics::NewProp_EnemyCountKey,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSEnemiesInRangeCondition_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSEnemiesInRangeCondition_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_URTSConditionNode,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSEnemiesInRangeCondition_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSEnemiesInRangeCondition_Statics::ClassParams = {
	&URTSEnemiesInRangeCondition::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_URTSEnemiesInRangeCondition_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSEnemiesInRangeCondition_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSEnemiesInRangeCondition_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSEnemiesInRangeCondition_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSEnemiesInRangeCondition()
{
	if (!Z_Registration_Info_UClass_URTSEnemiesInRangeCondition.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSEnemiesInRangeCondition.OuterSingleton, Z_Construct_UClass_URTSEnemiesInRangeCondition_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSEnemiesInRangeCondition.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSEnemiesInRangeCondition);
URTSEnemiesInRangeCondition::~URTSEnemiesInRangeCondition() {}
// ********** End Class URTSEnemiesInRangeCondition ************************************************

// ********** Begin Class URTSInFormationCondition *************************************************
void URTSInFormationCondition::StaticRegisterNativesURTSInFormationCondition()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSInFormationCondition;
UClass* URTSInFormationCondition::GetPrivateStaticClass()
{
	using TClass = URTSInFormationCondition;
	if (!Z_Registration_Info_UClass_URTSInFormationCondition.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSInFormationCondition"),
			Z_Registration_Info_UClass_URTSInFormationCondition.InnerSingleton,
			StaticRegisterNativesURTSInFormationCondition,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSInFormationCondition.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSInFormationCondition_NoRegister()
{
	return URTSInFormationCondition::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSInFormationCondition_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Condition node for checking if unit is in formation\n * Used for formation behavior decisions\n */" },
#endif
		{ "IncludePath", "RTSBehaviorNodes.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Condition node for checking if unit is in formation\nUsed for formation behavior decisions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCheckFormationLeaderValid_MetaData[] = {
		{ "Category", "Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Formation checking parameters\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Formation checking parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCheckFormationDistance_MetaData[] = {
		{ "Category", "Formation" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxFormationDistance_MetaData[] = {
		{ "Category", "Formation" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bCheckFormationLeaderValid_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCheckFormationLeaderValid;
	static void NewProp_bCheckFormationDistance_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCheckFormationDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxFormationDistance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSInFormationCondition>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
void Z_Construct_UClass_URTSInFormationCondition_Statics::NewProp_bCheckFormationLeaderValid_SetBit(void* Obj)
{
	((URTSInFormationCondition*)Obj)->bCheckFormationLeaderValid = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSInFormationCondition_Statics::NewProp_bCheckFormationLeaderValid = { "bCheckFormationLeaderValid", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSInFormationCondition), &Z_Construct_UClass_URTSInFormationCondition_Statics::NewProp_bCheckFormationLeaderValid_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCheckFormationLeaderValid_MetaData), NewProp_bCheckFormationLeaderValid_MetaData) };
void Z_Construct_UClass_URTSInFormationCondition_Statics::NewProp_bCheckFormationDistance_SetBit(void* Obj)
{
	((URTSInFormationCondition*)Obj)->bCheckFormationDistance = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSInFormationCondition_Statics::NewProp_bCheckFormationDistance = { "bCheckFormationDistance", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSInFormationCondition), &Z_Construct_UClass_URTSInFormationCondition_Statics::NewProp_bCheckFormationDistance_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCheckFormationDistance_MetaData), NewProp_bCheckFormationDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSInFormationCondition_Statics::NewProp_MaxFormationDistance = { "MaxFormationDistance", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSInFormationCondition, MaxFormationDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxFormationDistance_MetaData), NewProp_MaxFormationDistance_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSInFormationCondition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSInFormationCondition_Statics::NewProp_bCheckFormationLeaderValid,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSInFormationCondition_Statics::NewProp_bCheckFormationDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSInFormationCondition_Statics::NewProp_MaxFormationDistance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSInFormationCondition_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSInFormationCondition_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_URTSConditionNode,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSInFormationCondition_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSInFormationCondition_Statics::ClassParams = {
	&URTSInFormationCondition::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_URTSInFormationCondition_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSInFormationCondition_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSInFormationCondition_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSInFormationCondition_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSInFormationCondition()
{
	if (!Z_Registration_Info_UClass_URTSInFormationCondition.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSInFormationCondition.OuterSingleton, Z_Construct_UClass_URTSInFormationCondition_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSInFormationCondition.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSInFormationCondition);
URTSInFormationCondition::~URTSInFormationCondition() {}
// ********** End Class URTSInFormationCondition ***************************************************

// ********** Begin Class URTSCommandInterruptDecorator ********************************************
void URTSCommandInterruptDecorator::StaticRegisterNativesURTSCommandInterruptDecorator()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSCommandInterruptDecorator;
UClass* URTSCommandInterruptDecorator::GetPrivateStaticClass()
{
	using TClass = URTSCommandInterruptDecorator;
	if (!Z_Registration_Info_UClass_URTSCommandInterruptDecorator.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSCommandInterruptDecorator"),
			Z_Registration_Info_UClass_URTSCommandInterruptDecorator.InnerSingleton,
			StaticRegisterNativesURTSCommandInterruptDecorator,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSCommandInterruptDecorator.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSCommandInterruptDecorator_NoRegister()
{
	return URTSCommandInterruptDecorator::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSCommandInterruptDecorator_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Decorator node that interrupts child execution when commands are received\n * Ensures command responsiveness\n */" },
#endif
		{ "IncludePath", "RTSBehaviorNodes.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Decorator node that interrupts child execution when commands are received\nEnsures command responsiveness" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinimumInterruptPriority_MetaData[] = {
		{ "Category", "Commands" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Interrupt parameters\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Interrupt parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CheckInterval_MetaData[] = {
		{ "Category", "Commands" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_MinimumInterruptPriority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MinimumInterruptPriority;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CheckInterval;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSCommandInterruptDecorator>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_URTSCommandInterruptDecorator_Statics::NewProp_MinimumInterruptPriority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_URTSCommandInterruptDecorator_Statics::NewProp_MinimumInterruptPriority = { "MinimumInterruptPriority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCommandInterruptDecorator, MinimumInterruptPriority), Z_Construct_UEnum_ArmorWars_ERTSCommandPriority, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinimumInterruptPriority_MetaData), NewProp_MinimumInterruptPriority_MetaData) }; // 136588220
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSCommandInterruptDecorator_Statics::NewProp_CheckInterval = { "CheckInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCommandInterruptDecorator, CheckInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CheckInterval_MetaData), NewProp_CheckInterval_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSCommandInterruptDecorator_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCommandInterruptDecorator_Statics::NewProp_MinimumInterruptPriority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCommandInterruptDecorator_Statics::NewProp_MinimumInterruptPriority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCommandInterruptDecorator_Statics::NewProp_CheckInterval,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSCommandInterruptDecorator_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSCommandInterruptDecorator_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_URTSDecoratorNode,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSCommandInterruptDecorator_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSCommandInterruptDecorator_Statics::ClassParams = {
	&URTSCommandInterruptDecorator::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_URTSCommandInterruptDecorator_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSCommandInterruptDecorator_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSCommandInterruptDecorator_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSCommandInterruptDecorator_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSCommandInterruptDecorator()
{
	if (!Z_Registration_Info_UClass_URTSCommandInterruptDecorator.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSCommandInterruptDecorator.OuterSingleton, Z_Construct_UClass_URTSCommandInterruptDecorator_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSCommandInterruptDecorator.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSCommandInterruptDecorator);
URTSCommandInterruptDecorator::~URTSCommandInterruptDecorator() {}
// ********** End Class URTSCommandInterruptDecorator **********************************************

// ********** Begin Class URTSHealthCondition ******************************************************
void URTSHealthCondition::StaticRegisterNativesURTSHealthCondition()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSHealthCondition;
UClass* URTSHealthCondition::GetPrivateStaticClass()
{
	using TClass = URTSHealthCondition;
	if (!Z_Registration_Info_UClass_URTSHealthCondition.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSHealthCondition"),
			Z_Registration_Info_UClass_URTSHealthCondition.InnerSingleton,
			StaticRegisterNativesURTSHealthCondition,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSHealthCondition.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSHealthCondition_NoRegister()
{
	return URTSHealthCondition::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSHealthCondition_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Health condition node for checking unit health status\n * Used to trigger health-based behaviors like retreat\n */" },
#endif
		{ "IncludePath", "RTSBehaviorNodes.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Health condition node for checking unit health status\nUsed to trigger health-based behaviors like retreat" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealthThreshold_MetaData[] = {
		{ "Category", "Health" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Health checking parameters\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Health checking parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCheckLowHealth_MetaData[] = {
		{ "Category", "Health" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Health percentage threshold (0.0 to 1.0)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Health percentage threshold (0.0 to 1.0)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCheckHighHealth_MetaData[] = {
		{ "Category", "Health" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// If true, returns success when health is below threshold\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "If true, returns success when health is below threshold" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HealthThreshold;
	static void NewProp_bCheckLowHealth_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCheckLowHealth;
	static void NewProp_bCheckHighHealth_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCheckHighHealth;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSHealthCondition>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSHealthCondition_Statics::NewProp_HealthThreshold = { "HealthThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSHealthCondition, HealthThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealthThreshold_MetaData), NewProp_HealthThreshold_MetaData) };
void Z_Construct_UClass_URTSHealthCondition_Statics::NewProp_bCheckLowHealth_SetBit(void* Obj)
{
	((URTSHealthCondition*)Obj)->bCheckLowHealth = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSHealthCondition_Statics::NewProp_bCheckLowHealth = { "bCheckLowHealth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSHealthCondition), &Z_Construct_UClass_URTSHealthCondition_Statics::NewProp_bCheckLowHealth_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCheckLowHealth_MetaData), NewProp_bCheckLowHealth_MetaData) };
void Z_Construct_UClass_URTSHealthCondition_Statics::NewProp_bCheckHighHealth_SetBit(void* Obj)
{
	((URTSHealthCondition*)Obj)->bCheckHighHealth = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSHealthCondition_Statics::NewProp_bCheckHighHealth = { "bCheckHighHealth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSHealthCondition), &Z_Construct_UClass_URTSHealthCondition_Statics::NewProp_bCheckHighHealth_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCheckHighHealth_MetaData), NewProp_bCheckHighHealth_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSHealthCondition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSHealthCondition_Statics::NewProp_HealthThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSHealthCondition_Statics::NewProp_bCheckLowHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSHealthCondition_Statics::NewProp_bCheckHighHealth,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSHealthCondition_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSHealthCondition_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_URTSConditionNode,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSHealthCondition_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSHealthCondition_Statics::ClassParams = {
	&URTSHealthCondition::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_URTSHealthCondition_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSHealthCondition_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSHealthCondition_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSHealthCondition_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSHealthCondition()
{
	if (!Z_Registration_Info_UClass_URTSHealthCondition.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSHealthCondition.OuterSingleton, Z_Construct_UClass_URTSHealthCondition_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSHealthCondition.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSHealthCondition);
URTSHealthCondition::~URTSHealthCondition() {}
// ********** End Class URTSHealthCondition ********************************************************

// ********** Begin Class URTSStatBasedDecorator ***************************************************
void URTSStatBasedDecorator::StaticRegisterNativesURTSStatBasedDecorator()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSStatBasedDecorator;
UClass* URTSStatBasedDecorator::GetPrivateStaticClass()
{
	using TClass = URTSStatBasedDecorator;
	if (!Z_Registration_Info_UClass_URTSStatBasedDecorator.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSStatBasedDecorator"),
			Z_Registration_Info_UClass_URTSStatBasedDecorator.InnerSingleton,
			StaticRegisterNativesURTSStatBasedDecorator,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSStatBasedDecorator.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSStatBasedDecorator_NoRegister()
{
	return URTSStatBasedDecorator::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSStatBasedDecorator_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Decorator node that modifies behavior based on unit stats\n * Allows for stat-based AI behavior variations\n */" },
#endif
		{ "IncludePath", "RTSBehaviorNodes.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Decorator node that modifies behavior based on unit stats\nAllows for stat-based AI behavior variations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCheckMovementSpeed_MetaData[] = {
		{ "Category", "Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Stat checking parameters\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Stat checking parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinMovementSpeed_MetaData[] = {
		{ "Category", "Stats" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCheckAttackRange_MetaData[] = {
		{ "Category", "Stats" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinAttackRange_MetaData[] = {
		{ "Category", "Stats" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCheckHealth_MetaData[] = {
		{ "Category", "Stats" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinHealthPercentage_MetaData[] = {
		{ "Category", "Stats" },
		{ "ModuleRelativePath", "Public/RTSBehaviorNodes.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bCheckMovementSpeed_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCheckMovementSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinMovementSpeed;
	static void NewProp_bCheckAttackRange_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCheckAttackRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinAttackRange;
	static void NewProp_bCheckHealth_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCheckHealth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinHealthPercentage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSStatBasedDecorator>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
void Z_Construct_UClass_URTSStatBasedDecorator_Statics::NewProp_bCheckMovementSpeed_SetBit(void* Obj)
{
	((URTSStatBasedDecorator*)Obj)->bCheckMovementSpeed = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSStatBasedDecorator_Statics::NewProp_bCheckMovementSpeed = { "bCheckMovementSpeed", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSStatBasedDecorator), &Z_Construct_UClass_URTSStatBasedDecorator_Statics::NewProp_bCheckMovementSpeed_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCheckMovementSpeed_MetaData), NewProp_bCheckMovementSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSStatBasedDecorator_Statics::NewProp_MinMovementSpeed = { "MinMovementSpeed", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSStatBasedDecorator, MinMovementSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinMovementSpeed_MetaData), NewProp_MinMovementSpeed_MetaData) };
void Z_Construct_UClass_URTSStatBasedDecorator_Statics::NewProp_bCheckAttackRange_SetBit(void* Obj)
{
	((URTSStatBasedDecorator*)Obj)->bCheckAttackRange = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSStatBasedDecorator_Statics::NewProp_bCheckAttackRange = { "bCheckAttackRange", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSStatBasedDecorator), &Z_Construct_UClass_URTSStatBasedDecorator_Statics::NewProp_bCheckAttackRange_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCheckAttackRange_MetaData), NewProp_bCheckAttackRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSStatBasedDecorator_Statics::NewProp_MinAttackRange = { "MinAttackRange", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSStatBasedDecorator, MinAttackRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinAttackRange_MetaData), NewProp_MinAttackRange_MetaData) };
void Z_Construct_UClass_URTSStatBasedDecorator_Statics::NewProp_bCheckHealth_SetBit(void* Obj)
{
	((URTSStatBasedDecorator*)Obj)->bCheckHealth = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSStatBasedDecorator_Statics::NewProp_bCheckHealth = { "bCheckHealth", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSStatBasedDecorator), &Z_Construct_UClass_URTSStatBasedDecorator_Statics::NewProp_bCheckHealth_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCheckHealth_MetaData), NewProp_bCheckHealth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSStatBasedDecorator_Statics::NewProp_MinHealthPercentage = { "MinHealthPercentage", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSStatBasedDecorator, MinHealthPercentage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinHealthPercentage_MetaData), NewProp_MinHealthPercentage_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSStatBasedDecorator_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSStatBasedDecorator_Statics::NewProp_bCheckMovementSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSStatBasedDecorator_Statics::NewProp_MinMovementSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSStatBasedDecorator_Statics::NewProp_bCheckAttackRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSStatBasedDecorator_Statics::NewProp_MinAttackRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSStatBasedDecorator_Statics::NewProp_bCheckHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSStatBasedDecorator_Statics::NewProp_MinHealthPercentage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSStatBasedDecorator_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSStatBasedDecorator_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_URTSDecoratorNode,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSStatBasedDecorator_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSStatBasedDecorator_Statics::ClassParams = {
	&URTSStatBasedDecorator::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_URTSStatBasedDecorator_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSStatBasedDecorator_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSStatBasedDecorator_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSStatBasedDecorator_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSStatBasedDecorator()
{
	if (!Z_Registration_Info_UClass_URTSStatBasedDecorator.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSStatBasedDecorator.OuterSingleton, Z_Construct_UClass_URTSStatBasedDecorator_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSStatBasedDecorator.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSStatBasedDecorator);
URTSStatBasedDecorator::~URTSStatBasedDecorator() {}
// ********** End Class URTSStatBasedDecorator *****************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h__Script_ArmorWars_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_URTSMoveToLocationTask, URTSMoveToLocationTask::StaticClass, TEXT("URTSMoveToLocationTask"), &Z_Registration_Info_UClass_URTSMoveToLocationTask, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSMoveToLocationTask), 2460136297U) },
		{ Z_Construct_UClass_URTSAttackTargetTask, URTSAttackTargetTask::StaticClass, TEXT("URTSAttackTargetTask"), &Z_Registration_Info_UClass_URTSAttackTargetTask, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSAttackTargetTask), 3752301842U) },
		{ Z_Construct_UClass_URTSPatrolTask, URTSPatrolTask::StaticClass, TEXT("URTSPatrolTask"), &Z_Registration_Info_UClass_URTSPatrolTask, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSPatrolTask), 2071682256U) },
		{ Z_Construct_UClass_URTSFindEnemyTask, URTSFindEnemyTask::StaticClass, TEXT("URTSFindEnemyTask"), &Z_Registration_Info_UClass_URTSFindEnemyTask, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSFindEnemyTask), 9610799U) },
		{ Z_Construct_UClass_URTSHealthLowCondition, URTSHealthLowCondition::StaticClass, TEXT("URTSHealthLowCondition"), &Z_Registration_Info_UClass_URTSHealthLowCondition, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSHealthLowCondition), 3408004770U) },
		{ Z_Construct_UClass_URTSEnemyNearbyCondition, URTSEnemyNearbyCondition::StaticClass, TEXT("URTSEnemyNearbyCondition"), &Z_Registration_Info_UClass_URTSEnemyNearbyCondition, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSEnemyNearbyCondition), 2428401303U) },
		{ Z_Construct_UClass_URTSHasValidTargetCondition, URTSHasValidTargetCondition::StaticClass, TEXT("URTSHasValidTargetCondition"), &Z_Registration_Info_UClass_URTSHasValidTargetCondition, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSHasValidTargetCondition), 1095404410U) },
		{ Z_Construct_UClass_URTSInverterDecorator, URTSInverterDecorator::StaticClass, TEXT("URTSInverterDecorator"), &Z_Registration_Info_UClass_URTSInverterDecorator, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSInverterDecorator), 2501488340U) },
		{ Z_Construct_UClass_URTSRepeaterDecorator, URTSRepeaterDecorator::StaticClass, TEXT("URTSRepeaterDecorator"), &Z_Registration_Info_UClass_URTSRepeaterDecorator, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSRepeaterDecorator), 231952631U) },
		{ Z_Construct_UClass_URTSCooldownDecorator, URTSCooldownDecorator::StaticClass, TEXT("URTSCooldownDecorator"), &Z_Registration_Info_UClass_URTSCooldownDecorator, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSCooldownDecorator), 3301413937U) },
		{ Z_Construct_UClass_URTSExecuteCommandTaskNode, URTSExecuteCommandTaskNode::StaticClass, TEXT("URTSExecuteCommandTaskNode"), &Z_Registration_Info_UClass_URTSExecuteCommandTaskNode, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSExecuteCommandTaskNode), 2885047697U) },
		{ Z_Construct_UClass_URTSEnhancedMoveTask, URTSEnhancedMoveTask::StaticClass, TEXT("URTSEnhancedMoveTask"), &Z_Registration_Info_UClass_URTSEnhancedMoveTask, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSEnhancedMoveTask), 2397957451U) },
		{ Z_Construct_UClass_URTSEnhancedCombatTask, URTSEnhancedCombatTask::StaticClass, TEXT("URTSEnhancedCombatTask"), &Z_Registration_Info_UClass_URTSEnhancedCombatTask, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSEnhancedCombatTask), 3076566085U) },
		{ Z_Construct_UClass_URTSFollowFormationTask, URTSFollowFormationTask::StaticClass, TEXT("URTSFollowFormationTask"), &Z_Registration_Info_UClass_URTSFollowFormationTask, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSFollowFormationTask), 2944317834U) },
		{ Z_Construct_UClass_URTSHasCommandsCondition, URTSHasCommandsCondition::StaticClass, TEXT("URTSHasCommandsCondition"), &Z_Registration_Info_UClass_URTSHasCommandsCondition, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSHasCommandsCondition), 3688241534U) },
		{ Z_Construct_UClass_URTSEnemiesInRangeCondition, URTSEnemiesInRangeCondition::StaticClass, TEXT("URTSEnemiesInRangeCondition"), &Z_Registration_Info_UClass_URTSEnemiesInRangeCondition, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSEnemiesInRangeCondition), 1774144955U) },
		{ Z_Construct_UClass_URTSInFormationCondition, URTSInFormationCondition::StaticClass, TEXT("URTSInFormationCondition"), &Z_Registration_Info_UClass_URTSInFormationCondition, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSInFormationCondition), 228745832U) },
		{ Z_Construct_UClass_URTSCommandInterruptDecorator, URTSCommandInterruptDecorator::StaticClass, TEXT("URTSCommandInterruptDecorator"), &Z_Registration_Info_UClass_URTSCommandInterruptDecorator, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSCommandInterruptDecorator), 2529803588U) },
		{ Z_Construct_UClass_URTSHealthCondition, URTSHealthCondition::StaticClass, TEXT("URTSHealthCondition"), &Z_Registration_Info_UClass_URTSHealthCondition, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSHealthCondition), 3515978031U) },
		{ Z_Construct_UClass_URTSStatBasedDecorator, URTSStatBasedDecorator::StaticClass, TEXT("URTSStatBasedDecorator"), &Z_Registration_Info_UClass_URTSStatBasedDecorator, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSStatBasedDecorator), 273319236U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h__Script_ArmorWars_934070724(TEXT("/Script/ArmorWars"),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h__Script_ArmorWars_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h__Script_ArmorWars_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
