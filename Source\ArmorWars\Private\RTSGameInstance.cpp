// Fill out your copyright notice in the Description page of Project Settings.

#include "RTSGameInstance.h"
#include "RTSTeamManager.h"
#include "RTSEconomySubsystem.h"
#include "UnitDatabaseSubsystem.h"
#include "Engine/Engine.h"

URTSGameInstance::URTSGameInstance()
{
	// Set default values
	bEnableDebugLogging = false;
}

void URTSGameInstance::Init()
{
	Super::Init();

	if (bEnableDebugLogging)
	{
		UE_LOG(LogTemp, Log, TEXT("RTSGameInstance: Initializing game instance"));
	}

	// Initialize subsystems - they should auto-initialize, but we can verify they exist
	URTSTeamManager* TeamManager = GetWorld()->GetSubsystem<URTSTeamManager>();
	if (TeamManager)
	{
		if (bEnableDebugLogging)
		{
			UE_LOG(LogTemp, Log, TEXT("RTSGameInstance: Team Manager subsystem initialized"));
		}
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("RTSGameInstance: Failed to get Team Manager subsystem"));
	}

	URTSEconomySubsystem* EconomySubsystem = GetSubsystem<URTSEconomySubsystem>();
	if (EconomySubsystem)
	{
		if (bEnableDebugLogging)
		{
			UE_LOG(LogTemp, Log, TEXT("RTSGameInstance: Economy subsystem initialized"));
		}
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("RTSGameInstance: Failed to get Economy subsystem"));
	}

	UUnitDatabaseSubsystem* UnitDatabase = GetSubsystem<UUnitDatabaseSubsystem>();
	if (UnitDatabase)
	{
		if (bEnableDebugLogging)
		{
			UE_LOG(LogTemp, Log, TEXT("RTSGameInstance: Unit Database subsystem initialized"));
		}
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("RTSGameInstance: Failed to get Unit Database subsystem"));
	}

	// Call Blueprint event
	OnGameInstanceInitialized();
}

void URTSGameInstance::Shutdown()
{
	if (bEnableDebugLogging)
	{
		UE_LOG(LogTemp, Log, TEXT("RTSGameInstance: Shutting down game instance"));
	}

	// Call Blueprint event
	OnGameInstanceShutdown();

	Super::Shutdown();
}

URTSTeamManager* URTSGameInstance::GetTeamManager() const
{
	return GetWorld()->GetSubsystem<URTSTeamManager>();
}

URTSEconomySubsystem* URTSGameInstance::GetEconomySubsystem() const
{
	return GetSubsystem<URTSEconomySubsystem>();
}

UUnitDatabaseSubsystem* URTSGameInstance::GetUnitDatabaseSubsystem() const
{
	return GetSubsystem<UUnitDatabaseSubsystem>();
}
