// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "RTSAIController.h"

#ifdef ARMORWARS_RTSAIController_generated_h
#error "RTSAIController.generated.h already included, missing '#pragma once' in RTSAIController.h"
#endif
#define ARMORWARS_RTSAIController_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class ARTSAIController;
class ARTSBaseActor;
class URTSBehaviorNode;
enum class ERTSAIPriority : uint8;
enum class ERTSAIState : uint8;
enum class ERTSCombatBehavior : uint8;
enum class ERTSFormationType : uint8;

// ********** Begin ScriptStruct FRTSAITargetInfo **************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSAIController_h_55_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FRTSAITargetInfo_Statics; \
	static class UScriptStruct* StaticStruct();


struct FRTSAITargetInfo;
// ********** End ScriptStruct FRTSAITargetInfo ****************************************************

// ********** Begin Delegate FOnAIStateChanged *****************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSAIController_h_491_DELEGATE \
static void FOnAIStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnAIStateChanged, ERTSAIState OldState, ERTSAIState NewState);


// ********** End Delegate FOnAIStateChanged *******************************************************

// ********** Begin Delegate FOnTargetAcquired *****************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSAIController_h_492_DELEGATE \
static void FOnTargetAcquired_DelegateWrapper(const FMulticastScriptDelegate& OnTargetAcquired, ARTSBaseActor* Target);


// ********** End Delegate FOnTargetAcquired *******************************************************

// ********** Begin Delegate FOnEnemyDetected ******************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSAIController_h_493_DELEGATE \
static void FOnEnemyDetected_DelegateWrapper(const FMulticastScriptDelegate& OnEnemyDetected, ARTSBaseActor* Enemy);


// ********** End Delegate FOnEnemyDetected ********************************************************

// ********** Begin Class ARTSAIController *********************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSAIController_h_94_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execBroadcastToGroup); \
	DECLARE_FUNCTION(execOnUnitDeath); \
	DECLARE_FUNCTION(execCreatePatrolBehaviorTree); \
	DECLARE_FUNCTION(execCreateCombatBehaviorTree); \
	DECLARE_FUNCTION(execCreateDefaultBehaviorTree); \
	DECLARE_FUNCTION(execIsBehaviorTreeRunning); \
	DECLARE_FUNCTION(execResumeBehaviorTree); \
	DECLARE_FUNCTION(execPauseBehaviorTree); \
	DECLARE_FUNCTION(execStopBehaviorTree); \
	DECLARE_FUNCTION(execStartBehaviorTree); \
	DECLARE_FUNCTION(execCoordinateAttackWithGroup); \
	DECLARE_FUNCTION(execShouldFlank); \
	DECLARE_FUNCTION(execShouldKite); \
	DECLARE_FUNCTION(execIsInOptimalRange); \
	DECLARE_FUNCTION(execSetRetreatThreshold); \
	DECLARE_FUNCTION(execEnableCoverSeeking); \
	DECLARE_FUNCTION(execSetFlankingBehavior); \
	DECLARE_FUNCTION(execEnableKitingBehavior); \
	DECLARE_FUNCTION(execSetEngagementRange); \
	DECLARE_FUNCTION(execSetCombatBehavior); \
	DECLARE_FUNCTION(execIsGroupLeader); \
	DECLARE_FUNCTION(execIsInGroup); \
	DECLARE_FUNCTION(execSetGroupFormation); \
	DECLARE_FUNCTION(execLeaveGroup); \
	DECLARE_FUNCTION(execJoinGroup); \
	DECLARE_FUNCTION(execHasReachedDestination); \
	DECLARE_FUNCTION(execIsMoving); \
	DECLARE_FUNCTION(execSetMovementSpeed); \
	DECLARE_FUNCTION(execEnableObstacleAvoidance); \
	DECLARE_FUNCTION(execSetFormationMovement); \
	DECLARE_FUNCTION(execMoveToLocationWithPathfinding); \
	DECLARE_FUNCTION(execGetDistanceToDefendPosition); \
	DECLARE_FUNCTION(execShouldRetreat); \
	DECLARE_FUNCTION(execIsInCombat); \
	DECLARE_FUNCTION(execCanSeeTarget); \
	DECLARE_FUNCTION(execDetectEnemiesInRange); \
	DECLARE_FUNCTION(execHasValidTarget); \
	DECLARE_FUNCTION(execFindBestTarget); \
	DECLARE_FUNCTION(execRemoveKnownTarget); \
	DECLARE_FUNCTION(execAddKnownTarget); \
	DECLARE_FUNCTION(execSetFollowTarget); \
	DECLARE_FUNCTION(execSetDefendPosition); \
	DECLARE_FUNCTION(execSetPatrolPoints); \
	DECLARE_FUNCTION(execSetAIState);


#define FID_ArmorWars_Source_ArmorWars_Public_RTSAIController_h_94_CALLBACK_WRAPPERS
ARMORWARS_API UClass* Z_Construct_UClass_ARTSAIController_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSAIController_h_94_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesARTSAIController(); \
	friend struct Z_Construct_UClass_ARTSAIController_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_ARTSAIController_NoRegister(); \
public: \
	DECLARE_CLASS2(ARTSAIController, AAIController, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_ARTSAIController_NoRegister) \
	DECLARE_SERIALIZER(ARTSAIController)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSAIController_h_94_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	ARTSAIController(ARTSAIController&&) = delete; \
	ARTSAIController(const ARTSAIController&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ARTSAIController); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ARTSAIController); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(ARTSAIController) \
	NO_API virtual ~ARTSAIController();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSAIController_h_91_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSAIController_h_94_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSAIController_h_94_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSAIController_h_94_CALLBACK_WRAPPERS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSAIController_h_94_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSAIController_h_94_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class ARTSAIController;

// ********** End Class ARTSAIController ***********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_ArmorWars_Source_ArmorWars_Public_RTSAIController_h

// ********** Begin Enum ERTSAIState ***************************************************************
#define FOREACH_ENUM_ERTSAISTATE(op) \
	op(ERTSAIState::Idle) \
	op(ERTSAIState::Patrol) \
	op(ERTSAIState::Attack) \
	op(ERTSAIState::Defend) \
	op(ERTSAIState::Follow) \
	op(ERTSAIState::Retreat) \
	op(ERTSAIState::FormationMove) \
	op(ERTSAIState::Investigate) 

enum class ERTSAIState : uint8;
template<> struct TIsUEnumClass<ERTSAIState> { enum { Value = true }; };
template<> ARMORWARS_API UEnum* StaticEnum<ERTSAIState>();
// ********** End Enum ERTSAIState *****************************************************************

// ********** Begin Enum ERTSAIPriority ************************************************************
#define FOREACH_ENUM_ERTSAIPRIORITY(op) \
	op(ERTSAIPriority::Low) \
	op(ERTSAIPriority::Normal) \
	op(ERTSAIPriority::High) \
	op(ERTSAIPriority::Critical) 

enum class ERTSAIPriority : uint8;
template<> struct TIsUEnumClass<ERTSAIPriority> { enum { Value = true }; };
template<> ARMORWARS_API UEnum* StaticEnum<ERTSAIPriority>();
// ********** End Enum ERTSAIPriority **************************************************************

// ********** Begin Enum ERTSCombatBehavior ********************************************************
#define FOREACH_ENUM_ERTSCOMBATBEHAVIOR(op) \
	op(ERTSCombatBehavior::Aggressive) \
	op(ERTSCombatBehavior::Defensive) \
	op(ERTSCombatBehavior::Kiting) \
	op(ERTSCombatBehavior::Flanking) \
	op(ERTSCombatBehavior::CoverSeeking) \
	op(ERTSCombatBehavior::SupportFire) \
	op(ERTSCombatBehavior::HitAndRun) 

enum class ERTSCombatBehavior : uint8;
template<> struct TIsUEnumClass<ERTSCombatBehavior> { enum { Value = true }; };
template<> ARMORWARS_API UEnum* StaticEnum<ERTSCombatBehavior>();
// ********** End Enum ERTSCombatBehavior **********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
