#include "RTSCommandPrioritySystem.h"
#include "RTSUnit.h"
#include "RTSCommandComponent.h"
#include "RTSBehaviorTreeComponent.h"
#include "RTSAIController.h"
#include "Engine/World.h"
#include "Engine/Engine.h"

URTSCommandPrioritySystem::URTSCommandPrioritySystem()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 0.1f;
    
    bEnableDebugLogging = false;
    PriorityUpdateInterval = 0.1f;
    MaxQueueSize = 20;
    bAutoExecuteHighestPriority = true;
    
    SourcePriorityWeight = 5.0f;
    CommandPriorityWeight = 3.0f;
    UrgencyWeight = 2.0f;
    DeadlineWeight = 4.0f;
    ImmediateWeight = 10.0f;
    
    NextSequenceNumber = 0;
    LastPriorityUpdate = 0.0f;
    bIsExecutingCommand = false;
}

void URTSCommandPrioritySystem::BeginPlay()
{
    Super::BeginPlay();
    
    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSCommandPrioritySystem: BeginPlay for %s"), 
            GetOwner() ? *GetOwner()->GetName() : TEXT("Unknown"));
    }
}

void URTSCommandPrioritySystem::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
    
    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    
    // Update priorities periodically
    if (CurrentTime - LastPriorityUpdate >= PriorityUpdateInterval)
    {
        UpdatePriorities();
        CleanupExpiredCommands();
        LastPriorityUpdate = CurrentTime;
    }
    
    // Auto-execute highest priority command
    if (bAutoExecuteHighestPriority && !bIsExecutingCommand && HasCommands())
    {
        ExecuteHighestPriorityCommand();
    }
}

bool URTSCommandPrioritySystem::AddCommand(const FRTSCommand& Command, const FRTSCommandContext& Context)
{
    if (!IsCommandValid(Command))
    {
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Warning, TEXT("RTSCommandPrioritySystem: Invalid command rejected"));
        }
        return false;
    }
    
    // Check queue size limit
    if (PriorityQueue.Num() >= MaxQueueSize)
    {
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Warning, TEXT("RTSCommandPrioritySystem: Queue full, command rejected"));
        }
        return false;
    }
    
    // Create priority command
    FRTSPriorityCommand PriorityCommand(Command, Context);
    PriorityCommand.Context.SequenceNumber = NextSequenceNumber++;
    PriorityCommand.Context.IssuedTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    
    // Calculate priority
    PriorityCommand.PriorityScore = CalculateCommandPriority(Command, PriorityCommand.Context);
    
    // Check if this command should interrupt current execution
    if (bIsExecutingCommand && CanInterruptCurrentCommand(Command, Context))
    {
        // Interrupt current command
        bIsExecutingCommand = false;
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSCommandPrioritySystem: Current command interrupted by higher priority"));
        }
    }
    
    // Add to queue
    PriorityQueue.Add(PriorityCommand);
    SortQueueByPriority();
    
    // Broadcast event
    BroadcastCommandAdded(PriorityCommand);
    OnQueueChanged.Broadcast();
    
    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSCommandPrioritySystem: Command added - Type: %s, Priority: %.2f, Source: %s"),
            *UEnum::GetValueAsString(Command.CommandType),
            PriorityCommand.PriorityScore,
            *UEnum::GetValueAsString(Context.Source));
    }
    
    return true;
}

bool URTSCommandPrioritySystem::AddPlayerCommand(const FRTSCommand& Command, AActor* Player)
{
    FRTSCommandContext Context;
    Context.Source = ERTSCommandSource::Player;
    Context.CommandIssuer = Player;
    Context.bRequiresImmediate = true; // Player commands are immediate
    Context.bCanBeInterrupted = false; // Player commands cannot be interrupted by AI
    
    return AddCommand(Command, Context);
}

bool URTSCommandPrioritySystem::AddAICommand(const FRTSCommand& Command, AActor* AIController)
{
    FRTSCommandContext Context;
    Context.Source = ERTSCommandSource::AIController;
    Context.CommandIssuer = AIController;
    Context.bCanBeInterrupted = true;
    
    return AddCommand(Command, Context);
}

bool URTSCommandPrioritySystem::AddEmergencyCommand(const FRTSCommand& Command)
{
    FRTSCommandContext Context;
    Context.Source = ERTSCommandSource::Emergency;
    Context.bRequiresImmediate = true;
    Context.bCanBeInterrupted = false;
    
    return AddCommand(Command, Context);
}

bool URTSCommandPrioritySystem::AddFormationCommand(const FRTSCommand& Command, AActor* FormationLeader)
{
    FRTSCommandContext Context;
    Context.Source = ERTSCommandSource::Formation;
    Context.CommandIssuer = FormationLeader;
    Context.bCanBeInterrupted = true;
    
    return AddCommand(Command, Context);
}

void URTSCommandPrioritySystem::ClearQueue()
{
    PriorityQueue.Empty();
    bIsExecutingCommand = false;
    OnQueueChanged.Broadcast();
    
    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSCommandPrioritySystem: Queue cleared"));
    }
}

bool URTSCommandPrioritySystem::RemoveCommand(int32 Index)
{
    if (Index >= 0 && Index < PriorityQueue.Num())
    {
        FRTSPriorityCommand RemovedCommand = PriorityQueue[Index];
        PriorityQueue.RemoveAt(Index);
        
        BroadcastCommandRemoved(RemovedCommand);
        OnQueueChanged.Broadcast();
        
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSCommandPrioritySystem: Command removed at index %d"), Index);
        }
        
        return true;
    }
    
    return false;
}

bool URTSCommandPrioritySystem::RemoveCommandsBySource(ERTSCommandSource Source)
{
    int32 RemovedCount = PriorityQueue.RemoveAll([Source](const FRTSPriorityCommand& PriorityCommand)
    {
        return PriorityCommand.Context.Source == Source;
    });
    
    if (RemovedCount > 0)
    {
        OnQueueChanged.Broadcast();
        
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSCommandPrioritySystem: Removed %d commands from source %s"), 
                RemovedCount, *UEnum::GetValueAsString(Source));
        }
        
        return true;
    }
    
    return false;
}

bool URTSCommandPrioritySystem::RemoveCommandsByIssuer(AActor* Issuer)
{
    if (!Issuer)
    {
        return false;
    }
    
    int32 RemovedCount = PriorityQueue.RemoveAll([Issuer](const FRTSPriorityCommand& PriorityCommand)
    {
        return PriorityCommand.Context.CommandIssuer.Get() == Issuer;
    });
    
    if (RemovedCount > 0)
    {
        OnQueueChanged.Broadcast();
        
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSCommandPrioritySystem: Removed %d commands from issuer %s"), 
                RemovedCount, *Issuer->GetName());
        }
        
        return true;
    }
    
    return false;
}

FRTSPriorityCommand URTSCommandPrioritySystem::GetHighestPriorityCommand() const
{
    if (PriorityQueue.Num() > 0)
    {
        return PriorityQueue[0];
    }
    
    return FRTSPriorityCommand();
}

TArray<FRTSPriorityCommand> URTSCommandPrioritySystem::GetCommandsByPriority() const
{
    TArray<FRTSPriorityCommand> SortedCommands = PriorityQueue;
    SortedCommands.Sort();
    return SortedCommands;
}

TArray<FRTSPriorityCommand> URTSCommandPrioritySystem::GetCommandsBySource(ERTSCommandSource Source) const
{
    TArray<FRTSPriorityCommand> FilteredCommands;
    
    for (const FRTSPriorityCommand& PriorityCommand : PriorityQueue)
    {
        if (PriorityCommand.Context.Source == Source)
        {
            FilteredCommands.Add(PriorityCommand);
        }
    }
    
    return FilteredCommands;
}

bool URTSCommandPrioritySystem::HasCommands() const
{
    return PriorityQueue.Num() > 0;
}

int32 URTSCommandPrioritySystem::GetQueueSize() const
{
    return PriorityQueue.Num();
}

bool URTSCommandPrioritySystem::CanInterruptCurrentCommand(const FRTSCommand& NewCommand, const FRTSCommandContext& NewContext) const
{
    if (!bIsExecutingCommand)
    {
        return true; // No current command to interrupt
    }
    
    if (!CurrentExecutingCommand.Context.bCanBeInterrupted)
    {
        return false; // Current command cannot be interrupted
    }
    
    // Calculate priority of new command
    float NewPriority = CalculateCommandPriority(NewCommand, NewContext);
    
    // Can interrupt if new command has higher priority
    return NewPriority > CurrentExecutingCommand.PriorityScore;
}

bool URTSCommandPrioritySystem::ExecuteHighestPriorityCommand()
{
    if (PriorityQueue.Num() == 0)
    {
        return false;
    }

    FRTSPriorityCommand HighestPriorityCommand = PriorityQueue[0];
    return ExecuteCommand(HighestPriorityCommand);
}

bool URTSCommandPrioritySystem::ExecuteCommand(const FRTSPriorityCommand& PriorityCommand)
{
    URTSCommandComponent* CommandComp = GetOwnerCommandComponent();
    if (!CommandComp)
    {
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Warning, TEXT("RTSCommandPrioritySystem: No command component found"));
        }
        return false;
    }

    // Issue command to command component
    bool bSuccess = CommandComp->IssueCommand(PriorityCommand.Command, true);

    if (bSuccess)
    {
        CurrentExecutingCommand = PriorityCommand;
        bIsExecutingCommand = true;

        // Remove from queue
        PriorityQueue.RemoveAt(0);

        BroadcastCommandExecuted(PriorityCommand);
        OnQueueChanged.Broadcast();

        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSCommandPrioritySystem: Executing command - Type: %s, Priority: %.2f"),
                *UEnum::GetValueAsString(PriorityCommand.Command.CommandType),
                PriorityCommand.PriorityScore);
        }
    }

    return bSuccess;
}

void URTSCommandPrioritySystem::UpdatePriorities()
{
    bool bPrioritiesChanged = false;

    for (FRTSPriorityCommand& PriorityCommand : PriorityQueue)
    {
        float OldPriority = PriorityCommand.PriorityScore;
        float NewPriority = CalculateCommandPriority(PriorityCommand.Command, PriorityCommand.Context);

        if (FMath::Abs(NewPriority - OldPriority) > 0.01f) // Threshold for priority change
        {
            PriorityCommand.PriorityScore = NewPriority;
            bPrioritiesChanged = true;
        }
    }

    if (bPrioritiesChanged)
    {
        SortQueueByPriority();
        OnQueueChanged.Broadcast();
    }
}

float URTSCommandPrioritySystem::CalculateCommandPriority(const FRTSCommand& Command, const FRTSCommandContext& Context) const
{
    float Priority = 1.0f;

    // Source priority
    Priority += GetSourcePriorityMultiplier(Context.Source) * SourcePriorityWeight;

    // Command type priority
    Priority += GetCommandTypePriorityMultiplier(Command.CommandType) * CommandPriorityWeight;

    // Command priority enum
    Priority += static_cast<float>(static_cast<uint8>(Command.Priority)) * CommandPriorityWeight;

    // Urgency factor
    Priority += CalculateUrgencyFactor(Context) * UrgencyWeight;

    // Deadline factor
    Priority += CalculateDeadlineFactor(Context) * DeadlineWeight;

    // Immediate requirement
    if (Context.bRequiresImmediate)
    {
        Priority += ImmediateWeight;
    }

    return Priority;
}

float URTSCommandPrioritySystem::GetSourcePriorityMultiplier(ERTSCommandSource Source) const
{
    switch (Source)
    {
        case ERTSCommandSource::Player:
            return 5.0f;
        case ERTSCommandSource::Emergency:
            return 4.5f;
        case ERTSCommandSource::AIController:
            return 3.0f;
        case ERTSCommandSource::Formation:
            return 2.5f;
        case ERTSCommandSource::System:
            return 2.0f;
        case ERTSCommandSource::Autonomous:
            return 1.0f;
        default:
            return 1.0f;
    }
}

float URTSCommandPrioritySystem::GetCommandTypePriorityMultiplier(ERTSCommandType CommandType) const
{
    switch (CommandType)
    {
        case ERTSCommandType::Stop:
            return 4.0f;
        case ERTSCommandType::AttackTarget:
            return 3.5f;
        case ERTSCommandType::AttackMove:
            return 3.0f;
        case ERTSCommandType::Move:
            return 2.5f;
        case ERTSCommandType::Formation:
            return 2.0f;
        case ERTSCommandType::Follow:
            return 2.0f;
        case ERTSCommandType::Guard:
            return 2.5f;
        case ERTSCommandType::Hold:
            return 2.0f;
        case ERTSCommandType::Patrol:
            return 1.5f;
        case ERTSCommandType::Retreat:
            return 3.5f;
        case ERTSCommandType::SpecialAbility:
            return 3.0f;
        default:
            return 1.0f;
    }
}

float URTSCommandPrioritySystem::CalculateUrgencyFactor(const FRTSCommandContext& Context) const
{
    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    float TimeSinceIssued = CurrentTime - Context.IssuedTime;

    // Commands become more urgent over time
    return FMath::Min(TimeSinceIssued / 10.0f, 2.0f); // Max 2.0 urgency after 10 seconds
}

float URTSCommandPrioritySystem::CalculateDeadlineFactor(const FRTSCommandContext& Context) const
{
    if (Context.ExecutionDeadline <= 0.0f)
    {
        return 0.0f; // No deadline
    }

    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    float TimeUntilDeadline = Context.ExecutionDeadline - CurrentTime;

    if (TimeUntilDeadline <= 0.0f)
    {
        return 5.0f; // Past deadline, very high priority
    }

    // Higher priority as deadline approaches
    return FMath::Max(0.0f, 3.0f - (TimeUntilDeadline / 5.0f));
}

void URTSCommandPrioritySystem::SortQueueByPriority()
{
    PriorityQueue.Sort();
}

void URTSCommandPrioritySystem::CleanupExpiredCommands()
{
    int32 RemovedCount = PriorityQueue.RemoveAll([this](const FRTSPriorityCommand& PriorityCommand)
    {
        return IsCommandExpired(PriorityCommand);
    });

    if (RemovedCount > 0)
    {
        OnQueueChanged.Broadcast();

        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSCommandPrioritySystem: Cleaned up %d expired commands"), RemovedCount);
        }
    }
}

bool URTSCommandPrioritySystem::IsCommandValid(const FRTSCommand& Command) const
{
    return Command.IsValid();
}

bool URTSCommandPrioritySystem::IsCommandExpired(const FRTSPriorityCommand& PriorityCommand) const
{
    if (PriorityCommand.Context.ExecutionDeadline <= 0.0f)
    {
        return false; // No deadline, never expires
    }

    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    return CurrentTime > PriorityCommand.Context.ExecutionDeadline;
}

void URTSCommandPrioritySystem::BroadcastCommandAdded(const FRTSPriorityCommand& PriorityCommand)
{
    OnCommandAdded.Broadcast(PriorityCommand);
}

void URTSCommandPrioritySystem::BroadcastCommandExecuted(const FRTSPriorityCommand& PriorityCommand)
{
    OnCommandExecuted.Broadcast(PriorityCommand);
}

void URTSCommandPrioritySystem::BroadcastCommandRemoved(const FRTSPriorityCommand& PriorityCommand)
{
    OnCommandRemoved.Broadcast(PriorityCommand);
}

URTSCommandComponent* URTSCommandPrioritySystem::GetOwnerCommandComponent() const
{
    ARTSUnit* Unit = GetOwnerUnit();
    return Unit ? Unit->GetCommandComponent() : nullptr;
}

URTSBehaviorTreeComponent* URTSCommandPrioritySystem::GetOwnerBehaviorTreeComponent() const
{
    ARTSUnit* Unit = GetOwnerUnit();
    if (Unit)
    {
        if (ARTSAIController* AIController = Cast<ARTSAIController>(Unit->GetController()))
        {
            return AIController->BehaviorTreeComponent;
        }
    }
    return nullptr;
}

ARTSUnit* URTSCommandPrioritySystem::GetOwnerUnit() const
{
    return Cast<ARTSUnit>(GetOwner());
}
