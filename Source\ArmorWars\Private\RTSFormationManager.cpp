#include "RTSFormationManager.h"
#include "RTSUnit.h"
#include "RTSCommandComponent.h"
#include "RTSFormationSystem.h"
#include "Engine/World.h"
#include "Engine/Engine.h"

URTSFormationManager::URTSFormationManager()
{
    NextGroupID = 0;
    FormationUpdateInterval = 0.1f;
    LastUpdateTime = 0.0f;
    bEnableDebugLogging = false;
}

void URTSFormationManager::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);
    
    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSFormationManager: Initialized"));
    }
}

void URTSFormationManager::Deinitialize()
{
    // Disband all formations
    FormationGroups.Empty();
    
    Super::Deinitialize();
    
    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSFormationManager: Deinitialized"));
    }
}

void URTSFormationManager::Tick(float DeltaTime)
{
    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    
    // Check if enough time has passed since last update
    if (CurrentTime - LastUpdateTime >= FormationUpdateInterval)
    {
        UpdateAllFormations();
        LastUpdateTime = CurrentTime;
    }
}

int32 URTSFormationManager::CreateFormation(const TArray<ARTSUnit*>& Units, ERTSFormationType FormationType, float UnitSpacing)
{
    if (Units.Num() < 2)
    {
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Warning, TEXT("RTSFormationManager: Cannot create formation with less than 2 units"));
        }
        return -1;
    }
    
    // Filter out invalid units
    TArray<ARTSUnit*> ValidUnits;
    for (ARTSUnit* Unit : Units)
    {
        if (Unit && IsValid(Unit) && Unit->IsAlive())
        {
            ValidUnits.Add(Unit);
        }
    }
    
    if (ValidUnits.Num() < 2)
    {
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Warning, TEXT("RTSFormationManager: Not enough valid units for formation"));
        }
        return -1;
    }
    
    // Create new formation group
    FRTSFormationGroup NewFormation;
    NewFormation.GroupID = GenerateGroupID();
    NewFormation.FormationType = FormationType;
    NewFormation.UnitSpacing = UnitSpacing;
    NewFormation.bIsActive = true;
    NewFormation.CreationTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    
    // Add units to formation
    for (ARTSUnit* Unit : ValidUnits)
    {
        NewFormation.Units.Add(Unit);
    }
    
    // Elect squad leader
    NewFormation.Leader = ElectSquadLeader(ValidUnits);
    
    if (!NewFormation.Leader.IsValid())
    {
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Warning, TEXT("RTSFormationManager: Failed to elect squad leader"));
        }
        return -1;
    }
    
    // Calculate initial formation center and direction
    NewFormation.FormationCenter = CalculateFormationCenter(NewFormation);
    NewFormation.FormationDirection = CalculateFormationDirection(NewFormation);
    
    // Store the formation
    FormationGroups.Add(NewFormation.GroupID, NewFormation);
    
    // Issue formation commands to units
    IssueFormationCommands(NewFormation);
    
    // Broadcast event
    BroadcastFormationCreated(NewFormation.GroupID, NewFormation);
    
    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSFormationManager: Created formation %d with %d units, leader: %s"),
            NewFormation.GroupID, ValidUnits.Num(), 
            NewFormation.Leader.IsValid() ? *NewFormation.Leader->GetName() : TEXT("None"));
    }
    
    return NewFormation.GroupID;
}

bool URTSFormationManager::DisbandFormation(int32 GroupID)
{
    FRTSFormationGroup* Formation = FormationGroups.Find(GroupID);
    if (!Formation)
    {
        return false;
    }
    
    // Clear formation data from all units
    for (const TSoftObjectPtr<ARTSUnit>& UnitPtr : Formation->Units)
    {
        if (ARTSUnit* Unit = UnitPtr.Get())
        {
            if (URTSCommandComponent* CommandComp = Unit->GetCommandComponent())
            {
                CommandComp->SetFormationData(FVector::ZeroVector, nullptr, -1);
            }
        }
    }
    
    // Remove formation
    FormationGroups.Remove(GroupID);
    
    // Broadcast event
    BroadcastFormationDisbanded(GroupID);
    
    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSFormationManager: Disbanded formation %d"), GroupID);
    }
    
    return true;
}

bool URTSFormationManager::AddUnitToFormation(int32 GroupID, ARTSUnit* Unit)
{
    if (!Unit || !IsValid(Unit))
    {
        return false;
    }
    
    FRTSFormationGroup* Formation = FormationGroups.Find(GroupID);
    if (!Formation)
    {
        return false;
    }
    
    // Check if unit is already in formation
    for (const TSoftObjectPtr<ARTSUnit>& ExistingUnit : Formation->Units)
    {
        if (ExistingUnit.Get() == Unit)
        {
            return false; // Already in formation
        }
    }
    
    // Add unit to formation
    Formation->Units.Add(Unit);
    
    // Update formation positions
    UpdateFormationPositions(*Formation);
    
    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSFormationManager: Added unit %s to formation %d"), 
            *Unit->GetName(), GroupID);
    }
    
    return true;
}

bool URTSFormationManager::RemoveUnitFromFormation(int32 GroupID, ARTSUnit* Unit)
{
    if (!Unit)
    {
        return false;
    }
    
    FRTSFormationGroup* Formation = FormationGroups.Find(GroupID);
    if (!Formation)
    {
        return false;
    }
    
    // Remove unit from formation
    int32 RemovedCount = Formation->Units.RemoveAll([Unit](const TSoftObjectPtr<ARTSUnit>& UnitPtr)
    {
        return UnitPtr.Get() == Unit;
    });
    
    if (RemovedCount > 0)
    {
        // Clear formation data from unit
        if (URTSCommandComponent* CommandComp = Unit->GetCommandComponent())
        {
            CommandComp->SetFormationData(FVector::ZeroVector, nullptr, -1);
        }
        
        // Check if this was the leader
        if (Formation->Leader.Get() == Unit)
        {
            // Elect new leader
            TArray<ARTSUnit*> RemainingUnits = GetFormationUnits(GroupID);
            if (RemainingUnits.Num() > 0)
            {
                Formation->Leader = ElectSquadLeader(RemainingUnits);
                if (Formation->Leader.IsValid())
                {
                    BroadcastFormationLeaderChanged(GroupID, Formation->Leader.Get());
                }
            }
        }
        
        // If formation has too few units, disband it
        if (Formation->GetUnitCount() < 2)
        {
            DisbandFormation(GroupID);
        }
        else
        {
            // Update formation positions
            UpdateFormationPositions(*Formation);
        }
        
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSFormationManager: Removed unit %s from formation %d"), 
                *Unit->GetName(), GroupID);
        }
        
        return true;
    }
    
    return false;
}

bool URTSFormationManager::DoesFormationExist(int32 GroupID) const
{
    return FormationGroups.Contains(GroupID);
}

FRTSFormationGroup URTSFormationManager::GetFormationGroup(int32 GroupID) const
{
    const FRTSFormationGroup* Formation = FormationGroups.Find(GroupID);
    return Formation ? *Formation : FRTSFormationGroup();
}

int32 URTSFormationManager::GetFormationForUnit(ARTSUnit* Unit) const
{
    if (!Unit)
    {
        return -1;
    }
    
    for (const auto& FormationPair : FormationGroups)
    {
        const FRTSFormationGroup& Formation = FormationPair.Value;
        for (const TSoftObjectPtr<ARTSUnit>& UnitPtr : Formation.Units)
        {
            if (UnitPtr.Get() == Unit)
            {
                return Formation.GroupID;
            }
        }
    }
    
    return -1;
}

ARTSUnit* URTSFormationManager::GetFormationLeader(int32 GroupID) const
{
    const FRTSFormationGroup* Formation = FormationGroups.Find(GroupID);
    return Formation ? Formation->Leader.Get() : nullptr;
}

TArray<ARTSUnit*> URTSFormationManager::GetFormationUnits(int32 GroupID) const
{
    TArray<ARTSUnit*> Units;
    
    const FRTSFormationGroup* Formation = FormationGroups.Find(GroupID);
    if (Formation)
    {
        for (const TSoftObjectPtr<ARTSUnit>& UnitPtr : Formation->Units)
        {
            if (ARTSUnit* Unit = UnitPtr.Get())
            {
                Units.Add(Unit);
            }
        }
    }
    
    return Units;
}

TArray<int32> URTSFormationManager::GetAllFormationIDs() const
{
    TArray<int32> FormationIDs;
    FormationGroups.GetKeys(FormationIDs);
    return FormationIDs;
}

bool URTSFormationManager::MoveFormation(int32 GroupID, const FVector& TargetLocation, ERTSCommandPriority Priority)
{
    FRTSFormationGroup* Formation = FormationGroups.Find(GroupID);
    if (!Formation)
    {
        return false;
    }

    // Update formation center
    Formation->FormationCenter = TargetLocation;

    // Calculate formation positions
    TArray<FVector> FormationPositions = CalculateFormationPositions(GroupID, TargetLocation);

    // Issue move commands to all units
    int32 UnitIndex = 0;
    for (const TSoftObjectPtr<ARTSUnit>& UnitPtr : Formation->Units)
    {
        if (ARTSUnit* Unit = UnitPtr.Get())
        {
            if (UnitIndex < FormationPositions.Num())
            {
                FVector FormationOffset = FormationPositions[UnitIndex] - TargetLocation;

                // Set formation data
                if (URTSCommandComponent* CommandComp = Unit->GetCommandComponent())
                {
                    CommandComp->SetFormationData(FormationOffset, Formation->Leader.Get(), UnitIndex);
                    CommandComp->IssueMoveCommand(TargetLocation, Priority, true);
                }
            }
            UnitIndex++;
        }
    }

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSFormationManager: Formation %d moving to %s"),
            GroupID, *TargetLocation.ToString());
    }

    return true;
}

bool URTSFormationManager::SetFormationType(int32 GroupID, ERTSFormationType NewFormationType)
{
    FRTSFormationGroup* Formation = FormationGroups.Find(GroupID);
    if (!Formation)
    {
        return false;
    }

    Formation->FormationType = NewFormationType;
    UpdateFormationPositions(*Formation);

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSFormationManager: Formation %d type changed to %s"),
            GroupID, *UEnum::GetValueAsString(NewFormationType));
    }

    return true;
}

bool URTSFormationManager::SetFormationSpacing(int32 GroupID, float NewSpacing)
{
    FRTSFormationGroup* Formation = FormationGroups.Find(GroupID);
    if (!Formation)
    {
        return false;
    }

    Formation->UnitSpacing = NewSpacing;
    UpdateFormationPositions(*Formation);

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSFormationManager: Formation %d spacing changed to %.2f"),
            GroupID, NewSpacing);
    }

    return true;
}

TArray<FVector> URTSFormationManager::CalculateFormationPositions(int32 GroupID, const FVector& FormationCenter) const
{
    const FRTSFormationGroup* Formation = FormationGroups.Find(GroupID);
    if (!Formation)
    {
        return TArray<FVector>();
    }

    return URTSFormationSystem::CalculateFormationPositions(
        Formation->FormationType,
        Formation->GetUnitCount(),
        FormationCenter,
        Formation->FormationDirection,
        Formation->UnitSpacing
    );
}

FVector URTSFormationManager::CalculateUnitFormationOffset(int32 GroupID, ARTSUnit* Unit) const
{
    if (!Unit)
    {
        return FVector::ZeroVector;
    }

    const FRTSFormationGroup* Formation = FormationGroups.Find(GroupID);
    if (!Formation)
    {
        return FVector::ZeroVector;
    }

    // Find unit index in formation
    int32 UnitIndex = -1;
    for (int32 i = 0; i < Formation->Units.Num(); i++)
    {
        if (Formation->Units[i].Get() == Unit)
        {
            UnitIndex = i;
            break;
        }
    }

    if (UnitIndex == -1)
    {
        return FVector::ZeroVector;
    }

    // Calculate formation positions
    TArray<FVector> FormationPositions = CalculateFormationPositions(GroupID, Formation->FormationCenter);

    if (UnitIndex < FormationPositions.Num())
    {
        return FormationPositions[UnitIndex] - Formation->FormationCenter;
    }

    return FVector::ZeroVector;
}

ARTSUnit* URTSFormationManager::ElectSquadLeader(const TArray<ARTSUnit*>& Units) const
{
    if (Units.Num() == 0)
    {
        return nullptr;
    }

    ARTSUnit* BestLeader = nullptr;
    float BestScore = -1.0f;

    for (ARTSUnit* Unit : Units)
    {
        if (!IsValidLeader(Unit))
        {
            continue;
        }

        float Score = CalculateLeadershipScore(Unit);
        if (Score > BestScore)
        {
            BestScore = Score;
            BestLeader = Unit;
        }
    }

    // Fallback to first valid unit if no leader found
    if (!BestLeader)
    {
        for (ARTSUnit* Unit : Units)
        {
            if (Unit && IsValid(Unit) && Unit->IsAlive())
            {
                BestLeader = Unit;
                break;
            }
        }
    }

    return BestLeader;
}

bool URTSFormationManager::ChangeFormationLeader(int32 GroupID, ARTSUnit* NewLeader)
{
    if (!NewLeader || !IsValid(NewLeader))
    {
        return false;
    }

    FRTSFormationGroup* Formation = FormationGroups.Find(GroupID);
    if (!Formation)
    {
        return false;
    }

    // Check if new leader is in the formation
    bool bIsInFormation = false;
    for (const TSoftObjectPtr<ARTSUnit>& UnitPtr : Formation->Units)
    {
        if (UnitPtr.Get() == NewLeader)
        {
            bIsInFormation = true;
            break;
        }
    }

    if (!bIsInFormation)
    {
        return false;
    }

    Formation->Leader = NewLeader;
    BroadcastFormationLeaderChanged(GroupID, NewLeader);

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSFormationManager: Formation %d leader changed to %s"),
            GroupID, *NewLeader->GetName());
    }

    return true;
}

void URTSFormationManager::UpdateAllFormations()
{
    CleanupInvalidFormations();

    for (auto& FormationPair : FormationGroups)
    {
        UpdateFormation(FormationPair.Key);
    }
}

void URTSFormationManager::UpdateFormation(int32 GroupID)
{
    FRTSFormationGroup* Formation = FormationGroups.Find(GroupID);
    if (!Formation)
    {
        return;
    }

    // Clean up invalid units
    Formation->CleanupInvalidUnits();

    // Check if formation still has enough units
    if (Formation->GetUnitCount() < 2)
    {
        DisbandFormation(GroupID);
        return;
    }

    // Validate leader
    ValidateFormationLeader(*Formation);

    // Update formation positions
    UpdateFormationPositions(*Formation);
}

void URTSFormationManager::CleanupInvalidFormations()
{
    TArray<int32> FormationsToRemove;

    for (auto& FormationPair : FormationGroups)
    {
        FRTSFormationGroup& Formation = FormationPair.Value;
        Formation.CleanupInvalidUnits();

        if (Formation.GetUnitCount() < 2 || !Formation.Leader.IsValid())
        {
            FormationsToRemove.Add(Formation.GroupID);
        }
    }

    for (int32 GroupID : FormationsToRemove)
    {
        DisbandFormation(GroupID);
    }
}

void URTSFormationManager::UpdateFormationPositions(FRTSFormationGroup& FormationGroup)
{
    if (!FormationGroup.Leader.IsValid())
    {
        return;
    }

    // Update formation center and direction
    FormationGroup.FormationCenter = CalculateFormationCenter(FormationGroup);
    FormationGroup.FormationDirection = CalculateFormationDirection(FormationGroup);

    // Calculate formation positions
    TArray<FVector> FormationPositions = URTSFormationSystem::CalculateFormationPositions(
        FormationGroup.FormationType,
        FormationGroup.GetUnitCount(),
        FormationGroup.FormationCenter,
        FormationGroup.FormationDirection,
        FormationGroup.UnitSpacing
    );

    // Update unit formation data
    int32 UnitIndex = 0;
    for (const TSoftObjectPtr<ARTSUnit>& UnitPtr : FormationGroup.Units)
    {
        if (ARTSUnit* Unit = UnitPtr.Get())
        {
            if (UnitIndex < FormationPositions.Num())
            {
                FVector FormationOffset = FormationPositions[UnitIndex] - FormationGroup.FormationCenter;

                if (URTSCommandComponent* CommandComp = Unit->GetCommandComponent())
                {
                    CommandComp->SetFormationData(FormationOffset, FormationGroup.Leader.Get(), UnitIndex);
                }
            }
            UnitIndex++;
        }
    }
}

void URTSFormationManager::ValidateFormationLeader(FRTSFormationGroup& FormationGroup)
{
    if (!FormationGroup.Leader.IsValid() || !FormationGroup.Leader->IsAlive())
    {
        // Elect new leader
        TArray<ARTSUnit*> ValidUnits = GetFormationUnits(FormationGroup.GroupID);
        if (ValidUnits.Num() > 0)
        {
            ARTSUnit* NewLeader = ElectSquadLeader(ValidUnits);
            if (NewLeader)
            {
                FormationGroup.Leader = NewLeader;
                BroadcastFormationLeaderChanged(FormationGroup.GroupID, NewLeader);
            }
        }
    }
}

void URTSFormationManager::IssueFormationCommands(FRTSFormationGroup& FormationGroup)
{
    // Calculate formation positions
    TArray<FVector> FormationPositions = CalculateFormationPositions(FormationGroup.GroupID, FormationGroup.FormationCenter);

    // Issue formation commands to all units
    int32 UnitIndex = 0;
    for (const TSoftObjectPtr<ARTSUnit>& UnitPtr : FormationGroup.Units)
    {
        if (ARTSUnit* Unit = UnitPtr.Get())
        {
            if (UnitIndex < FormationPositions.Num())
            {
                FVector FormationOffset = FormationPositions[UnitIndex] - FormationGroup.FormationCenter;

                if (URTSCommandComponent* CommandComp = Unit->GetCommandComponent())
                {
                    // Issue formation command
                    FRTSCommand FormationCommand;
                    FormationCommand.CommandType = ERTSCommandType::Formation;
                    FormationCommand.Priority = ERTSCommandPriority::High;
                    FormationCommand.FormationType = static_cast<ERTSFormationCommandType>(FormationGroup.FormationType);
                    FormationCommand.TargetLocation = FormationGroup.FormationCenter;
                    FormationCommand.FormationOffset = FormationOffset;
                    FormationCommand.FormationLeader = FormationGroup.Leader.Get();
                    FormationCommand.FormationIndex = UnitIndex;

                    CommandComp->IssueCommand(FormationCommand, false);
                }
            }
            UnitIndex++;
        }
    }
}

FVector URTSFormationManager::CalculateFormationCenter(const FRTSFormationGroup& FormationGroup) const
{
    if (FormationGroup.Leader.IsValid())
    {
        return FormationGroup.Leader->GetActorLocation();
    }

    // Fallback: calculate center of all units
    FVector Center = FVector::ZeroVector;
    int32 ValidUnitCount = 0;

    for (const TSoftObjectPtr<ARTSUnit>& UnitPtr : FormationGroup.Units)
    {
        if (ARTSUnit* Unit = UnitPtr.Get())
        {
            Center += Unit->GetActorLocation();
            ValidUnitCount++;
        }
    }

    return ValidUnitCount > 0 ? Center / ValidUnitCount : FVector::ZeroVector;
}

FVector URTSFormationManager::CalculateFormationDirection(const FRTSFormationGroup& FormationGroup) const
{
    if (FormationGroup.Leader.IsValid())
    {
        return FormationGroup.Leader->GetActorForwardVector();
    }

    return FVector::ForwardVector;
}

float URTSFormationManager::CalculateLeadershipScore(ARTSUnit* Unit) const
{
    if (!Unit || !IsValid(Unit))
    {
        return 0.0f;
    }

    float Score = 1.0f;

    // Health factor (healthier units are better leaders)
    Score += Unit->GetHealthPercentage() * 2.0f;

    // Tech level factor (higher tech units are better leaders)
    Score += static_cast<float>(static_cast<uint8>(Unit->GetTechLevel())) * 0.5f;

    // Combat capability factor
    if (Unit->HasWeapons())
    {
        Score += 1.0f;
        Score += Unit->GetMaxAttackRange() / 1000.0f; // Normalize range
    }

    return Score;
}

bool URTSFormationManager::IsValidLeader(ARTSUnit* Unit) const
{
    return Unit && IsValid(Unit) && Unit->IsAlive();
}

int32 URTSFormationManager::GenerateGroupID()
{
    return NextGroupID++;
}

void URTSFormationManager::BroadcastFormationCreated(int32 GroupID, const FRTSFormationGroup& FormationGroup)
{
    OnFormationCreated.Broadcast(GroupID, FormationGroup);
}

void URTSFormationManager::BroadcastFormationDisbanded(int32 GroupID)
{
    OnFormationDisbanded.Broadcast(GroupID);
}

void URTSFormationManager::BroadcastFormationLeaderChanged(int32 GroupID, ARTSUnit* NewLeader)
{
    OnFormationLeaderChanged.Broadcast(GroupID, NewLeader);
}
