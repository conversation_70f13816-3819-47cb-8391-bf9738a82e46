#include "RTSAITestingSystem.h"
#include "RTSUnit.h"
#include "RTSCommandComponent.h"
#include "RTSBehaviorTreeComponent.h"
#include "RTSFormationManager.h"
#include "RTSTeamManager.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Kismet/GameplayStatics.h"
#include "DrawDebugHelpers.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Misc/DateTime.h"

// URTSAIDebugComponent Implementation

URTSAIDebugComponent::URTSAIDebugComponent()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 0.5f;
    
    bEnableDebugLogging = false;
    bEnableVisualDebug = false;
    bLogCommandExecution = true;
    bLogBehaviorTreeStates = true;
    bLogMovementData = false;
    bLogCombatData = true;
    DebugUpdateInterval = 0.5f;
    LastDebugUpdate = 0.0f;
    MaxLogMessages = 50;
}

void URTSAIDebugComponent::BeginPlay()
{
    Super::BeginPlay();
    
    if (bEnableDebugLogging)
    {
        AddLogMessage(FString::Printf(TEXT("AI Debug Component initialized for %s"), 
            GetOwner() ? *GetOwner()->GetName() : TEXT("Unknown")));
    }
}

void URTSAIDebugComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
    
    if (bEnableDebugLogging || bEnableVisualDebug)
    {
        UpdateDebugInfo(DeltaTime);
    }
}

void URTSAIDebugComponent::LogCurrentState()
{
    ARTSUnit* Unit = GetOwnerUnit();
    if (!Unit)
    {
        return;
    }
    
    FString StateInfo = FString::Printf(TEXT("[%s] Unit State - Health: %.1f/%.1f, Position: %s, Velocity: %s"),
        *GetFormattedTimestamp(),
        Unit->GetCurrentHealth(),
        Unit->GetMaxHealth(),
        *Unit->GetActorLocation().ToString(),
        *Unit->GetVelocity().ToString());
    
    AddLogMessage(StateInfo);
    UE_LOG(LogTemp, Log, TEXT("RTSAIDebug: %s"), *StateInfo);
}

void URTSAIDebugComponent::LogCommandQueue()
{
    if (!bLogCommandExecution)
    {
        return;
    }
    
    ARTSUnit* Unit = GetOwnerUnit();
    if (!Unit)
    {
        return;
    }
    
    URTSCommandComponent* CommandComp = Unit->GetCommandComponent();
    if (!CommandComp)
    {
        return;
    }
    
    FString CommandInfo = FString::Printf(TEXT("[%s] Commands - Has Commands: %s, Queue Size: %d"),
        *GetFormattedTimestamp(),
        CommandComp->HasCommands() ? TEXT("Yes") : TEXT("No"),
        CommandComp->GetCommandQueueSize());
    
    if (CommandComp->HasCommands())
    {
        FRTSCommand CurrentCommand = CommandComp->GetCurrentCommand();
        CommandInfo += FString::Printf(TEXT(", Current: %s, Priority: %s"),
            *UEnum::GetValueAsString(CurrentCommand.CommandType),
            *UEnum::GetValueAsString(CurrentCommand.Priority));
    }
    
    AddLogMessage(CommandInfo);
    UE_LOG(LogTemp, Log, TEXT("RTSAIDebug: %s"), *CommandInfo);
}

void URTSAIDebugComponent::LogBehaviorTreeState()
{
    if (!bLogBehaviorTreeStates)
    {
        return;
    }
    
    // This would need integration with the behavior tree system
    FString BTInfo = FString::Printf(TEXT("[%s] Behavior Tree - Status: Active"),
        *GetFormattedTimestamp());
    
    AddLogMessage(BTInfo);
    UE_LOG(LogTemp, Log, TEXT("RTSAIDebug: %s"), *BTInfo);
}

void URTSAIDebugComponent::LogMovementState()
{
    if (!bLogMovementData)
    {
        return;
    }
    
    ARTSUnit* Unit = GetOwnerUnit();
    if (!Unit)
    {
        return;
    }
    
    FString MovementInfo = FString::Printf(TEXT("[%s] Movement - Speed: %.1f, Max Speed: %.1f, Is Moving: %s"),
        *GetFormattedTimestamp(),
        Unit->GetVelocity().Size(),
        Unit->GetMovementSpeed(),
        Unit->GetVelocity().Size() > 10.0f ? TEXT("Yes") : TEXT("No"));
    
    AddLogMessage(MovementInfo);
    UE_LOG(LogTemp, Log, TEXT("RTSAIDebug: %s"), *MovementInfo);
}

void URTSAIDebugComponent::LogCombatState()
{
    if (!bLogCombatData)
    {
        return;
    }
    
    ARTSUnit* Unit = GetOwnerUnit();
    if (!Unit)
    {
        return;
    }
    
    FString CombatInfo = FString::Printf(TEXT("[%s] Combat - Has Weapons: %s, Attack Range: %.1f"),
        *GetFormattedTimestamp(),
        Unit->HasWeapons() ? TEXT("Yes") : TEXT("No"),
        Unit->GetMaxAttackRange());
    
    AddLogMessage(CombatInfo);
    UE_LOG(LogTemp, Log, TEXT("RTSAIDebug: %s"), *CombatInfo);
}

void URTSAIDebugComponent::DrawDebugInfo()
{
    if (!bEnableVisualDebug)
    {
        return;
    }
    
    ARTSUnit* Unit = GetOwnerUnit();
    if (!Unit || !GetWorld())
    {
        return;
    }
    
    FVector UnitLocation = Unit->GetActorLocation();
    
    // Draw unit info
    DrawDebugString(GetWorld(), UnitLocation + FVector(0, 0, 100), 
        FString::Printf(TEXT("%s\nHealth: %.0f/%.0f"), *Unit->GetName(), Unit->GetCurrentHealth(), Unit->GetMaxHealth()),
        nullptr, FColor::White, 0.0f);
    
    // Draw velocity vector
    if (!Unit->GetVelocity().IsNearlyZero())
    {
        FVector VelocityEnd = UnitLocation + Unit->GetVelocity().GetSafeNormal() * 200.0f;
        DrawDebugDirectionalArrow(GetWorld(), UnitLocation, VelocityEnd, 50.0f, FColor::Green, false, 0.0f, 0, 3.0f);
    }
    
    // Draw attack range
    if (Unit->HasWeapons())
    {
        DrawDebugCircle(GetWorld(), UnitLocation, Unit->GetMaxAttackRange(), 32, FColor::Red, false, 0.0f, 0, 2.0f, 
            FVector::ForwardVector, FVector::RightVector);
    }
}

bool URTSAIDebugComponent::ValidateAIComponents()
{
    ARTSUnit* Unit = GetOwnerUnit();
    if (!Unit)
    {
        AddLogMessage(TEXT("Validation Failed: No owner unit"));
        return false;
    }
    
    bool bAllValid = true;
    
    // Check command component
    if (!Unit->GetCommandComponent())
    {
        AddLogMessage(TEXT("Validation Failed: Missing Command Component"));
        bAllValid = false;
    }
    
    // Check blackboard component
    if (!Unit->GetBlackboardComponent())
    {
        AddLogMessage(TEXT("Validation Failed: Missing Blackboard Component"));
        bAllValid = false;
    }
    
    // Check weapon controller
    if (!Unit->GetWeaponController())
    {
        AddLogMessage(TEXT("Validation Failed: Missing Weapon Controller"));
        bAllValid = false;
    }
    
    if (bAllValid)
    {
        AddLogMessage(TEXT("Validation Passed: All AI components present"));
    }
    
    return bAllValid;
}

TArray<FString> URTSAIDebugComponent::GetAIComponentStatus()
{
    TArray<FString> StatusList;
    ARTSUnit* Unit = GetOwnerUnit();
    
    if (!Unit)
    {
        StatusList.Add(TEXT("ERROR: No owner unit"));
        return StatusList;
    }
    
    StatusList.Add(FString::Printf(TEXT("Unit: %s"), *Unit->GetName()));
    StatusList.Add(FString::Printf(TEXT("Command Component: %s"), Unit->GetCommandComponent() ? TEXT("OK") : TEXT("MISSING")));
    StatusList.Add(FString::Printf(TEXT("Blackboard Component: %s"), Unit->GetBlackboardComponent() ? TEXT("OK") : TEXT("MISSING")));
    StatusList.Add(FString::Printf(TEXT("Weapon Controller: %s"), Unit->GetWeaponController() ? TEXT("OK") : TEXT("MISSING")));
    StatusList.Add(FString::Printf(TEXT("Health: %.1f/%.1f"), Unit->GetCurrentHealth(), Unit->GetMaxHealth()));
    StatusList.Add(FString::Printf(TEXT("Team ID: %d"), Unit->TeamID));
    
    return StatusList;
}

ARTSUnit* URTSAIDebugComponent::GetOwnerUnit() const
{
    return Cast<ARTSUnit>(GetOwner());
}

void URTSAIDebugComponent::UpdateDebugInfo(float DeltaTime)
{
    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    
    if (CurrentTime - LastDebugUpdate >= DebugUpdateInterval)
    {
        if (bEnableDebugLogging)
        {
            LogCurrentState();
            LogCommandQueue();
            LogBehaviorTreeState();
            LogMovementState();
            LogCombatState();
        }
        
        if (bEnableVisualDebug)
        {
            DrawDebugInfo();
        }
        
        LastDebugUpdate = CurrentTime;
    }
}

void URTSAIDebugComponent::AddLogMessage(const FString& Message)
{
    RecentLogMessages.Add(Message);
    
    // Keep only recent messages
    if (RecentLogMessages.Num() > MaxLogMessages)
    {
        RecentLogMessages.RemoveAt(0);
    }
}

FString URTSAIDebugComponent::GetFormattedTimestamp() const
{
    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    return FString::Printf(TEXT("%.2f"), CurrentTime);
}

// URTSAITestingManager Implementation

URTSAITestingManager::URTSAITestingManager()
{
    bIsRunningTest = false;
    CurrentTestScenario = ERTSTestScenario::BasicMovement;
    CurrentTestStartTime = 0.0f;
    MaxFrameHistorySize = 300;
}

void URTSAITestingManager::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);
    
    UE_LOG(LogTemp, Log, TEXT("RTSAITestingManager: Initialized"));
}

void URTSAITestingManager::Deinitialize()
{
    StopCurrentTest(false, TEXT("System shutdown"));
    CleanupTestUnits();
    
    Super::Deinitialize();
    
    UE_LOG(LogTemp, Log, TEXT("RTSAITestingManager: Deinitialized"));
}

void URTSAITestingManager::Tick(float DeltaTime)
{
    UpdatePerformanceMetrics(DeltaTime);
    
    if (bIsRunningTest)
    {
        ProcessCurrentTest(DeltaTime);
    }
}

bool URTSAITestingManager::StartTest(ERTSTestScenario TestScenario)
{
    if (bIsRunningTest)
    {
        UE_LOG(LogTemp, Warning, TEXT("RTSAITestingManager: Cannot start test - another test is already running"));
        return false;
    }
    
    if (!ValidateTestEnvironment())
    {
        UE_LOG(LogTemp, Error, TEXT("RTSAITestingManager: Test environment validation failed"));
        return false;
    }
    
    CurrentTestScenario = TestScenario;
    bIsRunningTest = true;
    CurrentTestStartTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    
    bool bSetupSuccess = false;
    
    switch (TestScenario)
    {
        case ERTSTestScenario::BasicMovement:
            bSetupSuccess = SetupBasicMovementTest();
            break;
        case ERTSTestScenario::FormationMovement:
            bSetupSuccess = SetupFormationMovementTest();
            break;
        case ERTSTestScenario::CombatEngagement:
            bSetupSuccess = SetupCombatEngagementTest();
            break;
        case ERTSTestScenario::ReturnFire:
            bSetupSuccess = SetupReturnFireTest();
            break;
        case ERTSTestScenario::CollisionAvoidance:
            bSetupSuccess = SetupCollisionAvoidanceTest();
            break;
        case ERTSTestScenario::CommandPriority:
            bSetupSuccess = SetupCommandPriorityTest();
            break;
        case ERTSTestScenario::StressTest:
            bSetupSuccess = SetupStressTest();
            break;
        default:
            bSetupSuccess = false;
            break;
    }
    
    if (!bSetupSuccess)
    {
        StopCurrentTest(false, TEXT("Test setup failed"));
        return false;
    }
    
    OnTestStarted.Broadcast(TestScenario);
    
    UE_LOG(LogTemp, Log, TEXT("RTSAITestingManager: Started test %s"), 
        *UEnum::GetValueAsString(TestScenario));
    
    return true;
}

void URTSAITestingManager::StopCurrentTest(bool bTestPassed, const FString& FailureReason)
{
    if (!bIsRunningTest)
    {
        return;
    }

    RecordTestResult(bTestPassed, FailureReason);

    bIsRunningTest = false;
    OnTestCompleted.Broadcast(CurrentTestScenario, bTestPassed);

    UE_LOG(LogTemp, Log, TEXT("RTSAITestingManager: Test %s completed - %s"),
        *UEnum::GetValueAsString(CurrentTestScenario),
        bTestPassed ? TEXT("PASSED") : TEXT("FAILED"));

    if (!bTestPassed && !FailureReason.IsEmpty())
    {
        UE_LOG(LogTemp, Warning, TEXT("RTSAITestingManager: Failure reason: %s"), *FailureReason);
    }
}

void URTSAITestingManager::RunAllTests()
{
    UE_LOG(LogTemp, Log, TEXT("RTSAITestingManager: Starting comprehensive test suite"));

    // Clear previous results
    TestResults.Empty();

    // Run all test scenarios
    TArray<ERTSTestScenario> TestScenarios = {
        ERTSTestScenario::BasicMovement,
        ERTSTestScenario::FormationMovement,
        ERTSTestScenario::CombatEngagement,
        ERTSTestScenario::ReturnFire,
        ERTSTestScenario::CollisionAvoidance,
        ERTSTestScenario::CommandPriority,
        ERTSTestScenario::StressTest
    };

    for (ERTSTestScenario Scenario : TestScenarios)
    {
        StartTest(Scenario);

        // Wait for test completion (in a real implementation, this would be handled differently)
        // For now, just log the intention
        UE_LOG(LogTemp, Log, TEXT("RTSAITestingManager: Would run test %s"),
            *UEnum::GetValueAsString(Scenario));
    }
}

bool URTSAITestingManager::RunBasicMovementTest()
{
    return StartTest(ERTSTestScenario::BasicMovement);
}

bool URTSAITestingManager::RunFormationMovementTest()
{
    return StartTest(ERTSTestScenario::FormationMovement);
}

bool URTSAITestingManager::RunCombatEngagementTest()
{
    return StartTest(ERTSTestScenario::CombatEngagement);
}

bool URTSAITestingManager::RunReturnFireTest()
{
    return StartTest(ERTSTestScenario::ReturnFire);
}

bool URTSAITestingManager::RunCollisionAvoidanceTest()
{
    return StartTest(ERTSTestScenario::CollisionAvoidance);
}

bool URTSAITestingManager::RunCommandPriorityTest()
{
    return StartTest(ERTSTestScenario::CommandPriority);
}

bool URTSAITestingManager::RunStressTest()
{
    return StartTest(ERTSTestScenario::StressTest);
}

TArray<ARTSUnit*> URTSAITestingManager::SpawnTestUnits(int32 Count, const FVector& SpawnLocation, float SpawnRadius)
{
    TArray<ARTSUnit*> SpawnedUnits;

    if (!GetWorld())
    {
        UE_LOG(LogTemp, Error, TEXT("RTSAITestingManager: No world context for spawning units"));
        return SpawnedUnits;
    }

    for (int32 i = 0; i < Count; i++)
    {
        // Calculate spawn position
        float Angle = (2.0f * PI * i) / Count;
        FVector Offset = FVector(FMath::Cos(Angle), FMath::Sin(Angle), 0.0f) * SpawnRadius;
        FVector SpawnPos = SpawnLocation + Offset;

        // Spawn unit (this would need a proper unit class reference)
        FActorSpawnParameters SpawnParams;
        SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

        // For now, just log the intention to spawn
        UE_LOG(LogTemp, Log, TEXT("RTSAITestingManager: Would spawn test unit %d at %s"),
            i + 1, *SpawnPos.ToString());

        // In a real implementation, you would spawn actual units here
        // ARTSUnit* SpawnedUnit = GetWorld()->SpawnActor<ARTSUnit>(UnitClass, SpawnPos, FRotator::ZeroRotator, SpawnParams);
        // if (SpawnedUnit)
        // {
        //     SpawnedUnits.Add(SpawnedUnit);
        //     TestUnits.Add(SpawnedUnit);
        // }
    }

    return SpawnedUnits;
}

void URTSAITestingManager::CleanupTestUnits()
{
    for (const TSoftObjectPtr<ARTSUnit>& UnitPtr : TestUnits)
    {
        if (ARTSUnit* Unit = UnitPtr.Get())
        {
            Unit->Destroy();
        }
    }

    TestUnits.Empty();

    UE_LOG(LogTemp, Log, TEXT("RTSAITestingManager: Cleaned up test units"));
}

bool URTSAITestingManager::ValidateTestEnvironment()
{
    if (!GetWorld())
    {
        UE_LOG(LogTemp, Error, TEXT("RTSAITestingManager: No world context"));
        return false;
    }

    // Check for required subsystems
    if (!GetWorld()->GetSubsystem<URTSFormationManager>())
    {
        UE_LOG(LogTemp, Error, TEXT("RTSAITestingManager: Formation Manager not available"));
        return false;
    }

    if (!GetWorld()->GetSubsystem<URTSTeamManager>())
    {
        UE_LOG(LogTemp, Error, TEXT("RTSAITestingManager: Team Manager not available"));
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("RTSAITestingManager: Test environment validation passed"));
    return true;
}

void URTSAITestingManager::StartPerformanceMonitoring()
{
    FrameTimeHistory.Empty();
    CurrentMetrics = FRTSPerformanceMetrics();

    UE_LOG(LogTemp, Log, TEXT("RTSAITestingManager: Started performance monitoring"));
}

void URTSAITestingManager::StopPerformanceMonitoring()
{
    UE_LOG(LogTemp, Log, TEXT("RTSAITestingManager: Stopped performance monitoring"));
    UE_LOG(LogTemp, Log, TEXT("RTSAITestingManager: Average frame time: %.3f ms"), CurrentMetrics.AverageFrameTime);
    UE_LOG(LogTemp, Log, TEXT("RTSAITestingManager: Max frame time: %.3f ms"), CurrentMetrics.MaxFrameTime);
}

void URTSAITestingManager::UpdatePerformanceMetrics(float DeltaTime)
{
    // Update frame time history
    float FrameTimeMs = DeltaTime * 1000.0f;
    FrameTimeHistory.Add(FrameTimeMs);

    if (FrameTimeHistory.Num() > MaxFrameHistorySize)
    {
        FrameTimeHistory.RemoveAt(0);
    }

    // Calculate metrics
    if (FrameTimeHistory.Num() > 0)
    {
        float TotalFrameTime = 0.0f;
        CurrentMetrics.MaxFrameTime = 0.0f;

        for (float FrameTime : FrameTimeHistory)
        {
            TotalFrameTime += FrameTime;
            CurrentMetrics.MaxFrameTime = FMath::Max(CurrentMetrics.MaxFrameTime, FrameTime);
        }

        CurrentMetrics.AverageFrameTime = TotalFrameTime / FrameTimeHistory.Num();
    }

    // Update other metrics
    CurrentMetrics.TotalUnitsTracked = TestUnits.Num();

    // Check for performance alerts
    if (CurrentMetrics.MaxFrameTime > 33.33f) // > 30 FPS
    {
        OnPerformanceAlert.Broadcast(FString::Printf(TEXT("High frame time detected: %.2f ms"), CurrentMetrics.MaxFrameTime));
    }
}

void URTSAITestingManager::GenerateTestReport()
{
    UE_LOG(LogTemp, Log, TEXT("=== RTS AI Test Report ==="));
    UE_LOG(LogTemp, Log, TEXT("Total Tests Run: %d"), TestResults.Num());

    int32 PassedTests = 0;
    int32 FailedTests = 0;

    for (const FRTSTestResult& Result : TestResults)
    {
        if (Result.bTestPassed)
        {
            PassedTests++;
        }
        else
        {
            FailedTests++;
        }

        UE_LOG(LogTemp, Log, TEXT("Test: %s - %s (%.2f seconds)"),
            *UEnum::GetValueAsString(Result.TestScenario),
            Result.bTestPassed ? TEXT("PASSED") : TEXT("FAILED"),
            Result.TestDuration);

        if (!Result.bTestPassed && !Result.FailureReason.IsEmpty())
        {
            UE_LOG(LogTemp, Log, TEXT("  Failure Reason: %s"), *Result.FailureReason);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("Passed: %d, Failed: %d"), PassedTests, FailedTests);
    UE_LOG(LogTemp, Log, TEXT("Success Rate: %.1f%%"), PassedTests > 0 ? (float)PassedTests / TestResults.Num() * 100.0f : 0.0f);
    UE_LOG(LogTemp, Log, TEXT("=== End Report ==="));
}

void URTSAITestingManager::ExportTestResults(const FString& FilePath)
{
    FString ReportContent = TEXT("RTS AI Test Results\n");
    ReportContent += FString::Printf(TEXT("Generated: %s\n\n"), *FDateTime::Now().ToString());

    for (const FRTSTestResult& Result : TestResults)
    {
        ReportContent += FString::Printf(TEXT("Test: %s\n"), *UEnum::GetValueAsString(Result.TestScenario));
        ReportContent += FString::Printf(TEXT("Result: %s\n"), Result.bTestPassed ? TEXT("PASSED") : TEXT("FAILED"));
        ReportContent += FString::Printf(TEXT("Duration: %.2f seconds\n"), Result.TestDuration);

        if (!Result.FailureReason.IsEmpty())
        {
            ReportContent += FString::Printf(TEXT("Failure Reason: %s\n"), *Result.FailureReason);
        }

        ReportContent += TEXT("\n");
    }

    FFileHelper::SaveStringToFile(ReportContent, *FilePath);
    UE_LOG(LogTemp, Log, TEXT("RTSAITestingManager: Test results exported to %s"), *FilePath);
}

bool URTSAITestingManager::ValidateAISystem()
{
    TArray<FString> ValidationErrors = GetSystemValidationErrors();

    if (ValidationErrors.Num() == 0)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSAITestingManager: AI System validation passed"));
        return true;
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("RTSAITestingManager: AI System validation failed with %d errors"), ValidationErrors.Num());
        for (const FString& Error : ValidationErrors)
        {
            UE_LOG(LogTemp, Error, TEXT("  - %s"), *Error);
        }
        return false;
    }
}

TArray<FString> URTSAITestingManager::GetSystemValidationErrors()
{
    TArray<FString> Errors;

    if (!GetWorld())
    {
        Errors.Add(TEXT("No world context available"));
        return Errors;
    }

    // Check subsystems
    if (!GetWorld()->GetSubsystem<URTSFormationManager>())
    {
        Errors.Add(TEXT("Formation Manager subsystem not found"));
    }

    if (!GetWorld()->GetSubsystem<URTSTeamManager>())
    {
        Errors.Add(TEXT("Team Manager subsystem not found"));
    }

    // Check for test units with AI components
    TArray<AActor*> FoundUnits;
    UGameplayStatics::GetAllActorsOfClass(GetWorld(), ARTSUnit::StaticClass(), FoundUnits);

    if (FoundUnits.Num() == 0)
    {
        Errors.Add(TEXT("No RTS Units found in the world"));
    }
    else
    {
        for (AActor* Actor : FoundUnits)
        {
            if (ARTSUnit* Unit = Cast<ARTSUnit>(Actor))
            {
                if (!Unit->GetCommandComponent())
                {
                    Errors.Add(FString::Printf(TEXT("Unit %s missing Command Component"), *Unit->GetName()));
                }

                if (!Unit->GetBlackboardComponent())
                {
                    Errors.Add(FString::Printf(TEXT("Unit %s missing Blackboard Component"), *Unit->GetName()));
                }
            }
        }
    }

    return Errors;
}

// Missing method implementations for URTSAITestingManager

void URTSAITestingManager::ProcessCurrentTest(float DeltaTime)
{
    if (!bIsRunningTest)
    {
        return;
    }

    CurrentTestTime += DeltaTime;

    // Check for test timeout
    if (CurrentTestTime > TestTimeoutDuration)
    {
        StopCurrentTest(false, TEXT("Test timeout"));
        return;
    }

    // Check if test is complete
    if (CheckTestCompletion())
    {
        StopCurrentTest(true, TEXT("Test completed successfully"));
    }
}

bool URTSAITestingManager::CheckTestCompletion()
{
    // Basic completion check - override in specific test implementations
    return CurrentTestTime > 5.0f; // Simple 5-second test duration
}

void URTSAITestingManager::RecordTestResult(bool bPassed, const FString& FailureReason)
{
    FRTSTestResult Result;
    Result.TestScenario = CurrentTestScenario;
    Result.bPassed = bPassed;
    Result.TestDuration = CurrentTestTime;
    Result.FailureReason = FailureReason;
    Result.Timestamp = FDateTime::Now();

    TestResults.Add(Result);

    UE_LOG(LogTemp, Log, TEXT("RTSAITestingManager: Test result recorded - %s: %s"),
        *UEnum::GetValueAsString(CurrentTestScenario),
        bPassed ? TEXT("PASSED") : *FString::Printf(TEXT("FAILED (%s)"), *FailureReason));
}

bool URTSAITestingManager::SetupBasicMovementTest()
{
    UE_LOG(LogTemp, Log, TEXT("RTSAITestingManager: Setting up basic movement test"));

    // Spawn test units
    TestUnits = SpawnTestUnits(3, FVector(0, 0, 100), 200.0f);

    if (TestUnits.Num() == 0)
    {
        UE_LOG(LogTemp, Error, TEXT("RTSAITestingManager: Failed to spawn test units"));
        return false;
    }

    // Set movement target
    FVector TargetLocation = FVector(1000, 0, 100);
    for (ARTSUnit* Unit : TestUnits)
    {
        if (Unit)
        {
            Unit->MoveToLocation(TargetLocation);
        }
    }

    return true;
}

bool URTSAITestingManager::SetupFormationMovementTest()
{
    UE_LOG(LogTemp, Log, TEXT("RTSAITestingManager: Setting up formation movement test"));

    // Spawn test units in formation
    TestUnits = SpawnTestUnits(5, FVector(0, 0, 100), 300.0f);

    if (TestUnits.Num() == 0)
    {
        return false;
    }

    // TODO: Set up formation - for now just move units together
    FVector TargetLocation = FVector(1500, 0, 100);
    for (ARTSUnit* Unit : TestUnits)
    {
        if (Unit)
        {
            Unit->MoveToLocation(TargetLocation);
        }
    }

    return true;
}

bool URTSAITestingManager::SetupCombatEngagementTest()
{
    UE_LOG(LogTemp, Log, TEXT("RTSAITestingManager: Setting up combat engagement test"));

    // Spawn friendly units
    TestUnits = SpawnTestUnits(3, FVector(0, 0, 100), 200.0f);

    // Spawn enemy units
    TArray<ARTSUnit*> EnemyUnits = SpawnTestUnits(2, FVector(1000, 0, 100), 150.0f);

    // Set enemy team
    for (ARTSUnit* Enemy : EnemyUnits)
    {
        if (Enemy)
        {
            Enemy->SetTeamID(1); // Different team
        }
    }

    return TestUnits.Num() > 0 && EnemyUnits.Num() > 0;
}

bool URTSAITestingManager::SetupReturnFireTest()
{
    UE_LOG(LogTemp, Log, TEXT("RTSAITestingManager: Setting up return fire test"));

    // Similar to combat test but focused on defensive behavior
    return SetupCombatEngagementTest();
}

bool URTSAITestingManager::SetupCollisionAvoidanceTest()
{
    UE_LOG(LogTemp, Log, TEXT("RTSAITestingManager: Setting up collision avoidance test"));

    // Spawn units close together
    TestUnits = SpawnTestUnits(8, FVector(0, 0, 100), 100.0f);

    if (TestUnits.Num() == 0)
    {
        return false;
    }

    // Make them all move to the same target to test collision avoidance
    FVector TargetLocation = FVector(1000, 1000, 100);
    for (ARTSUnit* Unit : TestUnits)
    {
        if (Unit)
        {
            Unit->MoveToLocation(TargetLocation);
        }
    }

    return true;
}

bool URTSAITestingManager::SetupCommandPriorityTest()
{
    UE_LOG(LogTemp, Log, TEXT("RTSAITestingManager: Setting up command priority test"));

    TestUnits = SpawnTestUnits(2, FVector(0, 0, 100), 100.0f);

    // TODO: Issue commands with different priorities
    return TestUnits.Num() > 0;
}

bool URTSAITestingManager::SetupStressTest()
{
    UE_LOG(LogTemp, Log, TEXT("RTSAITestingManager: Setting up stress test"));

    // Spawn many units for stress testing
    TestUnits = SpawnTestUnits(50, FVector(0, 0, 100), 1000.0f);

    return TestUnits.Num() > 0;
}

bool URTSAITestingManager::ValidateUnitsReachedDestination(const TArray<ARTSUnit*>& Units, const FVector& TargetLocation, float Tolerance)
{
    if (Units.Num() == 0)
    {
        return false;
    }

    int32 UnitsAtDestination = 0;
    for (ARTSUnit* Unit : Units)
    {
        if (Unit && Unit->IsAlive())
        {
            float Distance = FVector::Dist(Unit->GetActorLocation(), TargetLocation);
            if (Distance <= Tolerance)
            {
                UnitsAtDestination++;
            }
        }
    }

    // Consider test passed if at least 80% of units reached destination
    float SuccessRate = static_cast<float>(UnitsAtDestination) / Units.Num();
    return SuccessRate >= 0.8f;
}

bool URTSAITestingManager::ValidateFormationIntegrity(const TArray<ARTSUnit*>& Units)
{
    if (Units.Num() < 2)
    {
        return true; // Single unit formations are always valid
    }

    // Check if units are maintaining reasonable spacing
    float MaxAllowedSpacing = 500.0f;
    float MinAllowedSpacing = 50.0f;

    for (int32 i = 0; i < Units.Num(); i++)
    {
        for (int32 j = i + 1; j < Units.Num(); j++)
        {
            if (Units[i] && Units[j])
            {
                float Distance = FVector::Dist(Units[i]->GetActorLocation(), Units[j]->GetActorLocation());
                if (Distance > MaxAllowedSpacing || Distance < MinAllowedSpacing)
                {
                    return false;
                }
            }
        }
    }

    return true;
}

bool URTSAITestingManager::ValidateCombatBehavior(const TArray<ARTSUnit*>& Units)
{
    if (Units.Num() == 0)
    {
        return false;
    }

    // Check if units are engaging enemies appropriately
    int32 UnitsInCombat = 0;
    for (ARTSUnit* Unit : Units)
    {
        if (Unit && Unit->IsAlive())
        {
            // Check if unit is attacking or has a target
            if (Unit->IsAttacking())
            {
                UnitsInCombat++;
            }
        }
    }

    // At least some units should be in combat if enemies are present
    return UnitsInCombat > 0;
}

bool URTSAITestingManager::ValidatePerformanceThresholds()
{
    // Check frame rate and performance metrics
    float CurrentFrameRate = 1.0f / GetWorld()->GetDeltaSeconds();

    // Consider performance acceptable if frame rate is above 30 FPS
    if (CurrentFrameRate < 30.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("RTSAITestingManager: Performance below threshold - FPS: %.2f"), CurrentFrameRate);
        return false;
    }

    // Check memory usage if needed
    // TODO: Add memory usage validation

    return true;
}
